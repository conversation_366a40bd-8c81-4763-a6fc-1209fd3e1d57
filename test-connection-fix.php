<?php
/**
 * Test the connection fix for executeQuery function
 */
require_once 'config/db_config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Connection Fix Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Connection Fix Test</h2>";

// Test 1: Basic Connection Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: Basic Database Connection</h5>";
try {
    $conn1 = getConnection();
    if ($conn1) {
        echo "✅ Connection 1: Success<br>";
        $conn1->close();
        echo "✅ Connection 1: Closed successfully<br>";
    } else {
        echo "❌ Connection 1: Failed<br>";
    }
    
    $conn2 = getConnection();
    if ($conn2) {
        echo "✅ Connection 2: Success (after closing connection 1)<br>";
        $conn2->close();
        echo "✅ Connection 2: Closed successfully<br>";
    } else {
        echo "❌ Connection 2: Failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Connection test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 2: executeQuery SELECT Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: executeQuery SELECT Test</h5>";
try {
    $result1 = executeQuery("SELECT COUNT(*) as count FROM users");
    if ($result1) {
        $count = $result1->fetch_assoc()['count'];
        echo "✅ First SELECT query: Found $count users<br>";
    } else {
        echo "❌ First SELECT query failed<br>";
    }
    
    $result2 = executeQuery("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
    if ($result2) {
        $activeCount = $result2->fetch_assoc()['count'];
        echo "✅ Second SELECT query: Found $activeCount active users<br>";
    } else {
        echo "❌ Second SELECT query failed<br>";
    }
    
    $result3 = executeQuery("SELECT id, full_name, email FROM users ORDER BY created_at DESC LIMIT 3");
    if ($result3) {
        $userCount = $result3->num_rows;
        echo "✅ Third SELECT query: Retrieved $userCount recent users<br>";
    } else {
        echo "❌ Third SELECT query failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ SELECT query test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: executeQuery INSERT/DELETE Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: executeQuery INSERT/DELETE Test</h5>";
try {
    // Test INSERT
    $testEmail = 'test_connection_' . time() . '@example.com';
    $insertResult = executeQuery(
        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
        'ssssss',
        ['Test Connection User', $testEmail, password_hash('test123', PASSWORD_DEFAULT), 'Test Ward', 'Test LGA', 'Test State']
    );
    
    if ($insertResult && $insertResult->success && $insertResult->insert_id > 0) {
        $testUserId = $insertResult->insert_id;
        echo "✅ INSERT query: Created user with ID $testUserId<br>";
        
        // Test another SELECT after INSERT
        $selectResult = executeQuery("SELECT full_name FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($selectResult && $selectResult->num_rows > 0) {
            $userData = $selectResult->fetch_assoc();
            echo "✅ SELECT after INSERT: Found user '{$userData['full_name']}'<br>";
        } else {
            echo "❌ SELECT after INSERT failed<br>";
        }
        
        // Test DELETE
        $deleteResult = executeQuery("DELETE FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($deleteResult && $deleteResult->success && $deleteResult->affected_rows > 0) {
            echo "✅ DELETE query: Removed test user (affected {$deleteResult->affected_rows} rows)<br>";
        } else {
            echo "❌ DELETE query failed<br>";
        }
        
        // Test SELECT after DELETE
        $verifyResult = executeQuery("SELECT COUNT(*) as count FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($verifyResult) {
            $remainingCount = $verifyResult->fetch_assoc()['count'];
            if ($remainingCount == 0) {
                echo "✅ Verification: Test user successfully deleted<br>";
            } else {
                echo "⚠️ Verification: Test user may still exist<br>";
            }
        } else {
            echo "❌ Verification query failed<br>";
        }
        
    } else {
        echo "❌ INSERT query failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ INSERT/DELETE test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 4: Multiple Rapid Queries
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: Multiple Rapid Queries (Connection Stress Test)</h5>";
try {
    $successCount = 0;
    $totalQueries = 10;
    
    for ($i = 1; $i <= $totalQueries; $i++) {
        $result = executeQuery("SELECT $i as query_number, COUNT(*) as user_count FROM users");
        if ($result) {
            $data = $result->fetch_assoc();
            $successCount++;
        }
    }
    
    echo "✅ Rapid query test: $successCount/$totalQueries queries successful<br>";
    
    if ($successCount == $totalQueries) {
        echo "✅ All rapid queries completed successfully - no connection issues!<br>";
    } else {
        echo "⚠️ Some rapid queries failed - may indicate connection issues<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Rapid query test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 5: Error Handling Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 5: Error Handling Test</h5>";
try {
    // Test with invalid table name
    $result = executeQuery("SELECT * FROM non_existent_table");
    if ($result === false) {
        echo "✅ Error handling: Invalid query properly handled<br>";
    } else {
        echo "⚠️ Error handling: Invalid query should have failed<br>";
    }
    
    // Test with invalid SQL syntax
    $result2 = executeQuery("INVALID SQL SYNTAX");
    if ($result2 === false) {
        echo "✅ Error handling: Invalid syntax properly handled<br>";
    } else {
        echo "⚠️ Error handling: Invalid syntax should have failed<br>";
    }
    
} catch (Exception $e) {
    echo "✅ Error handling: Exception properly caught - " . $e->getMessage() . "<br>";
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='test-registration-flow.php' class='btn btn-success btn-lg me-2'>
                            <i class='fas fa-play me-2'></i>Test Registration Flow
                        </a>
                        <a href='test-registration-simple.php' class='btn btn-primary btn-lg me-2'>
                            <i class='fas fa-user-plus me-2'></i>Test Simple Registration
                        </a>
                        <a href='register.php' class='btn btn-warning btn-lg'>
                            <i class='fas fa-user-plus me-2'></i>Real Registration
                        </a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>What This Test Checks:</h6>
                        <ul class='small'>
                            <li>✅ Basic database connection management</li>
                            <li>✅ executeQuery function with SELECT queries</li>
                            <li>✅ executeQuery function with INSERT/DELETE queries</li>
                            <li>✅ Multiple rapid queries (connection stress test)</li>
                            <li>✅ Error handling for invalid queries</li>
                            <li>✅ No 'mysqli object is already closed' errors</li>
                        </ul>
                        
                        <div class='alert alert-success mt-3'>
                            <strong>Expected Result:</strong> All tests should pass with green checkmarks. 
                            If you see any red X marks, there are still connection issues to fix.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
