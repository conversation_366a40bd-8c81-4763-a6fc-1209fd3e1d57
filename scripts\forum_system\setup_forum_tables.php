<?php
/**
 * Forum System Database Setup
 * Creates tables for forum posts, replies, likes, and interactions
 */

require_once '../../config/db_config.php';

$conn = getConnection();

if (!$conn) {
    die("Database connection failed");
}

try {
    // Create forum_posts table
    $createPostsTable = "
    CREATE TABLE IF NOT EXISTS `forum_posts` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `title` varchar(255) NOT NULL,
        `content` text NOT NULL,
        `category` varchar(50) NOT NULL DEFAULT 'general',
        `like_count` int NOT NULL DEFAULT 0,
        `reply_count` int NOT NULL DEFAULT 0,
        `view_count` int NOT NULL DEFAULT 0,
        `is_pinned` tinyint(1) NOT NULL DEFAULT 0,
        `is_locked` tinyint(1) NOT NULL DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        <PERSON><PERSON>Y `user_id` (`user_id`),
        KEY `category` (`category`),
        KEY `created_at` (`created_at`),
        KEY `like_count` (`like_count`),
        KEY `reply_count` (`reply_count`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($createPostsTable)) {
        echo "✅ forum_posts table created successfully\n";
    } else {
        echo "❌ Error creating forum_posts table: " . $conn->error . "\n";
    }

    // Create forum_replies table
    $createRepliesTable = "
    CREATE TABLE IF NOT EXISTS `forum_replies` (
        `id` int NOT NULL AUTO_INCREMENT,
        `post_id` int NOT NULL,
        `user_id` int NOT NULL,
        `content` text NOT NULL,
        `like_count` int NOT NULL DEFAULT 0,
        `is_best_answer` tinyint(1) NOT NULL DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `post_id` (`post_id`),
        KEY `user_id` (`user_id`),
        KEY `created_at` (`created_at`),
        KEY `like_count` (`like_count`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($createRepliesTable)) {
        echo "✅ forum_replies table created successfully\n";
    } else {
        echo "❌ Error creating forum_replies table: " . $conn->error . "\n";
    }

    // Create forum_likes table
    $createLikesTable = "
    CREATE TABLE IF NOT EXISTS `forum_likes` (
        `id` int NOT NULL AUTO_INCREMENT,
        `post_id` int NULL,
        `reply_id` int NULL,
        `user_id` int NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_like` (`post_id`, `reply_id`, `user_id`),
        KEY `post_id` (`post_id`),
        KEY `reply_id` (`reply_id`),
        KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($createLikesTable)) {
        echo "✅ forum_likes table created successfully\n";
    } else {
        echo "❌ Error creating forum_likes table: " . $conn->error . "\n";
    }

    // Create forum_categories table
    $createCategoriesTable = "
    CREATE TABLE IF NOT EXISTS `forum_categories` (
        `id` int NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `slug` varchar(100) NOT NULL,
        `description` text,
        `color` varchar(7) DEFAULT '#1a5f3f',
        `icon` varchar(50) DEFAULT 'fas fa-folder',
        `post_count` int NOT NULL DEFAULT 0,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($createCategoriesTable)) {
        echo "✅ forum_categories table created successfully\n";
    } else {
        echo "❌ Error creating forum_categories table: " . $conn->error . "\n";
    }

    // Insert default categories
    $defaultCategories = [
        ['General Discussion', 'general', 'General discussions about Qur\'an recitation and Islamic topics', '#1a5f3f', 'fas fa-comments'],
        ['Recitation Tips', 'recitation', 'Tips and advice for improving Qur\'an recitation', '#28a745', 'fas fa-microphone'],
        ['Tajweed Rules', 'tajweed', 'Discussion about Tajweed rules and pronunciation', '#17a2b8', 'fas fa-book-open'],
        ['Memorization', 'memorization', 'Tips and techniques for memorizing the Qur\'an', '#ffc107', 'fas fa-brain'],
        ['Questions & Help', 'questions', 'Ask questions and get help from the community', '#dc3545', 'fas fa-question-circle'],
        ['Announcements', 'announcements', 'Important announcements and updates', '#6f42c1', 'fas fa-bullhorn']
    ];

    foreach ($defaultCategories as $category) {
        $stmt = $conn->prepare("INSERT IGNORE INTO forum_categories (name, slug, description, color, icon) VALUES (?, ?, ?, ?, ?)");
        $stmt->bind_param("sssss", $category[0], $category[1], $category[2], $category[3], $category[4]);
        
        if ($stmt->execute()) {
            echo "✅ Category '{$category[0]}' added\n";
        } else {
            echo "❌ Error adding category '{$category[0]}': " . $stmt->error . "\n";
        }
        $stmt->close();
    }

    echo "\n🎉 Forum system database setup completed successfully!\n";
    echo "Tables created:\n";
    echo "- forum_posts (for main forum posts)\n";
    echo "- forum_replies (for replies to posts)\n";
    echo "- forum_likes (for post and reply likes)\n";
    echo "- forum_categories (for post categories)\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 