<?php
/**
 * Database Connection Test Script
 * Tests if the database configuration is working properly
 */

require_once 'config/db_config.php';

echo "<h2>Database Connection Test</h2>";

try {
    // Test database connection
    $conn = getConnection();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test a simple query
    $result = executeQuery("SELECT 1 as test");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Database query successful!</p>";
    } else {
        echo "<p style='color: red;'>❌ Database query failed!</p>";
    }
    
    // Show current configuration
    echo "<h3>Current Configuration:</h3>";
    echo "<ul>";
    echo "<li><strong>DB_HOST:</strong> " . DB_HOST . "</li>";
    echo "<li><strong>DB_NAME:</strong> " . DB_NAME . "</li>";
    echo "<li><strong>DB_USER:</strong> " . DB_USER . "</li>";
    echo "<li><strong>DEVELOPMENT_MODE:</strong> " . (DEVELOPMENT_MODE ? 'true' : 'false') . "</li>";
    echo "<li><strong>BASE_URL:</strong> " . BASE_URL . "</li>";
    echo "<li><strong>PAYSTACK_PUBLIC_KEY:</strong> " . substr(PAYSTACK_PUBLIC_KEY, 0, 20) . "...</li>";
    echo "</ul>";
    
    // Test if users table exists
    $usersResult = executeQuery("SHOW TABLES LIKE 'users'");
    if ($usersResult && $usersResult->num_rows > 0) {
        echo "<p style='color: green;'>✅ Users table exists!</p>";
        
        // Count users
        $userCount = executeQuery("SELECT COUNT(*) as count FROM users");
        if ($userCount && $userCount->num_rows > 0) {
            $count = $userCount->fetch_assoc()['count'];
            echo "<p>📊 Total users in database: $count</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Users table not found!</p>";
    }
    
    // Test if transactions table exists
    $transactionsResult = executeQuery("SHOW TABLES LIKE 'transactions'");
    if ($transactionsResult && $transactionsResult->num_rows > 0) {
        echo "<p style='color: green;'>✅ Transactions table exists!</p>";
    } else {
        echo "<p style='color: red;'>❌ Transactions table not found!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>Environment:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'CLI') . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
?> 