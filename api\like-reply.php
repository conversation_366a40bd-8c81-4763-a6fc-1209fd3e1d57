<?php
/**
 * API Endpoint to like/unlike forum replies
 */

require_once '../config/db_config.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get reply ID from request
$replyId = intval($_POST['reply_id'] ?? 0);

if ($replyId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid reply ID']);
    exit;
}

try {
    $conn = getConnection();
    
    // Check if already liked
    $checkStmt = $conn->prepare("SELECT id FROM forum_likes WHERE reply_id = ? AND user_id = ?");
    $checkStmt->bind_param("ii", $replyId, $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows == 0) {
        // Add like
        $likeStmt = $conn->prepare("INSERT INTO forum_likes (reply_id, user_id, created_at) VALUES (?, ?, NOW())");
        $likeStmt->bind_param("ii", $replyId, $userId);
        $likeStmt->execute();
        $likeStmt->close();
        
        $action = 'liked';
        $message = 'Reply liked!';
    } else {
        // Remove like
        $unlikeStmt = $conn->prepare("DELETE FROM forum_likes WHERE reply_id = ? AND user_id = ?");
        $unlikeStmt->bind_param("ii", $replyId, $userId);
        $unlikeStmt->execute();
        $unlikeStmt->close();
        
        $action = 'unliked';
        $message = 'Reply unliked!';
    }
    
    $checkStmt->close();
    
    // Get updated like count
    $countStmt = $conn->prepare("SELECT COUNT(*) as like_count FROM forum_likes WHERE reply_id = ?");
    $countStmt->bind_param("i", $replyId);
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $likeCount = $countResult->fetch_assoc()['like_count'];
    $countStmt->close();
    
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'action' => $action,
        'message' => $message,
        'like_count' => $likeCount
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?> 