<?php
$page_title = 'Manage Content';
require_once __DIR__ . '/../components/admin_header.php';

// Check admin authentication  
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$conn = getConnection();

// Handle content actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $contentId = intval($_POST['content_id'] ?? 0);
    $contentType = $_POST['content_type'] ?? 'screen'; // 'screen' or 'mirror'
    
    if (validateCSRFToken($_POST['csrf_token']) && $contentId > 0) {
        switch ($action) {
            case 'hide':
                $table = $contentType === 'mirror' ? 'mirror_recordings' : 'screen_records';
                $stmt = $conn->prepare("UPDATE $table SET is_public = 0 WHERE id = ?");
                $stmt->bind_param("i", $contentId);
                if ($stmt->execute()) {
                    $_SESSION['success_message'] = 'Content hidden successfully';
                }
                break;
                
            case 'show':
                $table = $contentType === 'mirror' ? 'mirror_recordings' : 'screen_records';
                $stmt = $conn->prepare("UPDATE $table SET is_public = 1 WHERE id = ?");
                $stmt->bind_param("i", $contentId);
                if ($stmt->execute()) {
                    $_SESSION['success_message'] = 'Content restored successfully';
                }
                break;
                
            case 'delete':
                $table = $contentType === 'mirror' ? 'mirror_recordings' : 'screen_records';
                
                // Get file path before deletion
                $stmt = $conn->prepare("SELECT file_path, thumbnail_path FROM $table WHERE id = ?");
                $stmt->bind_param("i", $contentId);
                $stmt->execute();
                $result = $stmt->get_result();
                if ($row = $result->fetch_assoc()) {
                    // Delete from database
                    $deleteStmt = $conn->prepare("DELETE FROM $table WHERE id = ?");
                    $deleteStmt->bind_param("i", $contentId);
                    if ($deleteStmt->execute()) {
                        // Delete files
                        if (file_exists('../' . $row['file_path'])) {
                            unlink('../' . $row['file_path']);
                        }
                        if ($row['thumbnail_path'] && file_exists('../' . $row['thumbnail_path'])) {
                            unlink('../' . $row['thumbnail_path']);
                        }
                        $_SESSION['success_message'] = 'Content deleted permanently';
                    }
                    $deleteStmt->close();
                }
                break;
                
            case 'ban_user':
                $userId = intval($_POST['user_id'] ?? 0);
                if ($userId > 0) {
                    $stmt = $conn->prepare("UPDATE users SET is_banned = 1 WHERE id = ?");
                    $stmt->bind_param("i", $userId);
                    if ($stmt->execute()) {
                        $_SESSION['success_message'] = 'User banned successfully';
                    }
                }
                break;
        }
    }
    
    header('Location: manage-content.php');
    exit;
}

// Get filter parameters
$contentType = $_GET['type'] ?? 'all'; // 'all', 'screen', 'mirror'
$status = $_GET['status'] ?? 'all'; // 'all', 'public', 'hidden', 'reported'
$search = sanitize($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

// Build query for content
$allContent = [];

// Get screen recordings
if ($contentType === 'all' || $contentType === 'screen') {
    $whereClause = "1=1";
    if ($status === 'public') $whereClause .= " AND sr.is_public = 1";
    if ($status === 'hidden') $whereClause .= " AND sr.is_public = 0";
    if ($status === 'reported') $whereClause .= " AND sr.is_reported = 1";
    if (!empty($search)) $whereClause .= " AND (sr.title LIKE '%$search%' OR u.full_name LIKE '%$search%')";
    
    $query = "
        SELECT sr.*, u.full_name, u.email, u.profile_picture, 'screen' as content_type,
               0 as view_count,
               0 as like_count,
               0 as comment_count
        FROM screen_records sr
        JOIN users u ON sr.user_id = u.id
        WHERE $whereClause
        ORDER BY sr.created_at DESC
        LIMIT $perPage OFFSET $offset
    ";
    
    $result = $conn->query($query);
    while ($row = $result->fetch_assoc()) {
        $allContent[] = $row;
    }
}

// Get mirror recordings
if ($contentType === 'all' || $contentType === 'mirror') {
    $whereClause = "1=1";
    if ($status === 'public') $whereClause .= " AND mr.is_public = 1";
    if ($status === 'hidden') $whereClause .= " AND mr.is_public = 0";
    if ($status === 'reported') $whereClause .= " AND mr.is_reported = 1";
    if (!empty($search)) $whereClause .= " AND (mr.title LIKE '%$search%' OR u.full_name LIKE '%$search%')";
    
    $query = "
        SELECT mr.*, u.full_name, u.email, u.profile_picture, 'mirror' as content_type,
               0 as view_count,
               0 as like_count,
               0 as comment_count
        FROM mirror_recordings mr
        JOIN users u ON mr.user_id = u.id
        WHERE $whereClause
        ORDER BY mr.created_at DESC
        LIMIT $perPage OFFSET $offset
    ";
    
    $result = $conn->query($query);
    while ($row = $result->fetch_assoc()) {
        $allContent[] = $row;
    }
}

// Sort by creation date
usort($allContent, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Get total count for pagination
$totalContent = count($allContent);

// Get platform statistics
$stats = [];

// Screen recordings stats
$result = $conn->query("SELECT COUNT(*) as total_screen, SUM(views_count) as total_screen_views FROM screen_records");
$screenStats = $result->fetch_assoc();

// Mirror recordings stats
$result = $conn->query("SELECT COUNT(*) as total_mirror, SUM(views_count) as total_mirror_views FROM mirror_recordings");
$mirrorStats = $result->fetch_assoc();

// Reported content
$result = $conn->query("SELECT COUNT(*) as reported_screen FROM screen_records WHERE is_reported = 1");
$reportedScreen = $result->fetch_assoc()['reported_screen'];

$result = $conn->query("SELECT COUNT(*) as reported_mirror FROM mirror_recordings WHERE is_reported = 1");
$reportedMirror = $result->fetch_assoc()['reported_mirror'];

$stats = [
    'total_screen' => $screenStats['total_screen'],
    'total_mirror' => $mirrorStats['total_mirror'],
    'total_screen_views' => $screenStats['total_screen_views'],
    'total_mirror_views' => $mirrorStats['total_mirror_views'],
    'reported_content' => $reportedScreen + $reportedMirror
];

$conn->close();
?>

<style>
    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
   
    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }

    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } }
</style>

<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">Site Content</h4>
        <a href="#" class="btn btn-primary"><i class="fas fa-plus me-2"></i>Add New Content</a>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Type</th>
                    <th>Last Updated</th>
                    <th>Status</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($allContent)): ?>
                    <tr><td colspan="5" class="text-center py-5">No content found.</td></tr>
                <?php else: ?>
                    <?php foreach ($allContent as $content): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($content['title']); ?></td>
                        <td><?php echo htmlspecialchars(ucfirst($content['content_type'])); ?></td>
                        <td><?php echo date('M d, Y', strtotime($content['created_at'])); ?></td>
                        <td>
                            <span class="badge badge-<?php echo $content['is_public'] ? 'success' : 'secondary'; ?>">
                                <?php echo $content['is_public'] ? 'Published' : 'Draft'; ?>
                            </span>
                        </td>
                        <td class="text-end">
                            <a href="#" class="btn btn-sm btn-outline">Edit</a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>

<!-- Hidden form for actions -->
<form id="actionForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="content_id" id="contentId">
    <input type="hidden" name="content_type" id="contentType">
    <input type="hidden" name="user_id" id="userId">
</form>

<script>
    function toggleContent(contentId, contentType, action) {
        if (confirm(`Are you sure you want to ${action} this content?`)) {
            document.getElementById('actionType').value = action;
            document.getElementById('contentId').value = contentId;
            document.getElementById('contentType').value = contentType;
            document.getElementById('actionForm').submit();
        }
    }

    function deleteContent(contentId, contentType) {
        if (confirm('Are you sure you want to permanently delete this content? This action cannot be undone.')) {
            document.getElementById('actionType').value = 'delete';
            document.getElementById('contentId').value = contentId;
            document.getElementById('contentType').value = contentType;
            document.getElementById('actionForm').submit();
        }
    }

    function banUser(userId) {
        if (confirm('Are you sure you want to ban this user? They will not be able to access the platform.')) {
            document.getElementById('actionType').value = 'ban_user';
            document.getElementById('userId').value = userId;
            document.getElementById('actionForm').submit();
        }
    }
</script> 