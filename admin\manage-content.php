<?php
// Start output buffering to prevent header issues
ob_start();

$page_title = 'Manage Content';
require_once __DIR__ . '/../components/admin_header.php';

// Check admin authentication
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

try {
    $conn = getConnection();
} catch (Exception $e) {
    $_SESSION['error_message'] = 'Database connection failed: ' . $e->getMessage();
    header('Location: dashboard.php');
    exit;
}

// Handle content actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $contentId = intval($_POST['content_id'] ?? 0);
    $contentType = $_POST['content_type'] ?? 'screen'; // 'screen' or 'mirror'
    
    if (validateCSRFToken($_POST['csrf_token']) && $contentId > 0) {
        switch ($action) {
            case 'hide':
                if ($contentType === 'mirror') {
                    // Get content info for logging
                    $infoStmt = $conn->prepare("
                        SELECT c.title, c.user_id, u.full_name, u.email
                        FROM mirror_recordings c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $infoStmt->bind_param("i", $contentId);
                    $infoStmt->execute();
                    $infoResult = $infoStmt->get_result();

                    if ($contentInfo = $infoResult->fetch_assoc()) {
                        $stmt = $conn->prepare("UPDATE mirror_recordings SET is_public = 0 WHERE id = ?");
                        $stmt->bind_param("i", $contentId);
                        if ($stmt->execute()) {
                            $contentTitle = $contentInfo['title'] ?: 'Untitled';
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_HIDE', $contentInfo['user_id'],
                                         "Hidden mirror content: '{$contentTitle}' by {$contentInfo['full_name']}");
                            $_SESSION['success_message'] = "Content '{$contentTitle}' has been hidden from public view";
                        } else {
                            $_SESSION['error_message'] = 'Failed to hide content. Please try again.';
                        }
                        $stmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $infoStmt->close();
                } else {
                    // Screen recordings
                    $infoStmt = $conn->prepare("
                        SELECT c.title, c.user_id, u.full_name, u.email
                        FROM screen_records c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $infoStmt->bind_param("i", $contentId);
                    $infoStmt->execute();
                    $infoResult = $infoStmt->get_result();

                    if ($contentInfo = $infoResult->fetch_assoc()) {
                        $stmt = $conn->prepare("UPDATE screen_records SET is_public = 0 WHERE id = ?");
                        $stmt->bind_param("i", $contentId);
                        if ($stmt->execute()) {
                            $contentTitle = $contentInfo['title'] ?: 'Untitled';
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_HIDE', $contentInfo['user_id'],
                                         "Hidden screen content: '{$contentTitle}' by {$contentInfo['full_name']}");
                            $_SESSION['success_message'] = "Content '{$contentTitle}' has been hidden from public view";
                        } else {
                            $_SESSION['error_message'] = 'Failed to hide content. Please try again.';
                        }
                        $stmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $infoStmt->close();
                }
                break;

            case 'show':
                if ($contentType === 'mirror') {
                    // Get content info for logging
                    $infoStmt = $conn->prepare("
                        SELECT c.title, c.user_id, u.full_name, u.email
                        FROM mirror_recordings c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $infoStmt->bind_param("i", $contentId);
                    $infoStmt->execute();
                    $infoResult = $infoStmt->get_result();

                    if ($contentInfo = $infoResult->fetch_assoc()) {
                        $stmt = $conn->prepare("UPDATE mirror_recordings SET is_public = 1 WHERE id = ?");
                        $stmt->bind_param("i", $contentId);
                        if ($stmt->execute()) {
                            $contentTitle = $contentInfo['title'] ?: 'Untitled';
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_SHOW', $contentInfo['user_id'],
                                         "Restored mirror content: '{$contentTitle}' by {$contentInfo['full_name']}");
                            $_SESSION['success_message'] = "Content '{$contentTitle}' is now visible to the public";
                        } else {
                            $_SESSION['error_message'] = 'Failed to show content. Please try again.';
                        }
                        $stmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $infoStmt->close();
                } else {
                    // Screen recordings
                    $infoStmt = $conn->prepare("
                        SELECT c.title, c.user_id, u.full_name, u.email
                        FROM screen_records c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $infoStmt->bind_param("i", $contentId);
                    $infoStmt->execute();
                    $infoResult = $infoStmt->get_result();

                    if ($contentInfo = $infoResult->fetch_assoc()) {
                        $stmt = $conn->prepare("UPDATE screen_records SET is_public = 1 WHERE id = ?");
                        $stmt->bind_param("i", $contentId);
                        if ($stmt->execute()) {
                            $contentTitle = $contentInfo['title'] ?: 'Untitled';
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_SHOW', $contentInfo['user_id'],
                                         "Restored screen content: '{$contentTitle}' by {$contentInfo['full_name']}");
                            $_SESSION['success_message'] = "Content '{$contentTitle}' is now visible to the public";
                        } else {
                            $_SESSION['error_message'] = 'Failed to show content. Please try again.';
                        }
                        $stmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $infoStmt->close();
                }
                break;
                
            case 'delete':
                if ($contentType === 'mirror') {
                    // Get content and user info before deletion
                    $stmt = $conn->prepare("
                        SELECT c.file_path, c.title, c.user_id, u.full_name, u.email
                        FROM mirror_recordings c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $stmt->bind_param("i", $contentId);
                    $stmt->execute();
                    $result = $stmt->get_result();

                    if ($row = $result->fetch_assoc()) {
                        // Delete related records first (comments, likes, etc.)
                        $cleanupStmt1 = $conn->prepare("DELETE FROM comments WHERE mirror_recording_id = ?");
                        $cleanupStmt1->bind_param("i", $contentId);
                        $cleanupStmt1->execute();
                        $cleanupStmt1->close();

                        $cleanupStmt2 = $conn->prepare("DELETE FROM likes WHERE mirror_recording_id = ?");
                        $cleanupStmt2->bind_param("i", $contentId);
                        $cleanupStmt2->execute();
                        $cleanupStmt2->close();

                        // Delete from main table
                        $deleteStmt = $conn->prepare("DELETE FROM mirror_recordings WHERE id = ?");
                        $deleteStmt->bind_param("i", $contentId);

                        if ($deleteStmt->execute()) {
                            // Delete physical files
                            $filesDeleted = [];
                            if ($row['file_path'] && file_exists('../' . $row['file_path'])) {
                                if (unlink('../' . $row['file_path'])) {
                                    $filesDeleted[] = 'main file';
                                }
                            }

                            // Log admin action
                            $contentTitle = $row['title'] ?: 'Untitled';
                            $logDetails = "Deleted mirror content: '{$contentTitle}' by {$row['full_name']} ({$row['email']})";
                            if (!empty($filesDeleted)) {
                                $logDetails .= ". Files deleted: " . implode(', ', $filesDeleted);
                            }

                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_DELETE', $row['user_id'], $logDetails);
                            $_SESSION['success_message'] = "Content '{$contentTitle}' has been permanently deleted";
                        } else {
                            $_SESSION['error_message'] = 'Failed to delete content from database. Please try again.';
                        }
                        $deleteStmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $stmt->close();
                } else {
                    // Screen recordings
                    $stmt = $conn->prepare("
                        SELECT c.file_path, c.title, c.user_id, u.full_name, u.email
                        FROM screen_records c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.id = ?
                    ");
                    $stmt->bind_param("i", $contentId);
                    $stmt->execute();
                    $result = $stmt->get_result();

                    if ($row = $result->fetch_assoc()) {
                        // Delete related records first (comments, likes, etc.)
                        $cleanupStmt1 = $conn->prepare("DELETE FROM comments WHERE screen_record_id = ?");
                        $cleanupStmt1->bind_param("i", $contentId);
                        $cleanupStmt1->execute();
                        $cleanupStmt1->close();

                        $cleanupStmt2 = $conn->prepare("DELETE FROM likes WHERE screen_record_id = ?");
                        $cleanupStmt2->bind_param("i", $contentId);
                        $cleanupStmt2->execute();
                        $cleanupStmt2->close();

                        // Delete from main table
                        $deleteStmt = $conn->prepare("DELETE FROM screen_records WHERE id = ?");
                        $deleteStmt->bind_param("i", $contentId);

                        if ($deleteStmt->execute()) {
                            // Delete physical files
                            $filesDeleted = [];
                            if ($row['file_path'] && file_exists('../' . $row['file_path'])) {
                                if (unlink('../' . $row['file_path'])) {
                                    $filesDeleted[] = 'main file';
                                }
                            }

                            // Log admin action
                            $contentTitle = $row['title'] ?: 'Untitled';
                            $logDetails = "Deleted screen content: '{$contentTitle}' by {$row['full_name']} ({$row['email']})";
                            if (!empty($filesDeleted)) {
                                $logDetails .= ". Files deleted: " . implode(', ', $filesDeleted);
                            }

                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CONTENT_DELETE', $row['user_id'], $logDetails);
                            $_SESSION['success_message'] = "Content '{$contentTitle}' has been permanently deleted";
                        } else {
                            $_SESSION['error_message'] = 'Failed to delete content from database. Please try again.';
                        }
                        $deleteStmt->close();
                    } else {
                        $_SESSION['error_message'] = 'Content not found.';
                    }
                    $stmt->close();
                }
                break;
                
            case 'ban_user':
                $userId = intval($_POST['user_id'] ?? 0);
                if ($userId > 0) {
                    // Check if user exists and get their info
                    $checkStmt = $conn->prepare("SELECT full_name, email FROM users WHERE id = ?");
                    $checkStmt->bind_param("i", $userId);
                    $checkStmt->execute();
                    $userResult = $checkStmt->get_result();

                    if ($userInfo = $userResult->fetch_assoc()) {
                        // Update user status - use is_blocked instead of is_banned
                        $stmt = $conn->prepare("UPDATE users SET is_blocked = 1, is_active = 0 WHERE id = ?");
                        $stmt->bind_param("i", $userId);
                        if ($stmt->execute()) {
                            // Log admin action
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'USER_BAN', $userId,
                                         "Banned user: {$userInfo['full_name']} ({$userInfo['email']}) via content management");
                            $_SESSION['success_message'] = "User '{$userInfo['full_name']}' has been banned successfully";
                        } else {
                            $_SESSION['error_message'] = 'Failed to ban user. Please try again.';
                        }
                        $stmt->close();
                    } else {
                        $_SESSION['error_message'] = 'User not found.';
                    }
                    $checkStmt->close();
                } else {
                    $_SESSION['error_message'] = 'Invalid user ID.';
                }
                break;
        }
    }

    // Clean output buffer and redirect
    ob_clean();
    header('Location: manage-content.php');
    exit;
}

// Get filter parameters
$contentType = $_GET['type'] ?? 'all'; // 'all', 'screen', 'mirror'
$status = $_GET['status'] ?? 'all'; // 'all', 'public', 'hidden', 'reported'
$search = sanitize($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

// Build query for content
$allContent = [];

// Get screen recordings
if ($contentType === 'all' || $contentType === 'screen') {
    $whereClause = "1=1";
    if ($status === 'public') $whereClause .= " AND sr.is_public = 1";
    if ($status === 'hidden') $whereClause .= " AND sr.is_public = 0";
    if ($status === 'reported') $whereClause .= " AND sr.is_reported = 1";
    if (!empty($search)) $whereClause .= " AND (sr.title LIKE '%$search%' OR u.full_name LIKE '%$search%')";
    
    $query = "
        SELECT sr.*, u.full_name, u.email, u.profile_picture, 'screen' as content_type,
               0 as view_count,
               0 as like_count,
               0 as comment_count
        FROM screen_records sr
        JOIN users u ON sr.user_id = u.id
        WHERE $whereClause
        ORDER BY sr.created_at DESC
        LIMIT $perPage OFFSET $offset
    ";

    try {
        $result = $conn->query($query);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $allContent[] = $row;
            }
        }
    } catch (Exception $e) {
        error_log("Screen records query error: " . $e->getMessage());
    }
}

// Get mirror recordings
if ($contentType === 'all' || $contentType === 'mirror') {
    $whereClause = "1=1";
    if ($status === 'public') $whereClause .= " AND mr.is_public = 1";
    if ($status === 'hidden') $whereClause .= " AND mr.is_public = 0";
    if ($status === 'reported') $whereClause .= " AND mr.is_reported = 1";
    if (!empty($search)) $whereClause .= " AND (mr.title LIKE '%$search%' OR u.full_name LIKE '%$search%')";
    
    $query = "
        SELECT mr.*, u.full_name, u.email, u.profile_picture, 'mirror' as content_type,
               0 as view_count,
               0 as like_count,
               0 as comment_count
        FROM mirror_recordings mr
        JOIN users u ON mr.user_id = u.id
        WHERE $whereClause
        ORDER BY mr.created_at DESC
        LIMIT $perPage OFFSET $offset
    ";

    try {
        $result = $conn->query($query);
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $allContent[] = $row;
            }
        }
    } catch (Exception $e) {
        error_log("Mirror records query error: " . $e->getMessage());
    }
}

// Sort by creation date
usort($allContent, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Get total count for pagination
$totalContent = count($allContent);

// Get platform statistics with error handling
$stats = [
    'total_screen' => 0,
    'total_mirror' => 0,
    'total_screen_views' => 0,
    'total_mirror_views' => 0,
    'reported_content' => 0
];

try {
    // Screen recordings stats
    $result = $conn->query("SELECT COUNT(*) as total_screen, COALESCE(SUM(views_count), 0) as total_screen_views FROM screen_records");
    if ($result) {
        $screenStats = $result->fetch_assoc();
        $stats['total_screen'] = $screenStats['total_screen'];
        $stats['total_screen_views'] = $screenStats['total_screen_views'];
    }

    // Mirror recordings stats
    $result = $conn->query("SELECT COUNT(*) as total_mirror, COALESCE(SUM(views_count), 0) as total_mirror_views FROM mirror_recordings");
    if ($result) {
        $mirrorStats = $result->fetch_assoc();
        $stats['total_mirror'] = $mirrorStats['total_mirror'];
        $stats['total_mirror_views'] = $mirrorStats['total_mirror_views'];
    }

    // Reported content
    $result = $conn->query("SELECT COUNT(*) as reported_screen FROM screen_records WHERE is_reported = 1");
    if ($result) {
        $reportedScreen = $result->fetch_assoc()['reported_screen'];
        $stats['reported_content'] += $reportedScreen;
    }

    $result = $conn->query("SELECT COUNT(*) as reported_mirror FROM mirror_recordings WHERE is_reported = 1");
    if ($result) {
        $reportedMirror = $result->fetch_assoc()['reported_mirror'];
        $stats['reported_content'] += $reportedMirror;
    }
} catch (Exception $e) {
    error_log("Statistics query error: " . $e->getMessage());
    // Stats remain at default values (0)
}

$conn->close();
?>

<style>
    /* Enhanced Admin Content Management Styles */
    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: #fff;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .modern-table thead {
        background: linear-gradient(135deg, #15803d 0%, #1a5f3f 100%);
        color: #fff;
    }
    .modern-table th, .modern-table td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #e9ecef;
        vertical-align: middle;
    }
    .modern-table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }
    .modern-table tr:nth-child(even) {
        background: #f8f9fa;
    }
    .modern-table tr:hover {
        background: #e8f5e8;
        transition: background-color 0.2s ease;
    }

    /* Enhanced Badges */
    .badge {
        border-radius: 0.5rem;
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .badge.bg-success { background-color: #28a745 !important; }
    .badge.bg-danger { background-color: #dc3545 !important; }
    .badge.bg-warning { background-color: #ffc107 !important; color: #212529 !important; }
    .badge.bg-secondary { background-color: #6c757d !important; }
    .badge.bg-primary { background-color: #007bff !important; }

    /* Enhanced Buttons */
    .btn {
        border-radius: 0.5rem !important;
        font-weight: 600;
        padding: 0.5rem 1rem;
        transition: all 0.2s ease;
        border: none;
    }
    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
    .btn-primary {
        background: linear-gradient(135deg, #15803d 0%, #1a5f3f 100%) !important;
        color: #fff !important;
    }
    .btn-primary:hover {
        background: linear-gradient(135deg, #1a5f3f 0%, #15803d 100%) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(21,128,61,0.3);
    }
    .btn-success { background-color: #28a745 !important; }
    .btn-success:hover { background-color: #218838 !important; transform: translateY(-1px); }
    .btn-warning { background-color: #ffc107 !important; color: #212529 !important; }
    .btn-warning:hover { background-color: #e0a800 !important; transform: translateY(-1px); }
    .btn-danger { background-color: #dc3545 !important; }
    .btn-danger:hover { background-color: #c82333 !important; transform: translateY(-1px); }
    .btn-dark { background-color: #343a40 !important; }
    .btn-dark:hover { background-color: #23272b !important; transform: translateY(-1px); }

    /* Button Groups */
    .btn-group .btn {
        margin-right: 0.25rem;
    }
    .btn-group .btn:last-child {
        margin-right: 0;
    }

    /* Cards */
    .card {
        border: none;
        border-radius: 0.75rem;
        box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        transition: box-shadow 0.2s ease;
    }
    .card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }
    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.75rem 0.75rem 0 0 !important;
        padding: 1rem 1.25rem;
    }

    /* Statistics Cards */
    .card.bg-primary { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important; }
    .card.bg-success { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important; }
    .card.bg-info { background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important; }
    .card.bg-warning { background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%) !important; }

    /* Alerts */
    .alert {
        border: none;
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .alert-success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border-left: 4px solid #28a745;
    }
    .alert-danger {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    /* Content Thumbnail */
    .content-thumbnail {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #dee2e6;
    }

    /* Form Controls */
    .form-select, .form-control {
        border-radius: 0.5rem;
        border: 1px solid #ced4da;
        padding: 0.6rem 0.75rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }
    .form-select:focus, .form-control:focus {
        border-color: #15803d;
        box-shadow: 0 0 0 0.2rem rgba(21,128,61,0.25);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .modern-table th, .modern-table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.9rem;
        }
        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        .btn-group .btn {
            margin-right: 0;
            font-size: 0.8rem;
        }
        .card-body {
            padding: 1rem;
        }
    }

    /* Loading States */
    .btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* Table Responsive */
    .table-responsive {
        border-radius: 0.5rem;
        overflow: hidden;
    }
</style>

<!-- Success/Error Messages -->
<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<!-- Content Statistics -->
<div class="row mb-4">
    <div class="col-md-3 col-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Screen Records</h6>
                        <h4 class="mb-0"><?php echo number_format($stats['total_screen']); ?></h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-desktop fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Mirror Records</h6>
                        <h4 class="mb-0"><?php echo number_format($stats['total_mirror']); ?></h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-video fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Views</h6>
                        <h4 class="mb-0"><?php echo number_format($stats['total_screen_views'] + $stats['total_mirror_views']); ?></h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-eye fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Reported Content</h6>
                        <h4 class="mb-0"><?php echo number_format($stats['reported_content']); ?></h4>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-flag fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="type" class="form-label">Content Type</label>
                <select name="type" id="type" class="form-select">
                    <option value="all" <?php echo $contentType === 'all' ? 'selected' : ''; ?>>All Types</option>
                    <option value="screen" <?php echo $contentType === 'screen' ? 'selected' : ''; ?>>Screen Records</option>
                    <option value="mirror" <?php echo $contentType === 'mirror' ? 'selected' : ''; ?>>Mirror Records</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                    <option value="public" <?php echo $status === 'public' ? 'selected' : ''; ?>>Public</option>
                    <option value="hidden" <?php echo $status === 'hidden' ? 'selected' : ''; ?>>Hidden</option>
                    <option value="reported" <?php echo $status === 'reported' ? 'selected' : ''; ?>>Reported</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" placeholder="Search by title or user..." value="<?php echo htmlspecialchars($search); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Content Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Content Management
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Content</th>
                        <th>User</th>
                        <th>Type</th>
                        <th>Stats</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th class="text-end">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($allContent)): ?>
                        <tr><td colspan="7" class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No content found matching your criteria.</p>
                        </td></tr>
                    <?php else: ?>
                        <?php foreach ($allContent as $content): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="content-thumbnail me-3">
                                        <i class="fas fa-<?php echo $content['content_type'] === 'screen' ? 'desktop' : 'video'; ?> fa-2x text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1"><?php echo htmlspecialchars($content['title'] ?: 'Untitled'); ?></h6>
                                        <small class="text-muted">
                                            <?php if ($content['duration']): ?>
                                                <i class="fas fa-clock me-1"></i><?php echo gmdate("i:s", $content['duration']); ?>
                                            <?php endif; ?>
                                            <?php if ($content['file_size']): ?>
                                                <i class="fas fa-file ms-2 me-1"></i><?php echo number_format($content['file_size'] / 1024 / 1024, 1); ?>MB
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <img src="<?php echo $content['profile_picture'] ?: '../assets/images/default-avatar.png'; ?>"
                                         alt="User" class="rounded-circle me-2" width="32" height="32">
                                    <div>
                                        <div class="fw-medium"><?php echo htmlspecialchars($content['full_name']); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($content['email']); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $content['content_type'] === 'screen' ? 'primary' : 'success'; ?>">
                                    <?php echo ucfirst($content['content_type']); ?> Record
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i><?php echo number_format($content['views_count']); ?>
                                    <i class="fas fa-heart ms-2 me-1"></i><?php echo number_format($content['likes_count']); ?>
                                    <i class="fas fa-comment ms-2 me-1"></i><?php echo number_format($content['comments_count']); ?>
                                </small>
                            </td>
                            <td>
                                <?php if ($content['is_reported']): ?>
                                    <span class="badge bg-danger">Reported</span>
                                <?php elseif ($content['is_public']): ?>
                                    <span class="badge bg-success">Public</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Hidden</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('M d, Y', strtotime($content['created_at'])); ?><br>
                                    <?php echo date('H:i', strtotime($content['created_at'])); ?>
                                </small>
                            </td>
                            <td class="text-end">
                                <div class="btn-group" role="group">
                                    <?php if ($content['is_public']): ?>
                                        <button type="button" class="btn btn-sm btn-warning"
                                                onclick="toggleContent(<?php echo $content['id']; ?>, '<?php echo $content['content_type']; ?>', 'hide')"
                                                title="Hide Content">
                                            <i class="fas fa-eye-slash"></i>
                                        </button>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-sm btn-success"
                                                onclick="toggleContent(<?php echo $content['id']; ?>, '<?php echo $content['content_type']; ?>', 'show')"
                                                title="Show Content">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php endif; ?>

                                    <button type="button" class="btn btn-sm btn-danger"
                                            onclick="deleteContent(<?php echo $content['id']; ?>, '<?php echo $content['content_type']; ?>')"
                                            title="Delete Content">
                                        <i class="fas fa-trash"></i>
                                    </button>

                                    <button type="button" class="btn btn-sm btn-dark"
                                            onclick="banUser(<?php echo $content['user_id']; ?>)"
                                            title="Ban User">
                                        <i class="fas fa-user-slash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>

<!-- Hidden form for actions -->
<form id="actionForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" id="actionType">
    <input type="hidden" name="content_id" id="contentId">
    <input type="hidden" name="content_type" id="contentType">
    <input type="hidden" name="user_id" id="userId">
</form>

<script>
    function toggleContent(contentId, contentType, action) {
        const actionText = action === 'hide' ? 'hide this content from public view' : 'make this content visible to the public';
        const confirmText = `Are you sure you want to ${actionText}?`;

        if (confirm(confirmText)) {
            // Show loading state
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            document.getElementById('actionType').value = action;
            document.getElementById('contentId').value = contentId;
            document.getElementById('contentType').value = contentType;
            document.getElementById('actionForm').submit();
        }
    }

    function deleteContent(contentId, contentType) {
        const confirmText = 'Are you sure you want to permanently delete this content?\n\n' +
                          '⚠️ WARNING: This action cannot be undone!\n' +
                          '• The content file will be deleted from the server\n' +
                          '• All associated comments and likes will be removed\n' +
                          '• This action will be logged for audit purposes';

        if (confirm(confirmText)) {
            // Show loading state
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            document.getElementById('actionType').value = 'delete';
            document.getElementById('contentId').value = contentId;
            document.getElementById('contentType').value = contentType;
            document.getElementById('actionForm').submit();
        }
    }

    function banUser(userId) {
        const confirmText = 'Are you sure you want to ban this user?\n\n' +
                          '⚠️ This will:\n' +
                          '• Block the user from accessing the platform\n' +
                          '• Deactivate their account\n' +
                          '• Log this action for audit purposes\n\n' +
                          'The user can be unbanned later from the User Management section.';

        if (confirm(confirmText)) {
            // Show loading state
            const button = event.target.closest('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            document.getElementById('actionType').value = 'ban_user';
            document.getElementById('userId').value = userId;
            document.getElementById('actionForm').submit();
        }
    }

    // Auto-dismiss alerts after 5 seconds
    document.addEventListener('DOMContentLoaded', function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                if (alert && alert.parentNode) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 500);
                }
            }, 5000);
        });
    });
</script>

<?php
// End output buffering and flush
ob_end_flush();
?>