/**
 * Wallet JavaScript for RECITE App
 * Handles form validation and dynamic calculations
 */

// Paystack funding logic
function initiatePaystack(event) {
    event.preventDefault();

    const fundAmount = document.getElementById('fund_amount').value;

    if (!fundAmount || fundAmount < 100) {
        alert('Please enter a valid amount (minimum ₦100).');
        return;
    }

    const handler = PaystackPop.setup({
        key: paystackPublicKey,
        email: userEmail,
        amount: fundAmount * 100, // Amount in kobo
        currency: 'NGN',
        ref: 'FUND_' + userId + '_' + fundAmount + '_' + Math.floor((Math.random() * 1000000000) + 1),
        callback: function(response) {
            // Payment successful, verify on server
            verifyPayment(response.reference);
        },
        onClose: function() {
            // User closed the popup
            alert('Transaction was not completed.');
        }
    });

    handler.openIframe();
}

function verifyPayment(reference) {
    fetch('api/paystack_fund.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reference: reference })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Wallet funded successfully!');
            window.location.reload();
        } else {
            alert('Payment verification failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while verifying payment.');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Wallet JavaScript loaded');
    
    // Fund Wallet Form Validation
    const fundForm = document.getElementById('fundWalletForm');
    if (fundForm) {
        fundForm.addEventListener('submit', function(e) {
            const amount = document.getElementById('fund_amount').value;
            
            if (!amount || amount < 100) {
                e.preventDefault();
                alert('Please enter a valid amount (minimum ₦100).');
                return false;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('#fundWalletModal .btn-primary');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;
            }
            
            return true;
        });
    }
    
    // Withdraw Form Validation
    const withdrawForm = document.getElementById('withdrawForm');
    if (withdrawForm) {
        withdrawForm.addEventListener('submit', function(e) {
            const amount = document.getElementById('withdraw_amount').value;
            const bankName = document.getElementById('bank_name').value;
            const bankAccount = document.getElementById('bank_account').value;
            
            if (!amount || amount < 500) {
                e.preventDefault();
                alert('Please enter a valid amount (minimum ₦500).');
                return false;
            }
            
            if (!bankName || !bankAccount) {
                e.preventDefault();
                alert('Please enter bank details.');
                return false;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('#withdrawModal .btn-primary');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;
            }
            
            return true;
        });
    }
    
    // Buy Points Form Validation
    const buyPointsForm = document.getElementById('buyPointsForm');
    if (buyPointsForm) {
        buyPointsForm.addEventListener('submit', function(e) {
            const points = document.getElementById('points_to_buy').value;
            
            if (!points || points < 1) {
                e.preventDefault();
                alert('Please enter a valid number of points.');
                return false;
            }
            
            const cost = points * 70;
            if (!confirm(`Buy ${points} points for ₦${cost.toLocaleString()}?`)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('#buyPointsModal .btn-success');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;
            }
            
            return true;
        });
    }
    
    // Sell Points Form Validation
    const sellPointsForm = document.getElementById('sellPointsForm');
    if (sellPointsForm) {
        sellPointsForm.addEventListener('submit', function(e) {
            const points = document.getElementById('points_to_sell').value;
            
            if (!points || points < 1) {
                e.preventDefault();
                alert('Please enter a valid number of points.');
                return false;
            }
            
            const earning = points * 50;
            if (!confirm(`Sell ${points} points for ₦${earning.toLocaleString()}?`)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('#sellPointsModal .btn-warning');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                submitBtn.disabled = true;
            }
            
            return true;
        });
    }
    
    // Dynamic cost calculation for buying points
    const pointsToBuyInput = document.getElementById('points_to_buy');
    if (pointsToBuyInput) {
        pointsToBuyInput.addEventListener('input', function() {
            const points = this.value;
            const cost = points * 70;
            const costDisplay = document.getElementById('buy_cost_display');
            if (costDisplay) {
                costDisplay.textContent = `₦${cost.toLocaleString()}`;
            }
        });
    }
    
    // Dynamic earning calculation for selling points
    const pointsToSellInput = document.getElementById('points_to_sell');
    if (pointsToSellInput) {
        pointsToSellInput.addEventListener('input', function() {
            const points = this.value;
            const earning = points * 50;
            const earningDisplay = document.getElementById('sell_earning_display');
            if (earningDisplay) {
                earningDisplay.textContent = `₦${earning.toLocaleString()}`;
            }
        });
    }
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }
        }, 5000);
    });
});
