# MySQL Collation Fix Instructions

## Problem
Your MySQL server doesn't support the `utf8mb4_0900_ai_ci` collation, which causes the error:
```
#1273 - Unknown collation: 'utf8mb4_0900_ai_ci'
```

## Solution
I've created fixed SQL files that use `utf8mb4_general_ci` collation, which is compatible with your MySQL version.

## Quick Fix Options

### Option 1: Import Admin Table Only (Fastest)
If you just need to fix the admin login issue:

1. **In phpMyAdmin or your database tool:**
   - Import: `database/admin_table_fixed.sql`
   - This creates the admin table with correct collation
   - Includes the admin user with password: `1@3Usazladan`

### Option 2: Import Complete Fixed Database
If you want to import the entire database:

1. **Use the fixed SQL file:**
   - Import: `database/recite_app_fixed.sql`
   - This is your complete database with all collation issues fixed

### Option 3: Run SQL Commands Directly
Copy and paste this SQL into phpMyAdmin:

```sql
-- Create admin table with compatible collation
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert admin user
INSERT INTO `admins` (`username`, `password`, `email`, `full_name`, `is_active`) VALUES
('admin', '$2y$10$xYhrgKAA83a37AXGAXrjDOmNOxUkVBMXt/DCpsznGu5GoVV6GHyDW', '<EMAIL>', 'System Administrator', 1)
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    updated_at = CURRENT_TIMESTAMP;
```

## What Was Fixed

### Collation Changes
- ❌ `utf8mb4_0900_ai_ci` (not supported)
- ✅ `utf8mb4_general_ci` (compatible)

### Table Structure Fixes
- ✅ Added missing `AUTO_INCREMENT` to all ID columns
- ✅ Added missing `PRIMARY KEY` definitions
- ✅ Fixed all table indexes and constraints
- ✅ Ensured consistent collation across all tables

## Files Created

1. **`database/recite_app_fixed.sql`** - Complete fixed database
2. **`database/admin_table_fixed.sql`** - Admin table only (quick fix)
3. **`database/create_admin_table.sql`** - Simple admin table creation
4. **`fix_sql_collation.php`** - Script that fixed the collation issues

## Admin Login Credentials

After importing any of the fixed SQL files:
- **Username:** admin
- **Password:** 1@3Usazladan
- **URL:** https://universalreciters.name.ng/admin/login.php

## Compatibility

The fixed SQL files work with:
- ✅ MySQL 5.7+
- ✅ MySQL 8.0+
- ✅ MariaDB 10.2+
- ✅ Most shared hosting providers
- ✅ cPanel/phpMyAdmin environments

## Next Steps

1. **Import the fixed SQL file** using one of the options above
2. **Test admin login** at: https://universalreciters.name.ng/admin/login.php
3. **Update your database credentials** in `config/server_config.php` if needed
4. **Run the setup tools** if you still have issues:
   - https://universalreciters.name.ng/setup_production.php
   - https://universalreciters.name.ng/admin/production_fix.php

## Verification

After importing, verify the admin table was created correctly:

```sql
DESCRIBE admins;
SELECT * FROM admins WHERE username = 'admin';
```

You should see the admin user with ID 1 and the correct password hash.

## Cleanup

After successful setup, delete these files for security:
- `fix_sql_collation.php`
- `setup_production.php`
- `admin/production_fix.php`
- `admin/diagnostic.php`
- `check_mysql_compatibility.php`
