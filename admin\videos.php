<?php
$page_title = 'Manage Videos';
require_once __DIR__ . '/../components/admin_header.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// Handle delete recording
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_recording') {
    $recordingId = $_POST['recording_id'] ?? '';
    $recordType = $_POST['record_type'] ?? '';

    try {
        $conn = getConnection();

        // Handle file system files (orphaned or file_system type)
        if (($recordType === 'orphaned' || $recordType === 'file_system') && (strpos($recordingId, 'orphan_') === 0 || strpos($recordingId, 'file_') === 0)) {
            $filePath = $_POST['file_path'] ?? '';
            if ($filePath && file_exists($filePath)) {
                if (unlink($filePath)) {
                    $message = 'File deleted successfully!';
                } else {
                    $error = 'Failed to delete file.';
                }
            } else {
                $error = 'File not found.';
            }
        }
        // Handle database records
        elseif (is_numeric($recordingId) && intval($recordingId) > 0) {
            $recordingId = intval($recordingId);

            // Get file path before deletion using executeQuery
            $table = ($recordType === 'screen_record') ? 'screen_records' : 'mirror_recordings';

            try {
                $result = executeQuery("SELECT file_path FROM $table WHERE id = ?", 'i', [$recordingId]);

                if ($result && $row = $result->fetch_assoc()) {
                    $filePath = $row['file_path'];

                    // Delete from database using executeQuery
                    $deleteResult = executeQuery("DELETE FROM $table WHERE id = ?", 'i', [$recordingId]);

                    if ($deleteResult && $deleteResult->success) {
                        // Delete physical file
                        if (file_exists($filePath)) {
                            unlink($filePath);
                        }
                        $message = 'Recording deleted successfully!';
                    } else {
                        $error = 'Failed to delete recording from database.';
                    }
                } else {
                    $error = 'Recording not found.';
                }
            } catch (Exception $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        } else {
            $error = 'Invalid recording ID.';
        }
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        logError("Admin delete recording error: " . $e->getMessage());
    }
}

// Handle video upload
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_video') {
    $title = sanitize($_POST['title']);
    $youtube_url = sanitize($_POST['youtube_url']);
    $category = sanitize($_POST['category']);
    $reciter = sanitize($_POST['reciter']);
    $description = sanitize($_POST['description']);
    $transcript = sanitize($_POST['transcript']);
    
    if (empty($title) || empty($youtube_url)) {
        $error = 'Title and YouTube URL are required.';
    } else {
        try {
            // Extract YouTube ID
            $youtube_id = extractYouTubeId($youtube_url);
            if (!$youtube_id) {
                throw new Exception('Invalid YouTube URL format.');
            }

            $adminId = $_SESSION['admin_id'];
            $insertResult = executeQuery("
                INSERT INTO videos (title, youtube_url, youtube_id, category, reciter, description, transcript, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ", "sssssssi", [$title, $youtube_url, $youtube_id, $category, $reciter, $description, $transcript, $adminId]);

            if ($insertResult && $insertResult->success) {
                $message = 'Video added successfully!';
            } else {
                $error = 'Failed to add video.';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
            logError("Admin add video error: " . $e->getMessage());
        }
    }
}

// Handle video toggle (activate/deactivate)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'toggle_video') {
    $videoId = intval($_POST['video_id']);
    $isActive = intval($_POST['is_active']);
    
    try {
        $updateResult = executeQuery("UPDATE videos SET is_active = ? WHERE id = ?", "ii", [$isActive, $videoId]);

        if ($updateResult && $updateResult->success) {
            $message = $isActive ? 'Video activated successfully!' : 'Video deactivated successfully!';
        } else {
            $error = 'Failed to update video status.';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin toggle video error: " . $e->getMessage());
    }
}

// Handle video deletion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete_video') {
    $videoId = intval($_POST['video_id']);
    
    try {
        $deleteResult = executeQuery("DELETE FROM videos WHERE id = ?", "i", [$videoId]);

        if ($deleteResult && $deleteResult->success) {
            $message = 'Video deleted successfully!';
        } else {
            $error = 'Failed to delete video.';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        logError("Admin delete video error: " . $e->getMessage());
    }
}

// Get user recordings from database and file system
$screenRecords = [];
$mirrorRecords = [];
$streamVideos = [];
$orphanedFiles = [];
$videos = [];

try {
    // First, scan the uploads/recordings directory for all video files
    $recordingsDir = '../uploads/recordings/';
    $allVideoFiles = [];

    if (is_dir($recordingsDir)) {
        $files = scandir($recordingsDir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && !is_dir($recordingsDir . $file)) {
                $isVideoFile = preg_match('/\.(mp4|webm|ogg|mov|avi)$/i', $file);
                if ($isVideoFile) {
                    $fullPath = $recordingsDir . $file;
                    $allVideoFiles[] = [
                        'filename' => $file,
                        'full_path' => $fullPath,
                        'file_size' => file_exists($fullPath) ? filesize($fullPath) : 0,
                        'created_time' => file_exists($fullPath) ? filemtime($fullPath) : time(),
                        'user_id' => null
                    ];

                    // Try to extract user ID from filename pattern
                    if (preg_match('/recording_(\d+)_/', $file, $matches)) {
                        $allVideoFiles[count($allVideoFiles) - 1]['user_id'] = intval($matches[1]);
                    }
                }
            }
        }
    }

    // Now try to get database records (if tables exist)
    $tablesExist = [];
    $checkTables = ['screen_records', 'mirror_recordings', 'streams'];

    foreach ($checkTables as $table) {
        try {
            $result = executeQuery("SHOW TABLES LIKE ?", 's', [$table]);
            $tablesExist[$table] = ($result && $result->num_rows > 0);
        } catch (Exception $e) {
            $tablesExist[$table] = false;
        }
    }

    // Get screen recordings if table exists
    if ($tablesExist['screen_records']) {
        try {
            $screenResult = executeQuery("
                SELECT sr.*, u.full_name, u.email,
                       'screen_record' as record_type,
                       DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
                FROM screen_records sr
                LEFT JOIN users u ON sr.user_id = u.id
                WHERE sr.file_path IS NOT NULL AND sr.file_path != ''
                ORDER BY sr.created_at DESC
            ");
            if ($screenResult) {
                while ($row = $screenResult->fetch_assoc()) {
                    $screenRecords[] = $row;
                }
            }
        } catch (Exception $e) {
            // Log error but continue
            error_log("Error fetching screen records: " . $e->getMessage());
        }
    }

    // Get mirror recordings if table exists
    if ($tablesExist['mirror_recordings']) {
        try {
            $mirrorResult = executeQuery("
                SELECT mr.*, u.full_name, u.email,
                       'mirror_record' as record_type,
                       DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date
                FROM mirror_recordings mr
                LEFT JOIN users u ON mr.user_id = u.id
                WHERE mr.file_path IS NOT NULL AND mr.file_path != ''
                ORDER BY mr.created_at DESC
            ");
            if ($mirrorResult) {
                while ($row = $mirrorResult->fetch_assoc()) {
                    $mirrorRecords[] = $row;
                }
            }
        } catch (Exception $e) {
            // Log error but continue
            error_log("Error fetching mirror records: " . $e->getMessage());
        }
    }

    // Get video streams if table exists
    if ($tablesExist['streams']) {
        try {
            $streamsResult = executeQuery("
                SELECT s.*, u.full_name, u.email,
                       'stream_video' as record_type,
                       DATE_FORMAT(s.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                       s.media_path as file_path
                FROM streams s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.content_type = 'video' AND s.media_path IS NOT NULL AND s.media_path != ''
                ORDER BY s.created_at DESC
            ");
            if ($streamsResult) {
                while ($row = $streamsResult->fetch_assoc()) {
                    $streamVideos[] = $row;
                }
            }
        } catch (Exception $e) {
            // Log error but continue
            error_log("Error fetching stream videos: " . $e->getMessage());
        }
    }

    // Now create the final video list by combining database records with file system files
    $dbFilePaths = array_merge(
        array_column($screenRecords, 'file_path'),
        array_column($mirrorRecords, 'file_path'),
        array_column($streamVideos, 'file_path')
    );

    // Process all video files found in the file system
    foreach ($allVideoFiles as $fileInfo) {
        $fullPath = $fileInfo['full_path'];
        $fileName = $fileInfo['filename'];

        // Check if this file has a corresponding database record
        $foundInDb = false;
        $dbRecord = null;

        // Check in screen records
        foreach ($screenRecords as $record) {
            if (strpos($record['file_path'], $fileName) !== false || $record['file_path'] === $fullPath) {
                $foundInDb = true;
                $dbRecord = $record;
                break;
            }
        }

        // Check in mirror records if not found in screen records
        if (!$foundInDb) {
            foreach ($mirrorRecords as $record) {
                if (strpos($record['file_path'], $fileName) !== false || $record['file_path'] === $fullPath) {
                    $foundInDb = true;
                    $dbRecord = $record;
                    break;
                }
            }
        }

        // Check in stream videos if not found yet
        if (!$foundInDb) {
            foreach ($streamVideos as $record) {
                if (strpos($record['file_path'], $fileName) !== false || $record['file_path'] === $fullPath) {
                    $foundInDb = true;
                    $dbRecord = $record;
                    break;
                }
            }
        }

        if ($foundInDb && $dbRecord) {
            // Use database record with file system info
            $dbRecord['file_path'] = $fullPath; // Ensure correct path
            $dbRecord['file_size'] = $fileInfo['file_size'];
            $videos[] = $dbRecord;
        } else {
            // Create orphaned file record
            $userId = $fileInfo['user_id'];
            $userName = 'Unknown User';
            $userEmail = '<EMAIL>';

            // Get user info if user ID found
            if ($userId) {
                try {
                    $userResult = executeQuery("SELECT full_name, email FROM users WHERE id = ?", 'i', [$userId]);
                    if ($userResult && $userRow = $userResult->fetch_assoc()) {
                        $userName = $userRow['full_name'] ?: 'User ' . $userId;
                        $userEmail = $userRow['email'] ?: 'user' . $userId . '@example.com';
                    }
                } catch (Exception $e) {
                    // Log error but continue with default values
                    error_log("Error fetching user info: " . $e->getMessage());
                }
            }

            $videos[] = [
                'id' => 'file_' . md5($fileName),
                'title' => $fileName,
                'description' => 'Video file from uploads directory',
                'file_path' => $fullPath,
                'file_size' => $fileInfo['file_size'],
                'full_name' => $userName,
                'email' => $userEmail,
                'user_id' => $userId,
                'record_type' => 'file_system',
                'created_at' => date('Y-m-d H:i:s', $fileInfo['created_time']),
                'formatted_date' => date('M d, Y at h:i A', $fileInfo['created_time']),
                'views_count' => 0,
                'likes_count' => 0,
                'comments_count' => 0,
                'shares_count' => 0,
                'earnings' => 0
            ];
        }
    }

    // Sort by created_at descending
    usort($videos, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });

} catch (Exception $e) {
    $videos = [];
    $error = "Database error: " . $e->getMessage();
    logError("Admin fetch videos error: " . $e->getMessage());
}

// Debug: Add some information about what was found
$debugInfo = [
    'file_system_files' => count($allVideoFiles ?? []),
    'screen_records_count' => count($screenRecords ?? []),
    'mirror_records_count' => count($mirrorRecords ?? []),
    'stream_videos_count' => count($streamVideos ?? []),
    'total_videos' => count($videos ?? []),
    'tables_exist' => $tablesExist ?? []
];

// Function to extract YouTube video ID from URL
function extractYouTubeId($url) {
    $patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/v\/([a-zA-Z0-9_-]+)/',
        '/youtube\.com\/user\/[^\/]+#.*\/([a-zA-Z0-9_-]+)/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>

<!-- Add Video Button/Modal -->
<div class="mb-4 text-end">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVideoModal">
        <i class="fas fa-plus me-2"></i>Add New Video
    </button>
</div>

<?php if ($message): ?>
    <div class="alert alert-success mb-4"><?php echo $message; ?></div>
<?php endif; ?>
<?php if ($error): ?>
    <div class="alert alert-danger mb-4"><?php echo $error; ?></div>
<?php endif; ?>

<!-- User Recordings Cards -->
<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">User Recordings & Streams</h4>
        <p class="text-muted">All recordings uploaded by users from the dashboard and selfie mirror</p>
    </div>

    <!-- Debug Information -->
    <!-- Removed debug info for production UI -->

    <?php if (empty(
        $videos)): ?>
        <div class="text-center py-5">
            <i class="fas fa-video fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No recordings found</h5>
            <p class="text-muted">User recordings will appear here once uploaded.</p>
        </div>
    <?php else: ?>
        <div class="row g-4">
            <?php foreach ($videos as $video): ?>
            <div class="col-lg-4 col-md-6 col-sm-12">
                <div class="card video-card simple-card h-100 d-flex flex-column justify-content-between" style="min-height: 370px; max-height: 370px;">
                    <div class="video-container position-relative" style="height: 200px;">
                        <?php
                        $videoPath = htmlspecialchars($video['file_path']);
                        $isVideoFile = preg_match('/\.(mp4|webm|ogg|mov|avi)$/i', $videoPath);
                        ?>
                        <?php if ($isVideoFile): ?>
                            <video class="card-img-top video-player" controls preload="metadata" style="height: 200px; object-fit: cover; border-radius: 12px 12px 0 0;">
                                <source src="<?php echo $videoPath; ?>" type="video/mp4">
                                <source src="<?php echo $videoPath; ?>" type="video/webm">
                                Your browser does not support the video tag.
                            </video>
                        <?php else: ?>
                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 200px; border-radius: 12px 12px 0 0;">
                                <i class="fas fa-video fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        <!-- Video Type Badge -->
                        <?php
                        $badgeClass = 'bg-secondary';
                        $badgeText = 'Unknown';
                        switch ($video['record_type']) {
                            case 'screen_record': $badgeClass = 'bg-primary'; $badgeText = 'Dashboard'; break;
                            case 'mirror_record': $badgeClass = 'bg-success'; $badgeText = 'Selfie Mirror'; break;
                            case 'stream_video': $badgeClass = 'bg-info'; $badgeText = 'Stream Video'; break;
                            case 'file_system': $badgeClass = 'bg-warning text-dark'; $badgeText = 'File System'; break;
                            case 'orphaned': $badgeClass = 'bg-danger'; $badgeText = 'Orphaned'; break;
                        }
                        ?>
                        <span class="badge position-absolute top-0 end-0 m-2 <?php echo $badgeClass; ?>">
                            <?php echo $badgeText; ?>
                        </span>
                        <!-- File Size Badge -->
                        <?php if (isset($video['file_size']) && $video['file_size'] > 0): ?>
                        <span class="file-size-badge">
                            <?php echo number_format($video['file_size'] / 1024 / 1024, 1) . ' MB'; ?>
                        </span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body p-3">
                        <h6 class="card-title mb-1" style="font-size: 1.05rem; font-weight: 600; color: #222; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                            <?php
                            // Only show a user-friendly title, not the raw file name or 'Video file from uploads directory'
                            $title = $video['title'];
                            if (preg_match('/^recording_\d+_\d+\.(mp4|webm|ogg|mov|avi)$/i', $title)) {
                                $title = 'User Recording';
                            } elseif (strtolower(trim($title)) === 'video file from uploads directory') {
                                $title = 'User Recording';
                            }
                            echo htmlspecialchars($title);
                            ?>
                        </h6>
                        <div class="d-flex align-items-center mb-2">
                            <div class="user-avatar me-2" style="width: 28px; height: 28px; background: #1a5f3f; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.85rem; font-weight: bold;">
                                <?php echo strtoupper(substr($video['full_name'], 0, 1)); ?>
                            </div>
                            <div style="font-size: 0.93rem; color: #555;">
                                <?php echo htmlspecialchars($video['full_name']); ?>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between small text-muted mb-2">
                            <span><?php echo $video['formatted_date']; ?></span>
                            <span><?php echo isset($video['file_size']) ? number_format($video['file_size'] / 1024 / 1024, 2) . ' MB' : 'Unknown'; ?></span>
                        </div>
                        <p class="card-text text-muted small mb-0" style="min-height: 32px; max-height: 32px; overflow: hidden; text-overflow: ellipsis;">
                            <?php
                            // Hide the description if it's 'Video file from uploads directory' or a raw file name
                            $desc = $video['description'] ?? '';
                            if (strtolower(trim($desc)) === 'video file from uploads directory' || preg_match('/^recording_\d+_\d+\.(mp4|webm|ogg|mov|avi)$/i', $desc)) {
                                $desc = '';
                            }
                            echo htmlspecialchars($desc);
                            ?>
                        </p>
                    </div>
                    <div class="card-footer bg-transparent border-0 pt-0 pb-3 d-flex justify-content-center gap-2" style="margin-top:auto;">
                        <?php if ($isVideoFile): ?>
                            <button class="btn btn-sm btn-primary" onclick="playVideo('<?php echo $videoPath; ?>', '<?php echo htmlspecialchars($title); ?>')">
                                <i class="fas fa-play"></i>
                            </button>
                        <?php endif; ?>
                        <a href="<?php echo $videoPath; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                        <a href="<?php echo $videoPath; ?>" download class="btn btn-sm btn-outline-success">
                            <i class="fas fa-download"></i>
                        </a>
                        <form action="videos.php" method="POST" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this recording?');">
                            <input type="hidden" name="action" value="delete_recording">
                            <input type="hidden" name="recording_id" value="<?php echo $video['id']; ?>">
                            <input type="hidden" name="record_type" value="<?php echo $video['record_type']; ?>">
                            <?php if ($video['record_type'] === 'orphaned' || $video['record_type'] === 'file_system'): ?>
                            <input type="hidden" name="file_path" value="<?php echo htmlspecialchars($video['file_path']); ?>">
                            <?php endif; ?>
                            <button type="submit" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Add Video Modal -->
<div class="modal fade" id="addVideoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
             <form action="videos.php" method="POST">
                <input type="hidden" name="action" value="add_video">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Video</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="title" placeholder="Video Title" required>
                                <label>Video Title</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" name="reciter" placeholder="Reciter Name" required>
                                <label>Reciter Name</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="url" class="form-control" name="youtube_url" placeholder="Full YouTube URL (e.g., https://www.youtube.com/watch?v=...)" required>
                        <label>Full YouTube URL</label>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" name="category" placeholder="Category (e.g., Surah Al-Fatiha)">
                        <label>Category</label>
                    </div>
                    <div class="form-floating">
                        <textarea class="form-control" name="description" placeholder="Description" style="height: 100px"></textarea>
                        <label>Description</label>
                    </div>
                    <div class="form-floating mt-3">
                        <textarea class="form-control" name="transcript" placeholder="Full transcript for recitation" style="height: 150px"></textarea>
                        <label>Transcript</label>
                        <div class="form-text">Enter the exact text that users will recite. This will be used for speech recognition feedback. Example: "بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Video</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Video Player Modal -->
<div class="modal fade" id="videoPlayerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="videoPlayerTitle">Video Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <video id="modalVideoPlayer" class="w-100" controls style="max-height: 70vh;">
                    <source id="modalVideoSource" src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a id="downloadVideoBtn" href="" download class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Download Video
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.video-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid #dee2e6;
}

.video-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.video-container {
    overflow: hidden;
    border-radius: 0.375rem 0.375rem 0 0;
}

.video-player {
    transition: transform 0.2s ease-in-out;
}

.video-player:hover {
    transform: scale(1.02);
}

.user-avatar {
    flex-shrink: 0;
}

.card-footer {
    border-top: 1px solid rgba(0,0,0,.125);
}

.btn-group-sm > .btn, .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Orphaned file styling */
.video-card.orphaned {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.video-card.orphaned .card-header {
    background: #fff3cd;
    border-bottom: 1px solid #ffc107;
}

/* Video file type indicators */
.video-card[data-file-type="mp4"] {
    border-left: 4px solid #28a745;
}

.video-card[data-file-type="webm"] {
    border-left: 4px solid #007bff;
}

.video-card[data-file-type="unknown"] {
    border-left: 4px solid #6c757d;
}

/* Loading state for videos */
.video-player.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* File size indicator */
.file-size-badge {
    position: absolute;
    bottom: 5px;
    left: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
}

@media (max-width: 768px) {
    .video-card {
        margin-bottom: 1rem;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .video-container {
        position: relative;
    }
}


    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }


.modern-table { width: 100%; border-collapse: separate; border-spacing: 0; }
.modern-table thead { background: #e6f9ed; color: #15803d; }
.modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
.modern-table tr:nth-child(even) { background: #f6fff9; }
.badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
.btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
.btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
.table-responsive { overflow-x: auto; }
@media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
@media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } }
</style>

<script>
function playVideo(videoSrc, videoTitle) {
    const modal = new bootstrap.Modal(document.getElementById('videoPlayerModal'));
    const videoPlayer = document.getElementById('modalVideoPlayer');
    const videoSource = document.getElementById('modalVideoSource');
    const modalTitle = document.getElementById('videoPlayerTitle');
    const downloadBtn = document.getElementById('downloadVideoBtn');

    // Set video source and title
    videoSource.src = videoSrc;
    modalTitle.textContent = videoTitle || 'Video Player';
    downloadBtn.href = videoSrc;

    // Load the video
    videoPlayer.load();

    // Show modal
    modal.show();

    // Auto-play when modal is shown
    document.getElementById('videoPlayerModal').addEventListener('shown.bs.modal', function () {
        videoPlayer.play().catch(function(error) {
            console.log('Auto-play prevented:', error);
        });
    });

    // Pause video when modal is hidden
    document.getElementById('videoPlayerModal').addEventListener('hidden.bs.modal', function () {
        videoPlayer.pause();
        videoPlayer.currentTime = 0;
    });
}

// Initialize video players with error handling
document.addEventListener('DOMContentLoaded', function() {
    const videoPlayers = document.querySelectorAll('.video-player');

    videoPlayers.forEach(function(video) {
        video.addEventListener('error', function() {
            const container = this.parentElement;
            container.innerHTML = '<div class="d-flex align-items-center justify-content-center bg-light" style="height: 200px;"><div class="text-center"><i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i><br><small class="text-muted">Video cannot be loaded</small></div></div>';
        });

        // Add loading state
        video.addEventListener('loadstart', function() {
            this.style.opacity = '0.7';
        });

        video.addEventListener('loadeddata', function() {
            this.style.opacity = '1';
        });
    });
});
</script>

<?php require_once __DIR__ . '/../components/admin_error_log_widget.php'; ?>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>