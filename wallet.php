<?php
/**
 * Wallet Page for RECITE App
 * Complete wallet management with funding, withdrawal, and points exchange
 */

require_once 'config/db_config.php';
require_once 'includes/points_system.php';

// Require login
requireLogin();

$page_title = 'My Wallet';
$userId = $_SESSION['user_id'];

// Get user data
$user = getUserById($userId);
if (!$user) {
    header('Location: logout.php');
    exit;
}

$walletBalance = floatval($user['wallet_balance'] ?? 0);

// Get user points
$userPoints = getUserPointsSummary($userId);
$totalPoints = $userPoints['total_points'] ?? 0;

// Exchange rates
$pointBuyRate = 70;  // ₦70 per point when buying
$pointSellRate = 50; // ₦50 per point when selling

// Get recent wallet transactions
$recentTransactions = [];
try {
    $transConn = getConnection();
    $stmt = $transConn->prepare("
        SELECT * FROM transactions
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $recentTransactions[] = $row;
    }
    $stmt->close();

    // Get withdrawal history
    $withdrawalHistory = [];
    $stmt = $transConn->prepare("
        SELECT * FROM withdrawal_requests
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $withdrawalHistory[] = $row;
    }
    $stmt->close();

} catch (Exception $e) {
    error_log("Error fetching transactions: " . $e->getMessage());
}

$message = '';
$error = '';
$showPaystackSection = false;
$fundAmount = 0;
$walletMessage = isset($_GET['wallet_message']) ? $_GET['wallet_message'] : '';
$walletStatus = isset($_GET['wallet_status']) ? $_GET['wallet_status'] : '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    // Debug: Log the action being processed
    error_log("Wallet form action: " . $action);
    error_log("POST data: " . print_r($_POST, true));

    if ($action === 'fund_wallet') {
        $fundAmount = floatval($_POST['fund_amount']);
        
        if ($fundAmount > 0 && $fundAmount >= 100) {
            $_SESSION['pending_wallet_fund'] = $fundAmount;
            $showPaystackSection = true;
            $message = 'Please complete the payment to fund your wallet.';
        } else {
            $error = 'Please enter a valid amount (minimum ₦100).';
        }
    } elseif ($action === 'withdraw_money') {
        $withdrawAmount = floatval($_POST['withdraw_amount']);
        $bankAccount = $_POST['bank_account'] ?? '';
        $bankName = $_POST['bank_name'] ?? '';
        $customBankName = $_POST['custom_bank_name'] ?? '';
        $accountName = $_POST['account_name'] ?? '';
        $accountHolderName = $_POST['account_holder_name'] ?? $user['full_name'];

        // Use custom bank name if "other" was selected
        if ($bankName === 'other' && !empty($customBankName)) {
            $bankName = $customBankName;
        }

        if ($withdrawAmount > 0 && $withdrawAmount >= 500 && $walletBalance >= $withdrawAmount && !empty($bankAccount) && !empty($bankName) && !empty($accountName)) {
            $conn = getConnection();
            $conn->begin_transaction();

            try {
                // Deduct from wallet
                $newBalance = $walletBalance - $withdrawAmount;
                $updateStmt = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateStmt->bind_param("di", $newBalance, $userId);
                $updateStmt->execute();

                // Create withdrawal request
                $withdrawalStmt = $conn->prepare("
                    INSERT INTO withdrawal_requests
                    (user_id, amount, points_converted, bank_name, account_number, account_holder_name, admin_notes, status, created_at)
                    VALUES (?, ?, 0, ?, ?, ?, ?, 'pending', NOW())
                ");
                $adminNotes = "Account Name: " . $accountName;
                $withdrawalStmt->bind_param("idssss", $userId, $withdrawAmount, $bankName, $bankAccount, $accountHolderName, $adminNotes);
                $withdrawalStmt->execute();

                // Also record in transactions table for user history
                $transactionStmt = $conn->prepare("
                    INSERT INTO transactions
                    (user_id, transaction_type, amount, description, status)
                    VALUES (?, 'withdrawal', ?, ?, 'pending')
                ");
                $description = "Withdrawal to " . $bankName . " - " . $bankAccount;
                $transactionStmt->bind_param("ids", $userId, $withdrawAmount, $description);
                $transactionStmt->execute();

                $conn->commit();
                $message = "Withdrawal request submitted! ₦" . number_format($withdrawAmount, 2) . " will be processed within 24 hours.";

                // Refresh user data
                $user = getUserById($userId);
                $walletBalance = floatval($user['wallet_balance'] ?? 0);

            } catch (Exception $e) {
                $conn->rollback();
                $error = "Withdrawal failed. Please try again. Error: " . $e->getMessage();
                logError("Withdrawal error for user $userId: " . $e->getMessage());
            }
        } else {
            $error = "Invalid withdrawal request. Check amount (min ₦500), balance, bank details, and account name.";
        }
    } elseif ($action === 'buy_points') {
        $pointsToBuy = intval($_POST['points_to_buy']);
        $totalCost = $pointsToBuy * $pointBuyRate;
        
        // Debug information
        error_log("Buy points attempt - User ID: $userId, Points: $pointsToBuy, Cost: $totalCost, Balance: $walletBalance");
        
        if ($pointsToBuy > 0 && $walletBalance >= $totalCost) {
            $conn = getConnection();
            $conn->begin_transaction();
            
            try {
                // Deduct from wallet
                $newBalance = $walletBalance - $totalCost;
                $updateStmt = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateStmt->bind_param("di", $newBalance, $userId);
                $updateResult = $updateStmt->execute();
                
                if (!$updateResult) {
                    throw new Exception("Failed to update wallet balance: " . $conn->error);
                }
                
                // Award points using the awardPoints function
                $description = "Purchased {$pointsToBuy} points for ₦{$totalCost}";
                $awardSuccess = awardPoints($userId, $pointsToBuy, 'purchase', $description);
                
                if (!$awardSuccess) {
                    throw new Exception("Failed to award points");
                }
                
                // Record transaction in main transactions table
                $transactionStmt = $conn->prepare("
                    INSERT INTO transactions
                    (user_id, transaction_type, amount, points, status, description)
                    VALUES (?, 'point_purchase', ?, ?, 'completed', ?)
                ");
                $transactionDescription = "Purchased {$pointsToBuy} points";
                $transactionStmt->bind_param("idis", $userId, $totalCost, $pointsToBuy, $transactionDescription);
                $transactionResult = $transactionStmt->execute();
                
                if (!$transactionResult) {
                    throw new Exception("Failed to record transaction: " . $conn->error);
                }
                
                $conn->commit();
                $message = "Successfully purchased {$pointsToBuy} points for ₦{$totalCost}!";
                
                // Refresh user data
                $user = getUserById($userId);
                $walletBalance = floatval($user['wallet_balance'] ?? 0);
                $userPoints = getUserPointsSummary($userId);
                $totalPoints = $userPoints['total_points'] ?? 0;
                
                error_log("Buy points success - New balance: $walletBalance, New points: $totalPoints");
                
            } catch (Exception $e) {
                $conn->rollback();
                $error = "Purchase failed. Please try again. Error: " . $e->getMessage();
                error_log("Buy points error: " . $e->getMessage());
            }
        } else {
            if ($pointsToBuy <= 0) {
                $error = "Please enter a valid number of points to buy.";
            } elseif ($walletBalance < $totalCost) {
                $error = "Insufficient wallet balance. You need ₦" . number_format($totalCost, 2) . " but have ₦" . number_format($walletBalance, 2);
            } else {
                $error = "Invalid points amount.";
            }
        }
    } elseif ($action === 'sell_points') {
        $pointsToSell = intval($_POST['points_to_sell']);
        $totalEarning = $pointsToSell * $pointSellRate;
        
        if ($pointsToSell > 0 && $totalPoints >= $pointsToSell) {
            $conn = getConnection();
            $conn->begin_transaction();
            
            try {
                // Deduct points
                awardPoints($userId, -$pointsToSell, 'sale', "Sold {$pointsToSell} points for ₦{$totalEarning}");
                
                // Add money to wallet
                $newWalletBalance = $walletBalance + $totalEarning;
                $updateWallet = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateWallet->bind_param("di", $newWalletBalance, $userId);
                $updateWallet->execute();
                
                // Record transaction
                $transactionStmt = $conn->prepare("
                    INSERT INTO transactions
                    (user_id, transaction_type, amount, points, status, description)
                    VALUES (?, 'point_sale', ?, ?, 'completed', ?)
                ");
                $description = "Sold {$pointsToSell} points";
                $transactionStmt->bind_param("idis", $userId, $totalEarning, $pointsToSell, $description);
                $transactionStmt->execute();
                
                $conn->commit();
                $message = "Successfully sold {$pointsToSell} points for ₦{$totalEarning}!";
                
                // Refresh user data
                $user = getUserById($userId);
                $walletBalance = floatval($user['wallet_balance'] ?? 0);
                $userPoints = getUserPointsSummary($userId);
                $totalPoints = $userPoints['total_points'] ?? 0;
                
            } catch (Exception $e) {
                $conn->rollback();
                $error = "Sale failed. Please try again.";
            }
        } else {
            $error = "Insufficient points or invalid amount.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - RECITE</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Modern Mobile CSS -->
    <link href="assets/css/modern-mobile.css" rel="stylesheet">

    <script src="https://js.paystack.co/v1/inline.js"></script>
</head>
<body>
    <div class="main-content">
        <!-- App Header -->
        <div class="app-header">
            <div class="header-content">
                <div class="header-title">
                    <i class="fas fa-wallet"></i> My Wallet
                </div>
                <div class="header-actions">
                    <a href="dashboard.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back
                    </a>
            </div>
            </div>
        </div>

        <div class="container">
    <?php if ($walletMessage): ?>
        <div class="alert alert-<?php echo $walletStatus === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
            <i class="fas fa-<?php echo $walletStatus === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i> <?php echo htmlspecialchars($walletMessage); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <?php if ($showPaystackSection): ?>
        <div id="paymentSection" class="text-center mt-4 p-4 bg-white rounded shadow-sm">
            <div class="text-3xl fw-bold text-primary mb-3">₦<?php echo number_format($fundAmount, 2); ?></div>
            <p class="text-muted mb-3">You are being redirected to the payment page...</p>
            <button onclick="payWithPaystack()" class="btn btn-primary w-100 mb-2">
                <i class="fas fa-credit-card"></i> Click here to pay if you are not redirected automatically.
            </button>
            <p class="text-sm text-gray-500 mt-2">
                <i class="fas fa-shield-alt"></i> Secure payment powered by Paystack
            </p>
        </div>
        <script>
            setTimeout(function() { payWithPaystack(); }, 1500);
        </script>
    <?php endif; ?>
            <!-- Balance Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">₦<?php echo number_format($walletBalance, 2); ?></div>
                    <div class="stat-label">Wallet Balance</div>
                <form method="POST" class="mt-3" id="fundWalletForm">
                    <input type="hidden" name="action" value="fund_wallet">
                        <div class="form-group">
                            <label for="fund_amount" class="form-label">Amount (Min ₦100)</label>
                            <input type="number" name="fund_amount" id="fund_amount" class="form-control" placeholder="Enter amount" min="100" step="0.01" required>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-credit-card"></i> Fund Wallet
                        </button>
                    </form>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($totalPoints); ?></div>
                    <div class="stat-label">Points Balance</div>
                    <form method="POST" id="sellPointsForm" class="mt-3">
                        <input type="hidden" name="action" value="sell_points">
                        <div class="form-group">
                            <label for="points_to_sell" class="form-label">Sell Points</label>
                            <input type="number" name="points_to_sell" id="points_to_sell" class="form-control" placeholder="Points to sell" min="1" max="<?php echo $totalPoints; ?>" required>
                        </div>
                        <button type="submit" class="btn btn-warning btn-full" onclick="return validateSellPoints()">
                            <i class="fas fa-money-bill-wave"></i> Sell Points
                        </button>
                    </form>
                </div>
            </div>
            <!-- Withdrawal Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-minus-circle"></i> Withdraw Money</h3>
        </div>
                <div class="card-body">
                <form method="POST" id="withdrawForm">
                    <input type="hidden" name="action" value="withdraw_money">
                        <div class="form-group">
                        <label for="withdraw_amount" class="form-label">Amount (Minimum ₦500)</label>
                            <input type="number" name="withdraw_amount" id="withdraw_amount" class="form-control" placeholder="Enter amount" min="500" max="<?php echo $walletBalance; ?>" step="0.01" required>
                            <small class="text-muted">Available: ₦<?php echo number_format($walletBalance, 2); ?></small>
                        </div>
                        <div class="form-group">
                        <label for="bank_name" class="form-label">Bank Name</label>
                            <select name="bank_name" id="bank_name" class="form-control" required onchange="toggleCustomBank(this.value)">
                            <option value="">Select your bank...</option>
                            <option value="Access Bank">Access Bank</option>
                            <option value="Citibank">Citibank</option>
                            <option value="Ecobank">Ecobank</option>
                            <option value="Fidelity Bank">Fidelity Bank</option>
                            <option value="First Bank">First Bank</option>
                            <option value="FCMB">FCMB</option>
                            <option value="GTBank">GTBank (Guaranty Trust Bank)</option>
                            <option value="Heritage Bank">Heritage Bank</option>
                            <option value="Jaiz Bank">Jaiz Bank</option>
                            <option value="Keystone Bank">Keystone Bank</option>
                            <option value="Kuda Bank">Kuda Bank</option>
                            <option value="Moniepoint">Moniepoint</option>
                            <option value="Opay">Opay</option>
                            <option value="Palmpay">Palmpay</option>
                            <option value="Polaris Bank">Polaris Bank</option>
                            <option value="Providus Bank">Providus Bank</option>
                            <option value="Stanbic IBTC Bank">Stanbic IBTC Bank</option>
                            <option value="Standard Chartered Bank">Standard Chartered Bank</option>
                            <option value="Sterling Bank">Sterling Bank</option>
                            <option value="Suntrust Bank">Suntrust Bank</option>
                            <option value="TAJBank">TAJBank</option>
                            <option value="Union Bank">Union Bank</option>
                            <option value="UBA">UBA (United Bank for Africa)</option>
                            <option value="Unity Bank">Unity Bank</option>
                            <option value="VFD Microfinance Bank">VFD Microfinance Bank</option>
                            <option value="Wema Bank">Wema Bank</option>
                            <option value="Zenith Bank">Zenith Bank</option>
                            <option value="Other">Other</option>
                        </select>
                        <input type="text" name="custom_bank_name" id="custom_bank_name" class="form-control mt-2" placeholder="Please specify your bank name" style="display: none;">
                    </div>
                        <div class="form-group">
                        <label for="bank_account" class="form-label">Account Number</label>
                        <input type="text" name="bank_account" id="bank_account" class="form-control" placeholder="Enter account number" required>
                    </div>
                        <div class="form-group">
                        <label for="account_name" class="form-label">Account Name</label>
                        <input type="text" name="account_name" id="account_name" class="form-control" placeholder="Enter account name" required>
                    </div>
                        <div class="form-group">
                        <label for="account_holder_name" class="form-label">Account Holder Full Name</label>
                        <input type="text" name="account_holder_name" id="account_holder_name" class="form-control" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                    </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i> Withdrawals are processed within 24 hours.
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-paper-plane"></i> Submit Request
                        </button>
                </form>
            </div>
        </div>

            <!-- Recent Transactions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-history"></i> Recent Transactions</h3>
                </div>
                <div class="card-body">
                        <?php if (empty($recentTransactions)): ?>
                        <div class="text-center p-4">
                            <i class="fas fa-receipt" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                            <h4>No Transactions Yet</h4>
                            <p class="text-muted">Your transaction history will appear here</p>
                        </div>
                    <?php else: ?>
                        <div class="list">
                            <?php foreach ($recentTransactions as $t): ?>
                                <div class="list-item">
                                    <div class="list-item-icon">
                                        <i class="fas fa-<?php echo $t['transaction_type'] === 'fund' ? 'plus' : ($t['transaction_type'] === 'withdrawal' ? 'minus' : 'exchange-alt'); ?>"></i>
                                    </div>
                                    <div class="list-item-content">
                                        <div class="list-item-title"><?php echo ucfirst($t['transaction_type']); ?></div>
                                        <div class="list-item-subtitle"><?php echo date('M d, Y', strtotime($t['created_at'])); ?></div>
                                    </div>
                                    <div class="text-right">
                                        <div class="stat-number">₦<?php echo number_format($t['amount'], 2); ?></div>
                                        <span class="badge badge-<?php echo $t['status'] === 'completed' ? 'success' : ($t['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst($t['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- Withdrawal History -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-money-check-alt"></i> Withdrawal History</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($withdrawalHistory)): ?>
                        <div class="text-center p-4">
                            <i class="fas fa-history" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                            <h4>No Withdrawal History</h4>
                            <p class="text-muted">Your withdrawal requests will appear here</p>
                        </div>
                    <?php else: ?>
                        <div class="list">
                            <?php foreach ($withdrawalHistory as $w): ?>
                                <div class="list-item">
                                    <div class="list-item-icon">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="list-item-content">
                                        <div class="list-item-title">Withdrawal Request</div>
                                        <div class="list-item-subtitle"><?php echo date('M d, Y', strtotime($w['created_at'])); ?></div>
                                    </div>
                                    <div class="text-right">
                                        <div class="stat-number">₦<?php echo number_format($w['amount'], 2); ?></div>
                                        <span class="badge badge-<?php echo $w['status'] === 'completed' ? 'success' : ($w['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                            <?php echo ucfirst($w['status']); ?>
                                        </span>
        </div>
    </div>
                            <?php endforeach; ?>
                </div>
                    <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Bottom Navigation -->
<nav class="bottom-nav">
    <a href="dashboard.php" class="bottom-nav-item">
        <i class="fas fa-tachometer-alt"></i>
        <span>Dashboard</span>
    </a>
    <a href="streams.php" class="bottom-nav-item">
        <i class="fas fa-video"></i>
        <span>Streams</span>
    </a>
    <a href="community.php" class="bottom-nav-item">
        <i class="fas fa-users"></i>
        <span>Community</span>
    </a>
    <a href="wallet.php" class="bottom-nav-item active">
        <i class="fas fa-wallet"></i>
        <span>Wallet</span>
    </a>
    <a href="profile.php" class="bottom-nav-item">
        <i class="fas fa-user-circle"></i>
        <span>Profile</span>
    </a>
</nav>

<script src="https://js.paystack.co/v1/inline.js"></script>
<script>
    // Toggle custom bank input
    function toggleCustomBank(selectedValue) {
        const customBankInput = document.getElementById('custom_bank_name');
        if (selectedValue === 'Other') {
            customBankInput.style.display = 'block';
            customBankInput.required = true;
        } else {
            customBankInput.style.display = 'none';
            customBankInput.required = false;
        }
    }

    // Paystack configuration
    const paystackPublicKey = "<?php echo PAYSTACK_PUBLIC_KEY; ?>";
    const userEmail = "<?php echo $user['email']; ?>";
    const userId = "<?php echo $user['id']; ?>";
    const walletBalance = <?php echo json_encode($walletBalance); ?>;
    const totalPoints = <?php echo json_encode($totalPoints); ?>;
    const pointSellRate = <?php echo json_encode($pointSellRate); ?>;
    
    // Validate sell points
    function validateSellPoints() {
        const pointsToSell = parseInt(document.getElementById('points_to_sell').value);
        
        if (!pointsToSell || pointsToSell < 1) {
            alert('Please enter a valid number of points to sell (minimum 1)');
            return false;
        }
        
        if (pointsToSell > totalPoints) {
            alert('Insufficient points. You have ' + totalPoints + ' points but trying to sell ' + pointsToSell);
            return false;
        }
        
        return true;
    }
    
    // Paystack payment
    function payWithPaystack() {
        const fundAmount = <?php echo json_encode($fundAmount); ?>;
        if (!fundAmount || fundAmount < 100) { return; }
        
        const handler = PaystackPop.setup({
            key: paystackPublicKey,
            email: userEmail,
            amount: fundAmount * 100,
            currency: 'NGN',
            ref: 'FUND_' + userId + '_' + fundAmount + '_' + Math.floor((Math.random() * 1000000000) + 1),
            callback: function(response) {
                window.location.href = 'verify_wallet_payment.php?reference=' + response.reference;
            },
            onClose: function() {}
        });
        handler.openIframe();
    }

    // Add smooth animations
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    });
</script>
</body>
</html>
