<?php
/**
 * Database Compatibility Fix Script
 * Fixes collation and compatibility issues for older MySQL versions
 */

require_once 'config/db_config.php';

echo "🔧 Database Compatibility Fix\n";
echo "============================\n\n";

$conn = getConnection();

if (!$conn) {
    die("❌ Database connection failed\n");
}

try {
    // Get MySQL version
    $result = $conn->query("SELECT VERSION() as version");
    $row = $result->fetch_assoc();
    $mysqlVersion = $row['version'];
    echo "MySQL Version: $mysqlVersion\n\n";
    
    // Check if we need to use older collation
    $useOldCollation = version_compare($mysqlVersion, '8.0.0', '<');
    
    if ($useOldCollation) {
        echo "⚠️  Detected older MySQL version. Using compatible collation.\n\n";
        $collation = 'utf8mb4_unicode_ci';
    } else {
        echo "✅ MySQL 8.0+ detected. Using modern collation.\n\n";
        $collation = 'utf8mb4_0900_ai_ci';
    }
    
    // Fix existing tables with incompatible collation
    $tables = [
        'users',
        'transactions', 
        'point_transactions',
        'screen_records',
        'mirror_recordings',
        'videos',
        'forum_posts',
        'forum_replies',
        'forum_likes',
        'forum_categories',
        'video_unlocks',
        'admin_earnings'
    ];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "🔧 Fixing collation for table: $table\n";
            
            // Convert table to compatible collation
            $sql = "ALTER TABLE `$table` CONVERT TO CHARACTER SET utf8mb4 COLLATE $collation";
            if ($conn->query($sql)) {
                echo "✅ Fixed collation for $table\n";
            } else {
                echo "⚠️  Could not fix collation for $table: " . $conn->error . "\n";
            }
        }
    }
    
    echo "\n🎉 Database compatibility fix completed!\n";
    echo "You can now run the forum and video unlock system setup scripts.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 