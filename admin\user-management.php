<?php
$page_title = 'User Management';
require_once __DIR__ . '/../components/admin_header.php';

$message = '';
$messageType = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        $userId = intval($_POST['user_id'] ?? 0);
        $reason = sanitize($_POST['reason'] ?? '');
        
        if ($userId > 0 && in_array($action, ['activate', 'deactivate', 'delete', 'reset_password'])) {
            try {
                $conn = getConnection();
                
                // Get user details
                $userStmt = $conn->prepare("SELECT username, email, is_active FROM users WHERE id = ?");
                $userStmt->bind_param("i", $userId);
                $userStmt->execute();
                $user = $userStmt->get_result()->fetch_assoc();
                $userStmt->close();
                
                if ($user) {
                    switch ($action) {
                        case 'activate':
                            $updateStmt = $conn->prepare("UPDATE users SET is_active = 1, updated_at = NOW() WHERE id = ?");
                            $updateStmt->bind_param("i", $userId);
                            $actionText = 'activated';
                            break;
                            
                        case 'deactivate':
                            $updateStmt = $conn->prepare("UPDATE users SET is_active = 0, updated_at = NOW() WHERE id = ?");
                            $updateStmt->bind_param("i", $userId);
                            $actionText = 'deactivated';
                            break;
                            
                        case 'delete':
                            // Soft delete - mark as deleted
                            $updateStmt = $conn->prepare("UPDATE users SET is_active = 0, status = 'deleted', updated_at = NOW() WHERE id = ?");
                            $updateStmt->bind_param("i", $userId);
                            $actionText = 'deleted';
                            break;
                            
                        case 'reset_password':
                            // Generate temporary password
                            $tempPassword = generateReferralCode(8);
                            $hashedPassword = password_hash($tempPassword, PASSWORD_DEFAULT);
                            $updateStmt = $conn->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                            $updateStmt->bind_param("si", $hashedPassword, $userId);
                            $actionText = 'password reset';
                            break;
                    }
                    
                    if ($updateStmt->execute()) {
                        // Log admin action
                        if (function_exists('logAdminAction')) {
                            logAdminAction($_SESSION['admin_username'] ?? 'admin', 'USER_' . strtoupper($action), $userId, 
                                "User {$actionText}: {$user['username']} ({$user['email']})" . ($reason ? " - Reason: $reason" : ""));
                        }
                        
                        if ($action === 'reset_password') {
                            $message = "Password reset for user '{$user['username']}'. New temporary password: <strong>$tempPassword</strong>";
                        } else {
                            $message = "User '{$user['username']}' has been {$actionText} successfully.";
                        }
                        $messageType = 'success';
                    } else {
                        $message = 'Failed to update user.';
                        $messageType = 'danger';
                    }
                    
                    $updateStmt->close();
                } else {
                    $message = 'User not found.';
                    $messageType = 'warning';
                }
                
                $conn->close();
                
            } catch (Exception $e) {
                logError("User management error: " . $e->getMessage());
                $message = 'An error occurred while processing the request.';
                $messageType = 'danger';
            }
        }
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = sanitize($_GET['search'] ?? '');
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $conn = getConnection();
    
    // Build query based on filters
    $whereClause = "WHERE 1=1";
    $params = [];
    $types = '';
    
    if ($status === 'active') {
        $whereClause .= " AND is_active = 1 AND (status IS NULL OR status != 'deleted')";
    } elseif ($status === 'inactive') {
        $whereClause .= " AND is_active = 0 AND (status IS NULL OR status != 'deleted')";
    } elseif ($status === 'deleted') {
        $whereClause .= " AND status = 'deleted'";
    } else {
        $whereClause .= " AND (status IS NULL OR status != 'deleted')";
    }
    
    if ($search) {
        $whereClause .= " AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
        $types .= 'sss';
    }
    
    // Get total count
    $countQuery = "SELECT COUNT(*) as total FROM users $whereClause";
    if (!empty($params)) {
        $countStmt = $conn->prepare($countQuery);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $totalUsers = $countStmt->get_result()->fetch_assoc()['total'];
        $countStmt->close();
    } else {
        $totalUsers = $conn->query($countQuery)->fetch_assoc()['total'];
    }
    
    // Get users
    $query = "
        SELECT u.*, 
               (SELECT COUNT(*) FROM recitations WHERE user_id = u.id) as recitations_count,
               (SELECT COUNT(*) FROM streams WHERE user_id = u.id) as streams_count,
               (SELECT COUNT(*) FROM withdrawal_requests WHERE user_id = u.id) as withdrawals_count
        FROM users u 
        $whereClause 
        ORDER BY u.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $params[] = $limit;
        $params[] = $offset;
        $types .= 'ii';
        $stmt->bind_param($types, ...$params);
    } else {
        $stmt->bind_param('ii', $limit, $offset);
    }
    
    $stmt->execute();
    $users = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    // Get summary statistics
    $summaryQuery = "
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 AND (status IS NULL OR status != 'deleted') THEN 1 END) as active_users,
            COUNT(CASE WHEN is_active = 0 AND (status IS NULL OR status != 'deleted') THEN 1 END) as inactive_users,
            COUNT(CASE WHEN status = 'deleted' THEN 1 END) as deleted_users,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week,
            SUM(wallet_balance) as total_wallet_balance
        FROM users
    ";
    $summary = $conn->query($summaryQuery)->fetch_assoc();
    
    $conn->close();
    
} catch (Exception $e) {
    logError("User management page error: " . $e->getMessage());
    $users = [];
    $totalUsers = 0;
    $summary = [];
}

$totalPages = ceil($totalUsers / $limit);
$csrfToken = generateCSRFToken();
?>

<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $summary['total_users'] ?? 0; ?></h4>
                    <p class="mb-0">Total Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4><?php echo $summary['active_users'] ?? 0; ?></h4>
                    <p class="mb-0">Active</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4><?php echo $summary['inactive_users'] ?? 0; ?></h4>
                    <p class="mb-0">Inactive</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4><?php echo $summary['deleted_users'] ?? 0; ?></h4>
                    <p class="mb-0">Deleted</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4><?php echo $summary['new_users_week'] ?? 0; ?></h4>
                    <p class="mb-0">New (7 days)</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4>₦<?php echo number_format($summary['total_wallet_balance'] ?? 0); ?></h4>
                    <p class="mb-0">Total Wallets</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>User Management
                </h5>
                
                <!-- Status Filter -->
                <div class="btn-group">
                    <a href="?status=all&search=<?php echo urlencode($search); ?>" 
                       class="btn btn-sm <?php echo $status === 'all' ? 'btn-light' : 'btn-outline-light'; ?>">All</a>
                    <a href="?status=active&search=<?php echo urlencode($search); ?>" 
                       class="btn btn-sm <?php echo $status === 'active' ? 'btn-light' : 'btn-outline-light'; ?>">Active</a>
                    <a href="?status=inactive&search=<?php echo urlencode($search); ?>" 
                       class="btn btn-sm <?php echo $status === 'inactive' ? 'btn-light' : 'btn-outline-light'; ?>">Inactive</a>
                    <a href="?status=deleted&search=<?php echo urlencode($search); ?>" 
                       class="btn btn-sm <?php echo $status === 'deleted' ? 'btn-light' : 'btn-outline-light'; ?>">Deleted</a>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="mt-3">
                <form method="GET" class="d-flex">
                    <input type="hidden" name="status" value="<?php echo $status; ?>">
                    <input type="text" class="form-control form-control-sm me-2" name="search" 
                           placeholder="Search by username, email, or name..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-sm btn-light">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if ($search): ?>
                    <a href="?status=<?php echo $status; ?>" class="btn btn-sm btn-outline-light ms-1">
                        <i class="fas fa-times"></i>
                    </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
        <div class="card-body">
            
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            
            <div class="mb-3">
                <a href="add-user.php" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>Add New User
                </a>
            </div>
            
            <?php if (empty($users)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No users found</h5>
                <p class="text-muted">
                    <?php echo $search ? "No users match your search criteria." : "No users available with the selected filters."; ?>
                </p>
            </div>
            <?php else: ?>
            
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Contact</th>
                            <th>Location</th>
                            <th>Activity</th>
                            <th>Wallet</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($user['full_name'] ?? ''); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php echo htmlspecialchars($user['email']); ?><br>
                                <small class="text-muted"><?php echo htmlspecialchars($user['phone_number'] ?? ''); ?></small>
                            </td>
                            <td>
                                <small>
                                    <?php echo htmlspecialchars($user['ward'] ?? ''); ?><?php echo $user['ward'] && $user['state'] ? ', ' : ''; ?><?php echo htmlspecialchars($user['state'] ?? ''); ?><br>
                                    <?php echo htmlspecialchars($user['country'] ?? ''); ?>
                                </small>
                            </td>
                            <td>
                                <small>
                                    <i class="fas fa-microphone text-primary"></i> <?php echo $user['recitations_count']; ?><br>
                                    <i class="fas fa-video text-info"></i> <?php echo $user['streams_count']; ?><br>
                                    <i class="fas fa-money-bill-wave text-warning"></i> <?php echo $user['withdrawals_count']; ?>
                                </small>
                            </td>
                            <td>
                                <strong>₦<?php echo number_format($user['wallet_balance'], 2); ?></strong>
                            </td>
                            <td>
                                <?php if ($user['status'] === 'deleted'): ?>
                                    <span class="badge bg-danger">Deleted</span>
                                <?php elseif ($user['is_active']): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                            </td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm">
                                    <?php if ($user['status'] !== 'deleted'): ?>
                                        <?php if ($user['is_active']): ?>
                                        <button class="btn btn-warning btn-sm" onclick="processUser(<?php echo $user['id']; ?>, 'deactivate', '<?php echo htmlspecialchars($user['username']); ?>')">
                                            <i class="fas fa-user-slash"></i> Deactivate
                                        </button>
                                        <?php else: ?>
                                        <button class="btn btn-success btn-sm" onclick="processUser(<?php echo $user['id']; ?>, 'activate', '<?php echo htmlspecialchars($user['username']); ?>')">
                                            <i class="fas fa-user-check"></i> Activate
                                        </button>
                                        <?php endif; ?>
                                        
                                        <button class="btn btn-info btn-sm" onclick="processUser(<?php echo $user['id']; ?>, 'reset_password', '<?php echo htmlspecialchars($user['username']); ?>')">
                                            <i class="fas fa-key"></i> Reset Password
                                        </button>
                                        
                                        <button class="btn btn-danger btn-sm" onclick="processUser(<?php echo $user['id']; ?>, 'delete', '<?php echo htmlspecialchars($user['username']); ?>')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    <?php else: ?>
                                        <small class="text-muted">User deleted</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="User pagination">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Process User Modal -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="user_id" id="modalUserId">
                <input type="hidden" name="action" id="modalAction">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Process User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason/Notes</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="Optional reason for this action..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modalMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function processUser(userId, action, username) {
    document.getElementById('modalUserId').value = userId;
    document.getElementById('modalAction').value = action;
    
    const modal = new bootstrap.Modal(document.getElementById('processModal'));
    const title = document.getElementById('modalTitle');
    const message = document.getElementById('modalMessage');
    const submitBtn = document.getElementById('modalSubmitBtn');
    
    const actionConfig = {
        activate: {
            title: 'Activate User',
            message: `This will activate the user account for "${username}".`,
            btnClass: 'btn-success',
            btnText: '<i class="fas fa-user-check me-2"></i>Activate'
        },
        deactivate: {
            title: 'Deactivate User',
            message: `This will deactivate the user account for "${username}". The user will not be able to login.`,
            btnClass: 'btn-warning',
            btnText: '<i class="fas fa-user-slash me-2"></i>Deactivate'
        },
        reset_password: {
            title: 'Reset Password',
            message: `This will generate a new temporary password for "${username}".`,
            btnClass: 'btn-info',
            btnText: '<i class="fas fa-key me-2"></i>Reset Password'
        },
        delete: {
            title: 'Delete User',
            message: `This will permanently delete the user account for "${username}". This action cannot be undone.`,
            btnClass: 'btn-danger',
            btnText: '<i class="fas fa-trash me-2"></i>Delete User'
        }
    };
    
    const config = actionConfig[action];
    title.textContent = config.title;
    message.textContent = config.message;
    submitBtn.className = 'btn ' + config.btnClass;
    submitBtn.innerHTML = config.btnText;
    
    modal.show();
}
</script>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}
</style>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
