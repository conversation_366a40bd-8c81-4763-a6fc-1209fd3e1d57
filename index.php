<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    header('Location: user/dashboard.php');
    exit;
}

// Get featured videos and top users for display
$conn = getConnection();

// Get latest videos (with error handling)
$featuredVideos = [];
try {
    $stmt = $conn->prepare("SELECT * FROM videos WHERE is_active = 1 ORDER BY created_at DESC LIMIT 6");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $featuredVideos[] = $row;
    }
} catch (Exception $e) {
    // If videos table doesn't exist, create sample data
    error_log("Videos table error: " . $e->getMessage());
    $featuredVideos = [
        [
            'id' => 1,
            'title' => '<PERSON>ah Al-<PERSON>iha - Beautiful Recitation',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Abdul Rahman',
            'description' => 'Beautiful recitation of the opening chapter of the Quran'
        ],
        [
            'id' => 2,
            'title' => 'Surah Al-Ikhlas - Perfect Pronunciation',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Muhammad',
            'description' => 'Learn perfect pronunciation of Surah Al-Ikhlas'
        ],
        [
            'id' => 3,
            'title' => 'Surah Al-Falaq - Protection Prayer',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'category' => 'Short Surahs',
            'reciter' => 'Sheikh Ahmad',
            'description' => 'Seeking protection through Quranic recitation'
        ]
    ];
}

// Get top performers for leaderboard preview (with error handling)
$topUsers = [];
try {
    $stmt = $conn->prepare("
        SELECT u.full_name, u.profile_picture, u.state, u.ward,
               COALESCE(SUM(r.final_score), 0) as total_score
        FROM users u
        LEFT JOIN recitations r ON u.id = r.user_id AND r.is_completed = 1
        WHERE u.is_active = 1
        GROUP BY u.id
        ORDER BY total_score DESC
        LIMIT 10
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $topUsers[] = $row;
    }
} catch (Exception $e) {
    // If rankings table doesn't exist, create sample data
    error_log("Rankings query error: " . $e->getMessage());
    $topUsers = [
        [
            'full_name' => 'Abdullah Muhammad',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Kano',
            'ward' => 'Fagge',
            'total_score' => 2450
        ],
        [
            'full_name' => 'Fatima Zahra',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Lagos',
            'ward' => 'Victoria Island',
            'total_score' => 2380
        ],
        [
            'full_name' => 'Muhammad Yusuf',
            'profile_picture' => 'assets/images/default-avatar.png',
            'state' => 'Abuja',
            'ward' => 'Garki',
            'total_score' => 2320
        ]
    ];
}

// Get recent stream recordings (with error handling)
$recentStreams = [];
try {
    $stmt = $conn->prepare("
        SELECT sr.*, u.full_name, u.profile_picture
        FROM screen_records sr
        JOIN users u ON sr.user_id = u.id
        WHERE sr.is_public = 1
        ORDER BY sr.created_at DESC
        LIMIT 8
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $recentStreams[] = $row;
    }
} catch (Exception $e) {
    // If screen_records table doesn't exist, create sample data
    error_log("Screen records query error: " . $e->getMessage());
    $recentStreams = [
        [
            'id' => 1,
            'title' => 'My Surah Al-Fatiha Practice',
            'full_name' => 'Abdullah Muhammad',
            'profile_picture' => 'assets/images/default-avatar.png',
            'views_count' => 45,
            'likes_count' => 12,
            'duration' => 180,
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
        ],
        [
            'id' => 2,
            'title' => 'Perfect Tajweed Practice',
            'full_name' => 'Fatima Zahra',
            'profile_picture' => 'assets/images/default-avatar.png',
            'views_count' => 38,
            'likes_count' => 15,
            'duration' => 240,
            'created_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
        ]
    ];
}

$stmt->close();
$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Learn, Recite, Rank & Earn</title>

    <!-- Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">

    <style>
        /* Enhanced Spacing & Modern Design */
        body {
            background: var(--white);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .section-header {
            text-align: center;
            margin-bottom: 3.5rem;
            margin-top: 2.5rem;
        }
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            width: 100%;
            box-sizing: border-box;
        }
        .feature-card, .stats-card {
            background: var(--white);
            padding: 2.5rem 2rem;
            border-radius: 1.5rem;
            box-shadow: 0 4px 24px rgba(0,0,0,0.07);
            text-align: center;
            transition: box-shadow 0.2s, transform 0.2s;
            border: 1px solid var(--gray-200);
        }
        .feature-card:hover, .stats-card:hover {
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            transform: translateY(-6px) scale(1.02);
            border-color: var(--primary-light);
        }
        .feature-icon, .stats-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem auto;
            color: var(--white);
            font-size: 2.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .section-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        .section-title i {
            color: var(--primary-light);
            font-size: 2rem;
        }
        .hero-section {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: var(--white);
            padding: 5rem 0 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 100 100\'><defs><pattern id=\'grain\' width=\'100\' height=\'100\' patternUnits=\'userSpaceOnUse\'><circle cx=\'50\' cy=\'50\' r=\'1\' fill=\'rgba(255,255,255,0.1)\'/></pattern></defs><rect width=\'100\' height=\'100\' fill=\'url(%23grain)\'/></svg>');
            opacity: 0.25;
        }
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        .hero-icon {
            font-size: 5rem;
            margin-bottom: 2.5rem;
            color: #fffbe6;
            text-shadow: 0 2px 8px rgba(0,0,0,0.10);
            animation: pulse 2.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.08); }
            100% { transform: scale(1); }
        }
        .hero-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
            line-height: 1.2;
            text-shadow: 0 2px 8px rgba(0,0,0,0.10);
        }
        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2.5rem;
            opacity: 0.97;
            line-height: 1.6;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        .hero-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        .hero-btn {
            padding: 1rem 2.5rem;
            border-radius: 2rem;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: background 0.2s, color 0.2s, transform 0.2s;
        }
        .hero-btn-primary {
            background: var(--white);
            color: var(--primary);
        }
        .hero-btn-primary:hover {
            background: var(--gray-50);
            color: var(--primary-dark);
            transform: translateY(-2px) scale(1.04);
        }
        .hero-btn-secondary {
            background: rgba(255,255,255,0.12);
            color: var(--white);
            border: 2px solid rgba(255,255,255,0.3);
        }
        .hero-btn-secondary:hover {
            background: rgba(255,255,255,0.22);
            color: var(--white);
            border-color: rgba(255,255,255,0.5);
        }
        /* Video Card Play Overlay */
        .video-thumb {
            position: relative;
            aspect-ratio: 16/9;
            background: var(--gray-100);
            border-radius: 1rem;
            margin-bottom: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .video-thumb i.fa-play-circle {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 3rem;
            color: var(--primary-light);
            opacity: 0.85;
            transition: color 0.2s;
        }
        .feature-card:hover .video-thumb i.fa-play-circle {
            color: var(--primary);
        }
        /* Stats Section */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 2rem;
            margin-top: 2.5rem;
        }
        .stats-card {
            padding: 2rem 1.5rem;
        }
        .stats-icon {
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }
        /* Footer Social Icons */
        .footer-social a {
            color: var(--gray-400);
            font-size: 1.5rem;
            margin-right: 1rem;
            transition: color 0.2s;
        }
        .footer-social a:hover {
            color: var(--primary-light);
        }
        /* Responsive Tweaks */
        @media (max-width: 768px) {
            .main-container { padding: 0 1rem; }
            .hero-content { padding: 0 0.5rem; }
            .hero-title { font-size: 2rem; }
            .hero-subtitle { font-size: 1rem; }
            .features-grid, .stats-grid { grid-template-columns: 1fr; gap: 1.5rem; }
        }
        /* Responsive Header */
        .home-header {
            background: var(--white);
            box-shadow: var(--shadow-sm);
            padding: 1.25rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .home-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.25rem;
        }
        .home-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: var(--primary);
        }
        .home-logo-text {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--gray-900);
        }
        .home-nav-buttons {
            display: flex;
            gap: 0.5rem;
        }
        .home-nav-buttons .btn {
            padding: 0.5rem 1.1rem;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 0.5rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.4rem;
            transition: var(--transition);
            white-space: nowrap;
        }
        /* Responsive Features Section */
        .features-section {
            padding: 3rem 0;
            background: var(--gray-50);
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 1.5rem;
            width: 100%;
            max-width: 100%;
        }
        .feature-card {
            padding: 1.5rem 1rem;
        }
        .feature-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .feature-title {
            font-size: 1.1rem;
        }
        .feature-description {
            font-size: 0.98rem;
        }
        /* Responsive Buttons */
        .hero-btn, .btn, .btn-lg {
            padding: 0.6rem 1.3rem !important;
            font-size: 1rem !important;
            border-radius: 1.2rem !important;
        }
            .hero-btn {
            font-size: 1rem !important;
        }
        .btn-lg {
            font-size: 1.05rem !important;
            padding: 0.7rem 1.5rem !important;
        }
        /* Responsive Section Titles */
            .section-title {
            font-size: 1.5rem;
            gap: 0.5rem;
        }
        /* Responsive Main Container */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }
        /* Responsive Grids and Cards */
        @media (max-width: 900px) {
            .main-container { padding: 0 0.5rem; }
            .features-grid { grid-template-columns: 1fr 1fr; }
        }
        @media (max-width: 600px) {
            .home-nav { flex-direction: column; align-items: flex-start; padding: 0 0.5rem; }
            .home-nav-buttons { width: 100%; justify-content: flex-end; margin-top: 0.5rem; }
            .features-grid { grid-template-columns: 1fr; gap: 1rem; }
            .feature-card { padding: 1rem 0.5rem; }
            .section-header { margin-bottom: 2rem; margin-top: 1.2rem; }
            .section-title { font-size: 1.1rem; }
            .hero-content { padding: 0 0.5rem; }
            .hero-title { font-size: 1.2rem; }
            .hero-subtitle { font-size: 0.98rem; }
            .hero-btn, .btn, .btn-lg { font-size: 0.95rem !important; padding: 0.5rem 1rem !important; }
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="home-header">
        <nav class="home-nav">
            <a href="index.php" class="home-logo">
                <i class="fas fa-mosque"></i>
                <span class="home-logo-text"><?php echo APP_NAME; ?></span>
            </a>
            <div class="home-nav-buttons">
                <a href="login.php" class="btn btn-outline-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </a>
                <a href="register.php" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    <span>Register</span>
                </a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="hero-icon">
                <i class="fas fa-mosque"></i>
                <i class="fas fa-star" style="margin-left: -1.5rem; font-size: 2.5rem; color: gold; opacity: 0.7;"></i>
            </div>
            <h1 class="hero-title"><?php echo APP_NAME; ?></h1>
            <p class="hero-subtitle">Learn, Recite, Rank & Earn with AI-powered Qur'anic education platform</p>
            <div class="hero-buttons">
                <a href="register.php" class="hero-btn hero-btn-primary">
                    <i class="fas fa-user-plus"></i>
                    <span>Register (₦1,000)</span>
                </a>
                <a href="login.php" class="hero-btn hero-btn-secondary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Login</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title"><i class="fas fa-bolt"></i> Why Choose <?php echo APP_NAME; ?>?</h2>
                <p class="section-subtitle">Experience the future of Qur'anic learning with our innovative AI-powered platform</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-microphone-lines"></i></div>
                    <h3 class="feature-title">AI Speech Recognition</h3>
                    <p class="feature-description">Advanced AI technology provides real-time feedback on your recitation accuracy with precise word-by-word analysis and pronunciation guidance.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-trophy"></i></div>
                    <h3 class="feature-title">Ranking System</h3>
                    <p class="feature-description">Compete with users from your Ward, LGEA, State, and Country. Climb the leaderboards through consistent practice and dedication.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-wallet"></i></div>
                    <h3 class="feature-title">Earn Rewards</h3>
                    <p class="feature-description">Convert your learning achievements into real rewards. Earn points and cash through accurate recitations and consistent practice.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-video"></i></div>
                    <h3 class="feature-title">Video Streaming</h3>
                    <p class="feature-description">Share your recitation journey with the community. Watch, like, and learn from fellow reciters worldwide.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-users"></i></div>
                    <h3 class="feature-title">Community</h3>
                    <p class="feature-description">Connect with other learners through live chat, video calls, and group recitation sessions in our vibrant community.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon"><i class="fas fa-globe"></i></div>
                    <h3 class="feature-title">Multilingual Support</h3>
                    <p class="feature-description">Available in English, Hausa, Arabic, Hindi, and Chinese to serve our diverse global Muslim community.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <!-- <section class="py-5" style="background: var(--background-light);">
        <div class="main-container">
            <div class="row text-center">
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">1,000+</h3>
                            <p class="text-secondary mb-0">Active Users</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">50,000+</h3>
                            <p class="text-secondary mb-0">Recitations</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">₦500K+</h3>
                            <p class="text-secondary mb-0">Rewards Paid</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-primary mb-2">95%</h3>
                            <p class="text-secondary mb-0">Accuracy Rate</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- CTA Section -->
    <!-- <section class="py-5">
        <div class="main-container text-center">
            <div class="card" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); color: white;">
                <div class="card-body p-5">
                    <h2 class="mb-4">Ready to Start Your Qur'anic Journey?</h2>
                    <p class="text-large mb-4 opacity-75">Join thousands of Muslims worldwide who are improving their recitation skills and earning rewards.</p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="register.php" class="btn btn-lg" style="background: white; color: var(--primary-color);">
                            <i class="fas fa-rocket me-2"></i>Get Started Now
                        </a>
                        <a href="about.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>Learn More
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section> -->

    <!-- Featured Videos Section -->
    <section style="padding: var(--space-4xl) 0; background: var(--white);">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title"><i class="fas fa-play-circle"></i> Featured Recitations</h2>
                <p class="section-subtitle">Learn from beautiful Qur'anic recitations by renowned scholars</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2.5rem;">
                <?php foreach (array_slice($featuredVideos, 0, 6) as $video): ?>
                <div class="feature-card" style="text-align: left;">
                    <div class="video-thumb">
                        <i class="fab fa-youtube"></i>
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <h4 style="font-size: 1.25rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">
                        <?php echo htmlspecialchars($video['title']); ?>
                    </h4>
                    <p style="color: var(--gray-600); margin-bottom: 0.5rem; font-size: 1rem;">
                        <i class="fas fa-user-circle" style="color: var(--primary-light);"></i> <?php echo htmlspecialchars($video['reciter']); ?>
                    </p>
                    <p style="color: var(--gray-500); font-size: 0.95rem; line-height: 1.5;">
                        <?php echo htmlspecialchars(substr($video['description'], 0, 100)) . '...'; ?>
                    </p>
                </div>
                <?php endforeach; ?>
            </div>

            <div style="text-align: center; margin-top: 2.5rem;">
                <a href="register.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-play-circle"></i>
                    <span>Start Learning Today</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Community Stats Section -->
    <section style="padding: var(--space-4xl) 0; background: var(--gray-50);">
        <div class="main-container">
            <div class="section-header">
                <h2 class="section-title"><i class="fas fa-users"></i> Join Our Growing Community</h2>
                <p class="section-subtitle">Thousands of Muslims worldwide are improving their Qur'anic recitation</p>
            </div>

            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-icon"><i class="fas fa-user-graduate"></i></div>
                    <div style="font-size: 2rem; font-weight: 800; color: var(--primary); margin-bottom: 0.5rem;">
                        <?php echo count($topUsers) > 0 ? '1000+' : '500+'; ?>
                    </div>
                    <h4 style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">
                        Active Learners
                    </h4>
                    <p style="color: var(--gray-600); font-size: 1rem;">
                        Students practicing daily
                    </p>
                </div>

                <div class="stats-card">
                    <div class="stats-icon"><i class="fas fa-book-quran"></i></div>
                    <div style="font-size: 2rem; font-weight: 800; color: var(--primary); margin-bottom: 0.5rem;">
                        50+
                    </div>
                    <h4 style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">
                        Surahs Available
                    </h4>
                    <p style="color: var(--gray-600); font-size: 1rem;">
                        Complete Qur'anic chapters
                    </p>
                </div>

                <div class="stats-card">
                    <div class="stats-icon"><i class="fas fa-bullseye"></i></div>
                    <div style="font-size: 2rem; font-weight: 800; color: var(--primary); margin-bottom: 0.5rem;">
                        95%
                    </div>
                    <h4 style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">
                        Accuracy Rate
                    </h4>
                    <p style="color: var(--gray-600); font-size: 1rem;">
                        AI-powered precision
                    </p>
                </div>

                <div class="stats-card">
                    <div class="stats-icon"><i class="fas fa-language"></i></div>
                    <div style="font-size: 2rem; font-weight: 800; color: var(--primary); margin-bottom: 0.5rem;">
                        5
                    </div>
                    <h4 style="font-size: 1.1rem; font-weight: 700; color: var(--gray-900); margin-bottom: 0.5rem;">
                        Languages
                    </h4>
                    <p style="color: var(--gray-600); font-size: 1rem;">
                        Global accessibility
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--gray-900); color: var(--white); padding: var(--space-3xl) 0 var(--space-xl);">
        <div class="main-container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-2xl); margin-bottom: var(--space-2xl);">
                <div>
                    <div style="display: flex; align-items: center; gap: var(--space-md); margin-bottom: var(--space-lg);">
                        <i class="fas fa-mosque" style="font-size: var(--font-size-xl); color: var(--primary-light);"></i>
                        <h5 style="color: var(--white); font-weight: 700; margin: 0;"><?php echo APP_NAME; ?></h5>
                    </div>
                    <p style="color: var(--gray-300); line-height: 1.6; margin-bottom: var(--space-lg);">Empowering the global Muslim community through innovative Qur'anic education and AI-powered learning experiences.</p>
                    <div style="display: flex; gap: var(--space-md);" class="footer-social">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Platform</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm);"><a href="login.php" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Login</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="register.php" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Register</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#features" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Features</a></li>
                    </ul>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Community</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Rankings</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Streaming</a></li>
                        <li style="margin-bottom: var(--space-sm);"><a href="#" style="color: var(--gray-300); text-decoration: none; transition: var(--transition);" onmouseover="this.style.color='var(--primary-light)'" onmouseout="this.style.color='var(--gray-300)'">Support</a></li>
                    </ul>
                </div>

                <div>
                    <h6 style="color: var(--white); font-weight: 600; margin-bottom: var(--space-lg);">Languages</h6>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇺🇸 English</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇳🇬 Hausa</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇸🇦 العربية</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇮🇳 हिन्दी</li>
                        <li style="margin-bottom: var(--space-sm); color: var(--gray-300);">🇨🇳 中文</li>
                    </ul>
                </div>
            </div>

            <div style="border-top: 1px solid var(--gray-700); padding-top: var(--space-xl); display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--space-md);">
                <p style="color: var(--gray-400); margin: 0;">&copy; 2024 Universal Reciters Foundation. All rights reserved.</p>
                <p style="color: var(--gray-400); margin: 0;">Founded by Mallam AlFijir Usman Zaki</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html> 