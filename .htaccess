# Qur'an Recite App .htaccess Configuration
# Simplified version for development/production

# Enable URL rewriting
RewriteEngine On

# Basic Security Headers (only if mod_headers is available)
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing  
    Header set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # Relaxed Content Security Policy for development
    Header set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https:; img-src 'self' data: https: http:; frame-src https:;"
</IfModule>

# Hide sensitive files
<Files "config.php">
    Require all denied
</Files>

<Files "*.sql">
    Require all denied
</Files>

<Files ".env">
    Require all denied
</Files>

<Files "test.php">
    Require all denied
</Files>

# Set file upload limits and error reporting (only if mod_php is available)
<IfModule mod_php.c>
    php_value upload_max_filesize 100M
    php_value post_max_size 100M
    php_value max_execution_time 300
    php_value memory_limit 256M

    # Enable error reporting for debugging (comment out in production)
    php_flag display_errors On
    php_flag log_errors On
    php_value error_log error_log.txt
</IfModule>

# Enable compression (only if mod_deflate is available)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript
</IfModule>

# Browser caching for static assets (only if mod_expires is available)
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType image/png "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 week"
    ExpiresByType image/jpeg "access plus 1 week"
    ExpiresByType image/gif "access plus 1 week"
</IfModule>

# Simple URL redirects (only essential ones)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Admin redirects
RewriteRule ^admin/?$ admin/dashboard.php [L]
RewriteRule ^admin/login/?$ admin/login.php [L]

# User redirects  
RewriteRule ^dashboard/?$ user/dashboard.php [L]
RewriteRule ^profile/?$ user/profile.php [L]
RewriteRule ^wallet/?$ user/wallet.php [L]

# Basic security - prevent directory browsing
Options -Indexes

# Prevent access to common system files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|bak|swp)$">
    Require all denied
</FilesMatch> 