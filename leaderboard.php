<?php
/**
 * Leaderboard Page - Rankings and Weekly Bonuses
 */

require_once 'config/database.php';
require_once 'includes/points_system.php';

// Require login
requireLogin();

$page_title = 'Leaderboard';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get leaderboard data
$period = $_GET['period'] ?? 'weekly';
$topPerformers = getTopPerformers(50, $period);
$userRank = getUserRanking($userId);
$userPoints = getUserPointsSummary($userId);

// Get user's position in current leaderboard
$userPosition = null;
foreach ($topPerformers as $index => $performer) {
    if ($performer['id'] == $userId) {
        $userPosition = $index + 1;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Same design system as other pages */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            padding: 2rem 1rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        /* Period Tabs */
        .period-tabs {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 2rem 0;
        }

        .period-tab {
            padding: 0.75rem 1.5rem;
            border: 2px solid var(--primary);
            border-radius: 25px;
            text-decoration: none;
            color: var(--primary);
            font-weight: 500;
            transition: all 0.2s;
        }

        .period-tab.active {
            background: var(--primary);
            color: white;
        }

        .period-tab:hover {
            background: var(--primary);
            color: white;
        }

        /* User Stats Card */
        .user-stats {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .user-rank {
            font-size: 3rem;
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .user-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        /* Leaderboard */
        .leaderboard {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .leaderboard-header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            text-align: center;
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid var(--border);
            transition: background-color 0.2s;
        }

        .leaderboard-item:hover {
            background: #f8f9fa;
        }

        .leaderboard-item.current-user {
            background: rgba(26, 95, 63, 0.1);
            border-left: 4px solid var(--primary);
        }

        .rank-number {
            width: 50px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .rank-medal {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            flex: 1;
            margin-left: 1rem;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .user-details h6 {
            margin: 0;
            font-weight: 600;
        }

        .user-details small {
            color: #666;
        }

        .points-display {
            text-align: right;
            font-weight: bold;
            color: var(--primary);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            padding: 0.5rem 0;
            z-index: 100;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text);
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .bottom-nav-item:hover {
            background: var(--secondary);
            color: var(--primary);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.1);
        }

        .bottom-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .bottom-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.5rem;
            }
            
            .period-tabs {
                flex-wrap: wrap;
            }
            
            .user-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-trophy"></i> Leaderboard</h1>
        <p>Compete with fellow reciters and earn weekly bonuses</p>
    </div>

    <!-- Period Tabs -->
    <div class="period-tabs">
        <a href="?period=weekly" class="period-tab <?php echo $period === 'weekly' ? 'active' : ''; ?>">
            <i class="fas fa-calendar-week"></i> Weekly
        </a>
        <a href="?period=monthly" class="period-tab <?php echo $period === 'monthly' ? 'active' : ''; ?>">
            <i class="fas fa-calendar-alt"></i> Monthly
        </a>
        <a href="?period=all_time" class="period-tab <?php echo $period === 'all_time' ? 'active' : ''; ?>">
            <i class="fas fa-infinity"></i> All Time
        </a>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- User Stats -->
        <div class="user-stats">
            <div class="user-rank">#<?php echo $userPosition ?? $userRank ?? 'N/A'; ?></div>
            <h5>Your Current Ranking</h5>
            
            <div class="user-stats-grid">
                <div class="stat-item">
                    <div class="stat-value"><?php echo number_format($userPoints['total_points']); ?></div>
                    <div class="stat-label">Total Points</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $userPoints['current_streak']; ?></div>
                    <div class="stat-label">Current Streak</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $userPoints['total_recitations']; ?></div>
                    <div class="stat-label">Recitations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $userPoints['total_referrals']; ?></div>
                    <div class="stat-label">Referrals</div>
                </div>
            </div>
        </div>

        <!-- Leaderboard -->
        <div class="leaderboard">
            <div class="leaderboard-header">
                <h4><i class="fas fa-crown"></i> Top Performers - <?php echo ucfirst(str_replace('_', ' ', $period)); ?></h4>
            </div>
            
            <?php foreach ($topPerformers as $index => $performer): ?>
                <div class="leaderboard-item <?php echo $performer['id'] == $userId ? 'current-user' : ''; ?>">
                    <div class="rank-number">
                        <?php if ($index < 3): ?>
                            <span class="rank-medal">
                                <?php echo $index === 0 ? '🥇' : ($index === 1 ? '🥈' : '🥉'); ?>
                            </span>
                        <?php else: ?>
                            #<?php echo $index + 1; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="user-info">
                        <div class="user-avatar">
                            <?php if (!empty($performer['profile_picture'])): ?>
                                <img src="<?php echo htmlspecialchars($performer['profile_picture']); ?>" 
                                     alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                            <?php else: ?>
                                <?php echo strtoupper(substr($performer['full_name'], 0, 1)); ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="user-details">
                            <h6><?php echo htmlspecialchars($performer['full_name']); ?></h6>
                            <small>
                                <?php echo $performer['total_recitations']; ?> recitations • 
                                Streak: <?php echo $performer['current_streak']; ?>
                            </small>
                        </div>
                    </div>
                    
                    <div class="points-display">
                        <?php echo number_format($period === 'all_time' ? $performer['total_points'] : $performer['period_points']); ?> pts
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <a href="dashboard.php" class="bottom-nav-item">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="streams.php" class="bottom-nav-item">
            <i class="fas fa-video"></i>
            <span>Streams</span>
        </a>

        <a href="community.php" class="bottom-nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="wallet.php" class="bottom-nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="profile.php" class="bottom-nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </nav>
</body>
</html>
