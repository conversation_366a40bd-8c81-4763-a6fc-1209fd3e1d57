<?php
session_start();
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Video Unlock Transactions';
require_once '../components/admin_header.php';

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$user_filter = $_GET['user_filter'] ?? '';

// Build query
$where_conditions = [];
$params = [];
$param_types = '';

if ($status_filter) {
    $where_conditions[] = "vu.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if ($date_from) {
    $where_conditions[] = "DATE(vu.created_at) >= ?";
    $params[] = $date_from;
    $param_types .= 's';
}

if ($date_to) {
    $where_conditions[] = "DATE(vu.created_at) <= ?";
    $params[] = $date_to;
    $param_types .= 's';
}

if ($user_filter) {
    $where_conditions[] = "(u.full_name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$user_filter%";
    $params[] = "%$user_filter%";
    $param_types .= 'ss';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get transactions
$conn = getConnection();
$sql = "SELECT 
            vu.*,
            u.full_name as user_name,
            u.email as user_email,
            c.full_name as creator_name,
            c.email as creator_email
        FROM video_unlocks vu
        LEFT JOIN users u ON vu.user_id = u.id
        LEFT JOIN users c ON vu.creator_id = c.id
        $where_clause
        ORDER BY vu.created_at DESC
        LIMIT 100";

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}
$stmt->execute();
$transactions = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get summary statistics
$summary_sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(unlock_price) as total_revenue,
                    SUM(creator_amount) as total_creator_earnings,
                    SUM(admin_amount) as total_admin_earnings
                FROM video_unlocks
                WHERE status = 'completed'";

$summary_result = $conn->query($summary_sql);
$summary = $summary_result->fetch_assoc();

$conn->close();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-lock me-2"></i>Video Unlock Transactions</h4>
                </div>
                <div class="card-body">
                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>Total Transactions</h5>
                                    <h3><?php echo number_format($summary['total_transactions']); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>Total Revenue</h5>
                                    <h3>₦<?php echo number_format($summary['total_revenue'], 2); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5>Creator Earnings</h5>
                                    <h3>₦<?php echo number_format($summary['total_creator_earnings'], 2); ?></h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>Admin Earnings</h5>
                                    <h3>₦<?php echo number_format($summary['total_admin_earnings'], 2); ?></h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="">All</option>
                                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Date From</label>
                                    <input type="date" name="date_from" class="form-control" value="<?php echo $date_from; ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Date To</label>
                                    <input type="date" name="date_to" class="form-control" value="<?php echo $date_to; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">User Search</label>
                                    <input type="text" name="user_filter" class="form-control" placeholder="Name or Email" value="<?php echo htmlspecialchars($user_filter); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">Filter</button>
                                        <a href="video-unlock-transactions.php" class="btn btn-secondary">Clear</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Transactions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>Recording Type</th>
                                    <th>Recording ID</th>
                                    <th>Creator</th>
                                    <th>Unlock Price</th>
                                    <th>Creator Amount</th>
                                    <th>Admin Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($transactions)): ?>
                                    <tr>
                                        <td colspan="11" class="text-center">No transactions found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($transactions as $transaction): ?>
                                        <tr>
                                            <td><?php echo $transaction['id']; ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($transaction['user_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['user_email']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo ucfirst(str_replace('_', ' ', $transaction['recording_type'])); ?></span>
                                            </td>
                                            <td><?php echo $transaction['recording_id']; ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($transaction['creator_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($transaction['creator_email']); ?></small>
                                                </div>
                                            </td>
                                            <td>₦<?php echo number_format($transaction['unlock_price'], 2); ?></td>
                                            <td>₦<?php echo number_format($transaction['creator_amount'], 2); ?></td>
                                            <td>₦<?php echo number_format($transaction['admin_amount'], 2); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch ($transaction['status']) {
                                                    case 'completed':
                                                        $status_class = 'bg-success';
                                                        break;
                                                    case 'pending':
                                                        $status_class = 'bg-warning';
                                                        break;
                                                    case 'failed':
                                                        $status_class = 'bg-danger';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($transaction['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="viewDetails(<?php echo $transaction['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Transaction Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transactionDetails">
                <!-- Details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
function viewDetails(transactionId) {
    // Load transaction details via AJAX
    fetch(`get-transaction-details.php?id=${transactionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('transactionDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('transactionModal')).show();
            } else {
                alert('Error loading transaction details');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading transaction details');
        });
}
</script>

<?php require_once '../components/admin_footer.php'; ?> 