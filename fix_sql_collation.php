<?php
/**
 * Fix SQL Collation Issues
 * This script fixes the collation issues in the recite_app SQL file
 */

$sqlFile = __DIR__ . '/database/recite_app (1).sql';
$outputFile = __DIR__ . '/database/recite_app_fixed.sql';

if (!file_exists($sqlFile)) {
    die("SQL file not found: $sqlFile\n");
}

echo "Reading SQL file...\n";
$content = file_get_contents($sqlFile);

echo "Fixing collation issues...\n";

// Replace all instances of utf8mb4_0900_ai_ci with utf8mb4_general_ci
$content = str_replace('utf8mb4_0900_ai_ci', 'utf8mb4_general_ci', $content);

// Also replace utf8mb4_unicode_ci with utf8mb4_general_ci for consistency
$content = str_replace('utf8mb4_unicode_ci', 'utf8mb4_general_ci', $content);

// Fix missing AUTO_INCREMENT and PRIMARY KEY definitions
$fixes = [
    // Fix admins table
    'CREATE TABLE `admins` (
  `id` int NOT NULL,' => 'CREATE TABLE `admins` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix admin_logs table  
    'CREATE TABLE `admin_logs` (
  `id` int NOT NULL,' => 'CREATE TABLE `admin_logs` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix call_signals table
    'CREATE TABLE `call_signals` (
  `id` int NOT NULL,' => 'CREATE TABLE `call_signals` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix comments table
    'CREATE TABLE `comments` (
  `id` int NOT NULL,' => 'CREATE TABLE `comments` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix content table
    'CREATE TABLE `content` (
  `id` int NOT NULL,' => 'CREATE TABLE `content` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix content_library table
    'CREATE TABLE `content_library` (
  `id` int NOT NULL,' => 'CREATE TABLE `content_library` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix leaderboards table
    'CREATE TABLE `leaderboards` (
  `id` int NOT NULL,' => 'CREATE TABLE `leaderboards` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix likes table
    'CREATE TABLE `likes` (
  `id` int NOT NULL,' => 'CREATE TABLE `likes` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix mirror_recordings table
    'CREATE TABLE `mirror_recordings` (
  `id` int NOT NULL,' => 'CREATE TABLE `mirror_recordings` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix point_transactions table
    'CREATE TABLE `point_transactions` (
  `id` int NOT NULL,' => 'CREATE TABLE `point_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix rankings table
    'CREATE TABLE `rankings` (
  `id` int NOT NULL,' => 'CREATE TABLE `rankings` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix recitations table
    'CREATE TABLE `recitations` (
  `id` int NOT NULL,' => 'CREATE TABLE `recitations` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix recitation_evaluations table
    'CREATE TABLE `recitation_evaluations` (
  `id` int NOT NULL,' => 'CREATE TABLE `recitation_evaluations` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix referrals table
    'CREATE TABLE `referrals` (
  `id` int NOT NULL,' => 'CREATE TABLE `referrals` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix reports table
    'CREATE TABLE `reports` (
  `id` int NOT NULL,' => 'CREATE TABLE `reports` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix screen_records table
    'CREATE TABLE `screen_records` (
  `id` int NOT NULL,' => 'CREATE TABLE `screen_records` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix streams table
    'CREATE TABLE `streams` (
  `id` int NOT NULL,' => 'CREATE TABLE `streams` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix stream_interactions table
    'CREATE TABLE `stream_interactions` (
  `id` int NOT NULL,' => 'CREATE TABLE `stream_interactions` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix transactions table
    'CREATE TABLE `transactions` (
  `id` int NOT NULL,' => 'CREATE TABLE `transactions` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix unlocked_content table
    'CREATE TABLE `unlocked_content` (
  `id` int NOT NULL,' => 'CREATE TABLE `unlocked_content` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix users table
    'CREATE TABLE `users` (
  `id` int NOT NULL,' => 'CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix user_interactions table
    'CREATE TABLE `user_interactions` (
  `id` int NOT NULL,' => 'CREATE TABLE `user_interactions` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix user_points table
    'CREATE TABLE `user_points` (
  `id` int NOT NULL,' => 'CREATE TABLE `user_points` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix user_stats table
    'CREATE TABLE `user_stats` (
  `id` int NOT NULL,' => 'CREATE TABLE `user_stats` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix user_unlocked_content table
    'CREATE TABLE `user_unlocked_content` (
  `id` int NOT NULL,' => 'CREATE TABLE `user_unlocked_content` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix videos table
    'CREATE TABLE `videos` (
  `id` int NOT NULL,' => 'CREATE TABLE `videos` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix views table
    'CREATE TABLE `views` (
  `id` int NOT NULL,' => 'CREATE TABLE `views` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix wallet_transactions table
    'CREATE TABLE `wallet_transactions` (
  `id` int NOT NULL,' => 'CREATE TABLE `wallet_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix withdrawals table
    'CREATE TABLE `withdrawals` (
  `id` int NOT NULL,' => 'CREATE TABLE `withdrawals` (
  `id` int NOT NULL AUTO_INCREMENT,',
    
    // Fix withdrawal_requests table
    'CREATE TABLE `withdrawal_requests` (
  `id` int NOT NULL,' => 'CREATE TABLE `withdrawal_requests` (
  `id` int NOT NULL AUTO_INCREMENT,'
];

// Apply all fixes
foreach ($fixes as $search => $replace) {
    $content = str_replace($search, $replace, $content);
}

echo "Writing fixed SQL file...\n";
file_put_contents($outputFile, $content);

echo "✅ Fixed SQL file created: $outputFile\n";
echo "✅ All collation issues have been resolved\n";
echo "✅ All tables now use utf8mb4_general_ci collation\n";
echo "✅ All AUTO_INCREMENT and PRIMARY KEY definitions added\n\n";

echo "You can now import the fixed SQL file into your database.\n";
?>
