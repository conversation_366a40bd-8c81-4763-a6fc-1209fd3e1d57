<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SupportAI - 24/7 AI Customer Support Solution</title>
    <meta name="description" content="Transform your customer support with AI that understands, responds and learns. Reduce costs by 70% while improving customer satisfaction.">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='0.9em' font-size='90'%3E🤖%3C/text%3E%3C/svg%3E">
    <style>
        :root {
            --primary: #6366f1;
            --primary-dark: #4f46e5;
            --primary-light: #a5b4fc;
            --secondary: #f59e0b;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --success: #10b981;
            --border: #e2e8f0;
            --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: var(--light);
            overflow-x: hidden;
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        header.scrolled {
            box-shadow: var(--shadow-md);
        }
        
        nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-links a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-links a:hover {
            color: var(--primary);
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-size: 0.95rem;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .btn-outline {
            border: 2px solid var(--primary);
            color: var(--primary);
            background: transparent;
        }
        
        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--dark);
        }
        
        /* Hero Section */
        .hero {
            padding: 120px 0 80px;
            background: var(--gradient);
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E");
            animation: float 6s ease-in-out infinite;
        }
        
        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .hero-text h1 {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .hero-text p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            display: block;
        }
        
        .stat-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }
        
        .hero-cta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .hero-visual {
            position: relative;
        }
        
        .demo-chat {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: var(--shadow-xl);
            max-width: 400px;
            margin: 0 auto;
        }
        
        .chat-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border);
        }
        
        .chat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .chat-status {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            color: var(--success);
        }
        
        .chat-messages {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .message {
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            max-width: 80%;
            animation: fadeInUp 0.3s ease-out;
        }
        
        .message.user {
            background: var(--primary-light);
            color: var(--dark);
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message.bot {
            background: var(--light);
            align-self: flex-start;
        }
        
        /* Features Section */
        .features {
            padding: 80px 0;
            background: white;
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark);
        }
        
        .section-header p {
            font-size: 1.125rem;
            color: var(--gray);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            background: var(--primary);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .feature-card p {
            color: var(--gray);
        }
        
        /* Pricing Section */
        .pricing {
            padding: 80px 0;
            background: var(--light);
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .pricing-card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }
        
        .pricing-card.featured {
            border-color: var(--primary);
            transform: scale(1.05);
        }
        
        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary);
            color: white;
            padding: 0.25rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .plan-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .plan-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        
        .plan-price span {
            font-size: 0.875rem;
            color: var(--gray);
            font-weight: 400;
        }
        
        .plan-features {
            list-style: none;
            margin: 2rem 0;
        }
        
        .plan-features li {
            padding: 0.5rem 0;
            color: var(--gray);
        }
        
        .plan-features li::before {
            content: '✓';
            color: var(--success);
            margin-right: 0.5rem;
        }
        
        /* Testimonials */
        .testimonials {
            padding: 80px 0;
            background: white;
        }
        
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .testimonial-card {
            background: var(--light);
            border-radius: 1rem;
            padding: 2rem;
            position: relative;
        }
        
        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 1rem;
            font-size: 4rem;
            color: var(--primary);
            opacity: 0.3;
        }
        
        .testimonial-text {
            margin-bottom: 1.5rem;
            font-style: italic;
            color: var(--gray);
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .author-info h4 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .author-info p {
            font-size: 0.875rem;
            color: var(--gray);
        }
        
        /* CTA Section */
        .cta {
            padding: 80px 0;
            background: var(--gradient);
            color: white;
            text-align: center;
        }
        
        .cta h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .cta p {
            font-size: 1.125rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Footer */
        footer {
            background: var(--dark);
            color: white;
            padding: 60px 0 20px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .footer-section h3 {
            margin-bottom: 1rem;
            color: white;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 0.5rem;
        }
        
        .footer-section ul li a {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-section ul li a:hover {
            color: white;
        }
        
        .footer-bottom {
            border-top: 1px solid #475569;
            padding-top: 1rem;
            text-align: center;
            color: #cbd5e1;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                flex-direction: column;
                padding: 1rem;
                box-shadow: var(--shadow-lg);
            }
            
            .nav-links.active {
                display: flex;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .hero-stats {
                justify-content: center;
            }
            
            .hero-cta {
                justify-content: center;
            }
            
            .features-grid,
            .pricing-grid,
            .testimonials-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-card.featured {
                transform: none;
            }
        }
        
        /* Smooth scroll */
        html {
            scroll-behavior: smooth;
        }
        
        /* Loading animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
  </head>

  <body>
    <!-- Header -->
    <header id="header">
      <nav class="container">
        <div class="logo">
          <span>🤖</span>
          SupportAI
        </div>
        <ul class="nav-links" id="navLinks">
          <li>
            <a href="#features">Features</a>
          </li>
          <li>
            <a href="#pricing">Pricing</a>
          </li>
          <li>
            <a href="#testimonials">Testimonials</a>
          </li>
          <li>
            <a href="#contact">Contact</a>
          </li>
          <li>
            <a href="#login" class="btn btn-outline">Login</a>
          </li>
          <li>
            <a href="#signup" class="btn btn-primary">Start Free Trial</a>
          </li>
        </ul>
        <button class="mobile-menu-btn" id="mobileMenuBtn">☰</button>
      </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <div class="hero-content">
          <div class="hero-text">
            <h1>AI Customer Support That Never Sleeps</h1>
            <p>Transform your customer support with AI that understands, responds, and learns. Reduce support costs by 70% while improving customer satisfaction 24/7.</p>

            <div class="hero-stats">
              <div class="stat">
                <span class="stat-number">98%</span>
                <span class="stat-label">Customer Satisfaction</span>
              </div>
              <div class="stat">
                <span class="stat-number">2s</span>
                <span class="stat-label">Average Response Time</span>
              </div>
              <div class="stat">
                <span class="stat-number">70%</span>
                <span class="stat-label">Cost Reduction</span>
              </div>
            </div>

            <div class="hero-cta">
              <a href="#pricing" class="btn btn-primary">Start Free Trial</a>
              <a href="#demo" class="btn btn-outline" style="background: white; color: var(--primary);">Watch Demo</a>
            </div>
          </div>

          <div class="hero-visual">
            <div class="demo-chat">
              <div class="chat-header">
                <div class="chat-avatar">AI</div>
                <div>
                  <h4>SupportAI Assistant</h4>
                  <div class="chat-status">
                    <span style="display: inline-block; width: 8px; height: 8px; background: var(--success); border-radius: 50%; margin-right: 0.25rem;"></span>
                    Online & Ready
                  </div>
                </div>
              </div>
              <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                  Hello! I'm SupportAI. How can I help you today?
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
      <div class="container">
        <div class="section-header">
          <h2>Everything You Need for Exceptional Support</h2>
          <p>Powerful features designed to scale your customer support without scaling your team</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🧠</div>
            <h3>Smart AI Understanding</h3>
            <p>Advanced natural language processing that understands context, sentiment, and intent across 100+ languages</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">⚡</div>
            <h3>Instant Responses</h3>
            <p>Sub-second response times with 99.9% uptime guarantee. Your customers get help instantly, every time</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🔗</div>
            <h3>Seamless Integration</h3>
            <p>Connect with 50+ platforms including Zendesk, Shopify, Slack, and custom APIs in minutes</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>Advanced Analytics</h3>
            <p>Track customer satisfaction, resolution rates, and agent performance with real-time dashboards</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>Human Handoff</h3>
            <p>Seamlessly escalate complex issues to human agents with full context and conversation history</p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🔄</div>
            <h3>Continuous Learning</h3>
            <p>AI learns from every interaction, improving responses and handling new scenarios automatically</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
      <div class="container">
        <div class="section-header">
          <h2>Simple, Transparent Pricing</h2>
          <p>Start free, scale as you grow. No hidden fees or long-term contracts.</p>
        </div>

        <div class="pricing-grid">
          <div class="pricing-card">
            <h3 class="plan-name">Starter</h3>
            <div class="plan-price">$0<span>/month</span></div>
            <ul class="plan-features">
              <li>Up to 100 conversations/month</li>
              <li>Basic AI responses</li>
              <li>Email support</li>
              <li>1 integration</li>
              <li>Basic analytics</li>
            </ul>
            <a href="#signup" class="btn btn-outline">Get Started</a>
          </div>

          <div class="pricing-card featured">
            <h3 class="plan-name">Professional</h3>
            <div class="plan-price">$99<span>/month</span></div>
            <ul class="plan-features">
              <li>Up to 5,000 conversations/month</li>
              <li>Advanced AI with learning</li>
              <li>Priority support</li>
              <li>Unlimited integrations</li>
              <li>Advanced analytics & API</li>
              <li>Human handoff</li>
            </ul>
            <a href="#signup" class="btn btn-primary">Start Free Trial</a>
          </div>

          <div class="pricing-card">
            <h3 class="plan-name">Enterprise</h3>
            <div class="plan-price">Custom</div>
            <ul class="plan-features">
              <li>Unlimited conversations</li>
              <li>Custom AI training</li>
              <li>Dedicated support team</li>
              <li>Custom integrations</li>
              <li>White-label options</li>
              <li>SLA guarantee</li>
              <li>Advanced security</li>
            </ul>
            <a href="#contact" class="btn btn-outline">Contact Sales</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials" id="testimonials">
      <div class="container">
        <div class="section-header">
          <h2>Trusted by 10,000+ Businesses</h2>
          <p>See how companies are transforming their customer support with AI</p>
        </div>

        <div class="testimonials-grid">
          <div class="testimonial-card">
            <p class="testimonial-text">
              "SupportAI reduced our support tickets by 65% while improving customer satisfaction scores. The integration was seamless and the AI learns incredibly fast."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar">SJ</div>
              <div class="author-info">
                <h4>Sarah Johnson</h4>
                <p>Head of Support, TechFlow Inc.</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <p class="testimonial-text">
              "We went from 24-hour response times to instant answers. Our customers love the 24/7 availability and our team can focus on complex issues."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar">MR</div>
              <div class="author-info">
                <h4>Mike Rodriguez</h4>
                <p>CEO, ShopMart</p>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <p class="testimonial-text">
              "The ROI was incredible - we saved $50K in the first quarter alone. The AI handles 80% of queries perfectly and escalates the rest intelligently."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar">EL</div>
              <div class="author-info">
                <h4>Emily Lee</h4>
                <p>Support Manager, CloudBase</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="container">
        <h2>Ready to Transform Your Support?</h2>
        <p>Join thousands of businesses already using AI to deliver exceptional customer support. Start your free trial today - no credit card required.</p>
        <a href="#signup" class="btn" style="background: white; color: var(--primary); font-size: 1.125rem; padding: 1rem 2rem;">Start Your Free 14-Day Trial</a>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>SupportAI</h3>
            <p>Transform your customer support with AI that understands, responds, and learns 24/7.</p>
          </div>

          <div class="footer-section">
            <h3>Product</h3>
            <ul>
              <li>
                <a href="#features">Features</a>
              </li>
              <li>
                <a href="#pricing">Pricing</a>
              </li>
              <li>
                <a href="#integrations">Integrations</a>
              </li>
              <li>
                <a href="#api">API</a>
              </li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Company</h3>
            <ul>
              <li>
                <a href="#about">About</a>
              </li>
              <li>
                <a href="#blog">Blog</a>
              </li>
              <li>
                <a href="#careers">Careers</a>
              </li>
              <li>
                <a href="#contact">Contact</a>
              </li>
            </ul>
          </div>

          <div class="footer-section">
            <h3>Support</h3>
            <ul>
              <li>
                <a href="#help">Help Center</a>
              </li>
              <li>
                <a href="#docs">Documentation</a>
              </li>
              <li>
                <a href="#status">Status</a>
              </li>
              <li>
                <a href="#contact">Contact Us</a>
              </li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 SupportAI. All rights reserved. Built with ❤️ for better customer support.</p>
        </div>
      </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const navLinks = document.getElementById('navLinks');
        
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.classList.toggle('active');
            mobileMenuBtn.textContent = navLinks.classList.contains('active') ? '✕' : '☰';
        });

        // Close mobile menu when clicking a link
        const navLinksList = navLinks.querySelectorAll('a');
        navLinksList.forEach(link => {
            link.addEventListener('click', () => {
                navLinks.classList.remove('active');
                mobileMenuBtn.textContent = '☰';
            });
        });

        // Demo chat functionality
        const chatMessages = document.getElementById('chatMessages');
        const demoMessages = [
            { type: 'user', text: 'I need help with my order #12345' },
            { type: 'bot', text: "I'd be happy to help! Looking up order #12345... Your order was shipped yesterday via FedEx. Tracking: 1Z999AA10123456784. Expected delivery: Tomorrow by 8 PM." },
            { type: 'user', text: 'Can I change the delivery address?' },
            { type: 'bot', text: "I can help change your delivery address. The package hasn't been picked up yet, so we can update it. What's the new address?" },
            { type: 'user', text: '123 New Street, New York, NY 10001' },
            { type: 'bot', text: "Perfect! I've updated your delivery address to 123 New Street, New York, NY 10001. You'll receive a confirmation email shortly. Is there anything else I can help with?" }
        ];

        let messageIndex = 0;

        function typeMessage(message, element) {
            const text = message.text;
            let index = 0;
            
            const typingInterval = setInterval(() => {
                element.textContent += text[index];
                index++;
                
                if (index >= text.length) {
                    clearInterval(typingInterval);
                    
                    // Show next message after delay
                    setTimeout(() => {
                        messageIndex++;
                        if (messageIndex < demoMessages.length) {
                            addMessage(demoMessages[messageIndex]);
                        }
                    }, 1500);
                }
            }, 30);
        }

        function addMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.type}`;
            
            if (message.type === 'bot') {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message bot';
                loadingDiv.innerHTML = '<span class="loading"></span>';
                chatMessages.appendChild(loadingDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                setTimeout(() => {
                    loadingDiv.remove();
                    chatMessages.appendChild(messageDiv);
                    typeMessage(message, messageDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 1000);
            } else {
                messageDiv.textContent = message.text;
                chatMessages.appendChild(messageDiv);
                chatMessages.scrollTop = chatMessages.scrollHeight;
                
                setTimeout(() => {
                    messageIndex++;
                    if (messageIndex < demoMessages.length) {
                        addMessage(demoMessages[messageIndex]);
                    }
                }, 1000);
            }
        }

        // Start demo after 2 seconds
        setTimeout(() => {
            addMessage(demoMessages[0]);
        }, 2000);

        // Reset demo every 30 seconds
        setInterval(() => {
            chatMessages.innerHTML = `
                <div class="message bot">
                    Hello! I'm SupportAI. How can I help you today?
                </div>
            `;
            messageIndex = 0;
            setTimeout(() => {
                addMessage(demoMessages[0]);
            }, 2000);
        }, 30000);

        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fadeInUp');
                }
            });
        }, observerOptions);

        // Observe elements
        document.querySelectorAll('.feature-card, .pricing-card, .testimonial-card').forEach(el => {
            observer.observe(el);
        });
    </script>
  </body>

</html>