<?php
/**
 * Test Connection Fixes
 * Verify that all mysqli connection issues are resolved
 */
require_once 'config/db_config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Connection Fixes Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Connection Fixes Verification</h2>";

// Test 1: Basic executeQuery functionality
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: Basic executeQuery Functionality</h5>";
try {
    $result = executeQuery("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ SELECT query works: Found $count users<br>";
    } else {
        echo "❌ SELECT query failed<br>";
    }
    
    // Test INSERT/UPDATE/DELETE return format
    $testEmail = 'connection_test_' . time() . '@example.com';
    $insertResult = executeQuery(
        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
        'ssssss',
        ['Connection Test User', $testEmail, password_hash('test123', PASSWORD_DEFAULT), 'Test Ward', 'Test LGA', 'Test State']
    );
    
    if ($insertResult && $insertResult->success && $insertResult->insert_id > 0) {
        $testUserId = $insertResult->insert_id;
        echo "✅ INSERT query works: Created user with ID $testUserId<br>";
        
        // Test UPDATE
        $updateResult = executeQuery("UPDATE users SET full_name = ? WHERE id = ?", 'si', ['Updated Test User', $testUserId]);
        if ($updateResult && $updateResult->success) {
            echo "✅ UPDATE query works: Updated user<br>";
        } else {
            echo "❌ UPDATE query failed<br>";
        }
        
        // Clean up
        $deleteResult = executeQuery("DELETE FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($deleteResult && $deleteResult->success) {
            echo "✅ DELETE query works: Cleaned up test user<br>";
        } else {
            echo "❌ DELETE query failed<br>";
        }
    } else {
        echo "❌ INSERT query failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ executeQuery test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 2: Multiple rapid queries (stress test)
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: Multiple Rapid Queries (No Connection Conflicts)</h5>";
try {
    $successCount = 0;
    $totalQueries = 20;
    
    for ($i = 1; $i <= $totalQueries; $i++) {
        $result = executeQuery("SELECT ? as query_number, COUNT(*) as user_count FROM users", 'i', [$i]);
        if ($result) {
            $successCount++;
        }
    }
    
    echo "✅ Rapid query test: $successCount/$totalQueries queries successful<br>";
    
    if ($successCount == $totalQueries) {
        echo "✅ Perfect! No connection conflicts detected<br>";
    } else {
        echo "⚠️ Some queries failed - may indicate remaining issues<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Rapid query test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Table existence checks (like in videos.php)
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: Table Existence Checks (Videos.php Style)</h5>";
try {
    $tablesExist = [];
    $checkTables = ['users', 'videos', 'withdrawal_requests', 'transactions'];
    
    foreach ($checkTables as $table) {
        try {
            $result = executeQuery("SHOW TABLES LIKE ?", 's', [$table]);
            $tablesExist[$table] = ($result && $result->num_rows > 0);
            echo ($tablesExist[$table] ? "✅" : "⚠️") . " Table '$table': " . ($tablesExist[$table] ? "exists" : "not found") . "<br>";
        } catch (Exception $e) {
            $tablesExist[$table] = false;
            echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Table existence test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 4: Complex JOIN queries (like in payouts.php)
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: Complex JOIN Queries (Payouts.php Style)</h5>";
try {
    // Test withdrawal requests query
    $result = executeQuery("
        SELECT w.*, u.full_name as username, u.email
        FROM withdrawal_requests w
        JOIN users u ON w.user_id = u.id
        WHERE w.status = ?
        ORDER BY w.created_at DESC
        LIMIT 5
    ", 's', ['pending']);
    
    if ($result) {
        $count = $result->num_rows;
        echo "✅ Withdrawal requests JOIN query: Found $count pending requests<br>";
    } else {
        echo "⚠️ Withdrawal requests table may not exist or be empty<br>";
    }
    
    // Test transactions query
    $transactionResult = executeQuery("
        SELECT t.id, t.user_id, t.amount, t.description, t.status, t.created_at,
               u.full_name as username, u.email
        FROM transactions t
        JOIN users u ON t.user_id = u.id
        WHERE t.transaction_type = 'withdrawal'
        ORDER BY t.created_at DESC
        LIMIT 5
    ");
    
    if ($transactionResult) {
        $count = $transactionResult->num_rows;
        echo "✅ Transactions JOIN query: Found $count withdrawal transactions<br>";
    } else {
        echo "⚠️ Transactions table may not exist or be empty<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Complex JOIN test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 5: Function redeclaration check
echo "<div class='alert alert-info'>";
echo "<h5>Test 5: Function Redeclaration Prevention</h5>";
if (function_exists('processWithdrawalRequest')) {
    echo "✅ processWithdrawalRequest function exists<br>";
    echo "✅ No redeclaration errors - function_exists check working<br>";
} else {
    echo "⚠️ processWithdrawalRequest function not found (may be normal)<br>";
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='admin/videos.php' class='btn btn-success btn-lg me-2'>
                            <i class='fas fa-video me-2'></i>Test Videos Page
                        </a>
                        <a href='admin/payouts.php' class='btn btn-primary btn-lg me-2'>
                            <i class='fas fa-money-bill me-2'></i>Test Payouts Page
                        </a>
                        <a href='admin/dashboard.php' class='btn btn-warning btn-lg'>
                            <i class='fas fa-tachometer-alt me-2'></i>Admin Dashboard
                        </a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>Connection Fixes Applied:</h6>
                        <ul class='small'>
                            <li>✅ Replaced all direct \$conn usage with executeQuery()</li>
                            <li>✅ Fixed INSERT/UPDATE/DELETE return format handling</li>
                            <li>✅ Added function_exists checks to prevent redeclaration</li>
                            <li>✅ Removed connection closing statements (handled internally)</li>
                            <li>✅ Added proper error handling for all database operations</li>
                            <li>✅ Fixed table existence checks with parameterized queries</li>
                        </ul>
                        
                        <div class='alert alert-success mt-3'>
                            <strong>Expected Result:</strong> All tests should pass with green checkmarks. 
                            No more 'mysqli object is already closed' errors should occur.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
