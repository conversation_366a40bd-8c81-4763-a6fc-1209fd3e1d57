<?php
/**
 * Test Functions Script
 * Tests if all functions are working correctly after centralization
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Test Functions - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='test-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-vial me-2'></i>Function Tests</h1>
                        <p class='text-muted'>Testing centralized database configuration functions</p>
                    </div>";

$tests = [];
$errors = [];

// Test 1: Load configuration
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-cog me-2'></i>Test 1: Loading Configuration</h6>";

try {
    require_once 'config/db_config.php';
    echo "<p class='mb-0'>✅ Configuration loaded successfully</p>";
    $tests['config'] = true;
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Configuration failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    $tests['config'] = false;
    $errors[] = $e->getMessage();
}

echo "</div>";

// Test 2: Database connection
echo "<div class='test-result " . ($tests['config'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-database me-2'></i>Test 2: Database Connection</h6>";

if ($tests['config']) {
    try {
        $conn = getConnection();
        echo "<p class='mb-0'>✅ Database connection successful</p>";
        $tests['database'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['database'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (config failed)</p>";
    $tests['database'] = false;
}

echo "</div>";

// Test 3: Essential functions
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-function me-2'></i>Test 3: Essential Functions</h6>";

$functions = ['isLoggedIn', 'requireLogin', 'isAdmin', 'generateCSRFToken', 'validateCSRFToken', 'sanitize', 'logError', 'timeAgo', 'validateEmail', 'generateReferralCode', 'formatMoney', 'uploadFile'];

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p class='mb-0'>✅ $func() - Available</p>";
    } else {
        echo "<p class='mb-0'>❌ $func() - Missing</p>";
        $errors[] = "Function $func() not found";
    }
}

echo "</div>";

// Test 4: Session management
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-user-clock me-2'></i>Test 4: Session Management</h6>";

if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p class='mb-0'>✅ Session is active</p>";
    $tests['session'] = true;
} else {
    echo "<p class='mb-0'>⚠️ Session not active (normal for test script)</p>";
    $tests['session'] = true; // This is normal for a test script
}

echo "</div>";

// Test 5: Constants
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-constants me-2'></i>Test 5: Configuration Constants</h6>";

$constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS', 'BASE_URL', 'SITE_URL', 'PAYSTACK_PUBLIC_KEY', 'PAYSTACK_SECRET_KEY'];

foreach ($constants as $const) {
    if (defined($const)) {
        $value = constant($const);
        if ($const === 'DB_PASS') {
            $displayValue = empty($value) ? '[EMPTY]' : '[SET]';
        } else {
            $displayValue = htmlspecialchars($value);
        }
        echo "<p class='mb-0'>✅ $const: $displayValue</p>";
    } else {
        echo "<p class='mb-0'>❌ $const - Not defined</p>";
        $errors[] = "Constant $const not defined";
    }
}

echo "</div>";

// Test 6: Utility function tests
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-tools me-2'></i>Test 6: Utility Function Tests</h6>";

try {
    // Test formatMoney
    $money = formatMoney(1000);
    echo "<p class='mb-0'>✅ formatMoney(1000) = $money</p>";
    
    // Test timeAgo
    $timeAgo = timeAgo(date('Y-m-d H:i:s', strtotime('-1 hour')));
    echo "<p class='mb-0'>✅ timeAgo(1 hour ago) = $timeAgo</p>";
    
    // Test validateEmail
    $emailValid = validateEmail('<EMAIL>') ? 'Valid' : 'Invalid';
    echo "<p class='mb-0'>✅ validateEmail('<EMAIL>') = $emailValid</p>";
    
    // Test generateReferralCode
    $refCode = generateReferralCode();
    echo "<p class='mb-0'>✅ generateReferralCode() = $refCode</p>";
    
    // Test sanitize
    $sanitized = sanitize('<script>alert("test")</script>');
    echo "<p class='mb-0'>✅ sanitize() working (HTML escaped)</p>";
    
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Utility function error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $errors[] = $e->getMessage();
}

echo "</div>";

// Summary
$totalTests = count($tests);
$passedTests = count(array_filter($tests));

if (empty($errors)) {
    echo "<div class='alert alert-success'>
            <h5><i class='fas fa-check-circle me-2'></i>All Tests Passed!</h5>
            <p class='mb-3'>The centralized database configuration is working correctly.</p>
            <div class='text-center'>
                <a href='login.php' class='btn btn-success btn-lg me-2'>
                    <i class='fas fa-sign-in-alt me-2'></i>Test Login Page
                </a>
                <a href='admin/login.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-user-shield me-2'></i>Test Admin Login
                </a>
            </div>
          </div>";
} else {
    echo "<div class='alert alert-warning'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Some Issues Found</h5>
            <p>Tests passed: $passedTests/$totalTests</p>
            <h6>Errors:</h6>
            <ul class='mb-0'>";
    
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    
    echo "            </ul>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            Delete this test file after confirming everything works.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
