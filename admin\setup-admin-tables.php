<?php
/**
 * Setup Admin Tables
 * Creates all necessary tables for admin functionality
 */

require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';
$results = [];

try {
    $conn = getConnection();
    
    // Create admin_logs table
    $adminLogsTable = "
        CREATE TABLE IF NOT EXISTS `admin_logs` (
            `id` int NOT NULL AUTO_INCREMENT,
            `admin_username` varchar(100) DEFAULT NULL,
            `action` varchar(255) DEFAULT NULL,
            `target_user_id` int DEFAULT NULL,
            `details` text,
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_admin_username` (`admin_username`),
            <PERSON>EY `idx_created_at` (`created_at`),
            KEY `idx_target_user_id` (`target_user_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($adminLogsTable)) {
        $results[] = "✅ Admin logs table created/verified";
    } else {
        $results[] = "❌ Failed to create admin logs table: " . $conn->error;
    }
    
    // Create withdrawal_requests table if not exists
    $withdrawalRequestsTable = "
        CREATE TABLE IF NOT EXISTS `withdrawal_requests` (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `bank_name` varchar(100) NOT NULL,
            `account_number` varchar(20) NOT NULL,
            `account_name` varchar(100) NOT NULL,
            `status` enum('pending','approved','rejected') DEFAULT 'pending',
            `admin_notes` text,
            `processed_by` varchar(100) DEFAULT NULL,
            `processed_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($withdrawalRequestsTable)) {
        $results[] = "✅ Withdrawal requests table created/verified";
    } else {
        $results[] = "❌ Failed to create withdrawal requests table: " . $conn->error;
    }
    
    // Create reports table if not exists
    $reportsTable = "
        CREATE TABLE IF NOT EXISTS `reports` (
            `id` int NOT NULL AUTO_INCREMENT,
            `reporter_id` int NOT NULL,
            `reported_user_id` int DEFAULT NULL,
            `content_type` enum('user','recitation','stream','comment') NOT NULL,
            `content_id` int DEFAULT NULL,
            `reason` varchar(255) NOT NULL,
            `description` text,
            `status` enum('pending','reviewed','resolved','dismissed') DEFAULT 'pending',
            `admin_notes` text,
            `processed_by` varchar(100) DEFAULT NULL,
            `processed_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_reporter_id` (`reporter_id`),
            KEY `idx_reported_user_id` (`reported_user_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`reporter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($reportsTable)) {
        $results[] = "✅ Reports table created/verified";
    } else {
        $results[] = "❌ Failed to create reports table: " . $conn->error;
    }
    
    // Add status column to existing tables if not exists
    $alterQueries = [
        "ALTER TABLE `recitations` ADD COLUMN IF NOT EXISTS `status` enum('pending','approved','rejected','flagged','deleted') DEFAULT 'approved'",
        "ALTER TABLE `streams` ADD COLUMN IF NOT EXISTS `status` enum('pending','approved','rejected','flagged','deleted') DEFAULT 'approved'",
        "ALTER TABLE `comments` ADD COLUMN IF NOT EXISTS `status` enum('pending','approved','rejected','flagged','deleted') DEFAULT 'approved'",
        "ALTER TABLE `users` ADD COLUMN IF NOT EXISTS `status` varchar(20) DEFAULT NULL",
        "ALTER TABLE `recitations` ADD COLUMN IF NOT EXISTS `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "ALTER TABLE `streams` ADD COLUMN IF NOT EXISTS `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "ALTER TABLE `comments` ADD COLUMN IF NOT EXISTS `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    foreach ($alterQueries as $query) {
        if ($conn->query($query)) {
            $results[] = "✅ Table structure updated";
        } else {
            // Ignore errors for columns that already exist
            if (strpos($conn->error, 'Duplicate column name') === false && strpos($conn->error, 'check that column') === false) {
                $results[] = "⚠️ Table update warning: " . $conn->error;
            }
        }
    }
    
    // Create wallet_transactions table if not exists
    $walletTransactionsTable = "
        CREATE TABLE IF NOT EXISTS `wallet_transactions` (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NOT NULL,
            `transaction_type` enum('credit','debit') NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `description` varchar(255) NOT NULL,
            `reference` varchar(100) DEFAULT NULL,
            `status` enum('pending','completed','failed') DEFAULT 'completed',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_transaction_type` (`transaction_type`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($walletTransactionsTable)) {
        $results[] = "✅ Wallet transactions table created/verified";
    } else {
        $results[] = "❌ Failed to create wallet transactions table: " . $conn->error;
    }
    
    // Log this admin action
    logAdminAction($_SESSION['admin_username'] ?? 'admin', 'SETUP_TABLES', null, 'Admin tables setup completed');
    
    $message = 'Admin tables setup completed successfully!';
    $messageType = 'success';
    
    $conn->close();
    
} catch (Exception $e) {
    logError("Admin tables setup error: " . $e->getMessage());
    $message = 'Error setting up admin tables: ' . $e->getMessage();
    $messageType = 'danger';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Admin Tables - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .setup-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
        }
        .result-item {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #28a745;
        }
        .result-item.warning {
            border-left-color: #ffc107;
        }
        .result-item.error {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="setup-card p-5">
                    <div class="text-center mb-4">
                        <h1 class="text-primary">
                            <i class="fas fa-database me-2"></i>Admin Tables Setup
                        </h1>
                        <p class="text-muted">Setting up database tables for admin functionality</p>
                    </div>
                    
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-list-check me-2"></i>Setup Results
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($results)): ?>
                                <?php foreach ($results as $result): ?>
                                <div class="result-item <?php echo strpos($result, '❌') !== false ? 'error' : (strpos($result, '⚠️') !== false ? 'warning' : ''); ?>">
                                    <?php echo $result; ?>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">No results to display.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-info-circle me-2 text-info"></i>Tables Created/Verified:</h6>
                                <ul class="mb-0">
                                    <li><strong>admin_logs</strong> - Tracks all admin actions</li>
                                    <li><strong>withdrawal_requests</strong> - Manages user withdrawal requests</li>
                                    <li><strong>reports</strong> - Handles content and user reports</li>
                                    <li><strong>wallet_transactions</strong> - Tracks wallet transactions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-plus-circle me-2 text-success"></i>Columns Added:</h6>
                                <ul class="mb-0">
                                    <li><strong>status</strong> - Added to recitations, streams, comments, users tables</li>
                                    <li><strong>updated_at</strong> - Added to content tables for tracking modifications</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="dashboard.php" class="btn btn-primary btn-lg me-2">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                        <a href="user-management.php" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-users-cog me-2"></i>User Management
                        </a>
                        <a href="withdrawal-requests.php" class="btn btn-warning btn-lg">
                            <i class="fas fa-money-bill-wave me-2"></i>Withdrawal Requests
                        </a>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            <strong>Security Note:</strong> Delete this setup file after successful completion.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
