<?php
/**
 * Simple test for withdrawal approval functionality
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Simple Approval Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Simple Approval Test</h2>";

// Handle test approval
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_approve'])) {
    $requestId = intval($_POST['request_id']);
    $adminNotes = "Test approval from simple test script";
    
    echo "<div class='alert alert-info'>Testing approval for request ID: $requestId</div>";
    
    try {
        $conn = getConnection();
        if (!$conn) {
            throw new Exception("Database connection failed");
        }
        
        // Get the withdrawal request
        $stmt = $conn->prepare("
            SELECT wr.*, u.full_name, u.email, u.wallet_balance 
            FROM withdrawal_requests wr 
            JOIN users u ON wr.user_id = u.id 
            WHERE wr.id = ? AND wr.status = 'pending'
        ");
        $stmt->bind_param("i", $requestId);
        $stmt->execute();
        $result = $stmt->get_result();
        $request = $result->fetch_assoc();
        $stmt->close();
        
        if ($request) {
            echo "<div class='alert alert-success'>✅ Found withdrawal request:</div>";
            echo "<ul>";
            echo "<li>ID: {$request['id']}</li>";
            echo "<li>User: {$request['full_name']}</li>";
            echo "<li>Amount: ₦{$request['amount']}</li>";
            echo "<li>Status: {$request['status']}</li>";
            echo "</ul>";
            
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Update withdrawal request
                $combinedNotes = "Processed by: " . ($_SESSION['admin_username'] ?? 'admin') . "\n" . $adminNotes;
                $updateStmt = $conn->prepare("
                    UPDATE withdrawal_requests 
                    SET status = 'approved', admin_notes = ?, processed_at = NOW() 
                    WHERE id = ? AND status = 'pending'
                ");
                $updateStmt->bind_param("si", $combinedNotes, $requestId);
                
                if ($updateStmt->execute()) {
                    $affectedRows = $updateStmt->affected_rows;
                    echo "<div class='alert alert-success'>✅ Update executed. Affected rows: $affectedRows</div>";
                    
                    if ($affectedRows > 0) {
                        $conn->commit();
                        echo "<div class='alert alert-success'>✅ Withdrawal request approved successfully!</div>";
                    } else {
                        $conn->rollback();
                        echo "<div class='alert alert-warning'>⚠️ No rows were updated. Request may have already been processed.</div>";
                    }
                } else {
                    $conn->rollback();
                    echo "<div class='alert alert-danger'>❌ Failed to execute update: " . $updateStmt->error . "</div>";
                }
                $updateStmt->close();
                
            } catch (Exception $e) {
                $conn->rollback();
                echo "<div class='alert alert-danger'>❌ Transaction error: " . $e->getMessage() . "</div>";
            }
            
        } else {
            echo "<div class='alert alert-warning'>⚠️ No pending withdrawal request found with ID: $requestId</div>";
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
    }
}

// Show available pending withdrawals
try {
    $conn = getConnection();
    $result = $conn->query("
        SELECT wr.id, wr.user_id, wr.amount, wr.status, wr.created_at, u.full_name 
        FROM withdrawal_requests wr 
        JOIN users u ON wr.user_id = u.id 
        WHERE wr.status = 'pending' 
        ORDER BY wr.created_at DESC 
        LIMIT 10
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<h5>Available Pending Withdrawals:</h5>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>ID</th><th>User</th><th>Amount</th><th>Date</th><th>Action</th></tr></thead>";
        echo "<tbody>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>#{$row['id']}</td>";
            echo "<td>{$row['full_name']}</td>";
            echo "<td>₦" . number_format($row['amount'], 2) . "</td>";
            echo "<td>" . date('M j, Y', strtotime($row['created_at'])) . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='request_id' value='{$row['id']}'>";
            echo "<button type='submit' name='test_approve' class='btn btn-success btn-sm'>Test Approve</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>No pending withdrawal requests found.</div>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error fetching withdrawals: " . $e->getMessage() . "</div>";
}

echo "                <div class='text-center mt-4'>
                        <a href='payouts.php' class='btn btn-primary'>Back to Payouts</a>
                        <a href='dashboard.php' class='btn btn-secondary'>Dashboard</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
