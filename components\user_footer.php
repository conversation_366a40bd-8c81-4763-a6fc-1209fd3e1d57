        </div> <!-- End content-wrapper -->
    </div> <!-- End main-content -->
    </div> <!-- End app-layout -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script> 
    <script src="<?php echo BASE_URL; ?>/assets/js/main.js"></script>
    <script src="<?php echo BASE_URL; ?>/assets/js/dashboard.js"></script>
    
    <script>
    // Mobile Menu Toggle - Immediate execution
    (function() {
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const userSidebar = document.getElementById('userSidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (mobileMenuBtn && userSidebar && sidebarOverlay) {
            // Toggle sidebar on mobile menu button click
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('Mobile menu clicked'); // Debug
                
                userSidebar.classList.toggle('active');
                sidebarOverlay.classList.toggle('active');
                
                // Toggle hamburger icon
                const icon = mobileMenuBtn.querySelector('i');
                if (userSidebar.classList.contains('active')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            });
            
            // Close sidebar when clicking overlay
            sidebarOverlay.addEventListener('click', function() {
                userSidebar.classList.remove('active');
                sidebarOverlay.classList.remove('active');
                
                // Reset hamburger icon
                const icon = mobileMenuBtn.querySelector('i');
                icon.className = 'fas fa-bars';
            });
            
            // Close sidebar when clicking nav links on mobile
            const navLinks = userSidebar.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        userSidebar.classList.remove('active');
                        sidebarOverlay.classList.remove('active');
                        
                        // Reset hamburger icon
                        const icon = mobileMenuBtn.querySelector('i');
                        icon.className = 'fas fa-bars';
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    userSidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                    
                    // Reset hamburger icon
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.className = 'fas fa-bars';
                }
            });
        } else {
            console.error('Mobile menu elements not found');
        }
    })();
    
    document.addEventListener('DOMContentLoaded', function() {
        const dashboard = document.querySelector('.interactive-dashboard');
        if (dashboard) {
            // Camera Logic
            const mirrorVideo = document.getElementById('mirrorVideo');
            const startCameraBtn = document.getElementById('startCameraBtn');
            const cameraPlaceholder = document.getElementById('cameraPlaceholder');
            const startRecitationBtn = document.getElementById('startRecitationBtn');

            if (startCameraBtn) {
                startCameraBtn.addEventListener('click', async () => {
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
                        mirrorVideo.srcObject = stream;
                        cameraPlaceholder.style.display = 'none';
                        mirrorVideo.style.display = 'block';
                        startRecitationBtn.disabled = false;
                    } catch (err) {
                        console.error("Error accessing camera: ", err);
                        cameraPlaceholder.innerHTML = '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Could not access camera. Please check permissions.</p>';
                    }
                });
            }

            // Recitation Logic
            const transcriptText = dashboard.dataset.transcript.trim();
            const transcriptContainer = document.getElementById('transcript-container');
            const recitationStatus = document.getElementById('recitation-status');
            let originalWords = [];

            if (transcriptText) {
                originalWords = transcriptText.split(/\s+/);
                transcriptContainer.innerHTML = originalWords.map(word => `<span>${word}</span>`).join(' ');
            } else {
                transcriptContainer.innerHTML = '<p class="text-muted">No transcript available for this video.</p>';
                startRecitationBtn.disabled = true;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (SpeechRecognition) {
                const recognition = new SpeechRecognition();
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'ar-SA'; // Assuming Arabic for Qur'an. Change if needed.

                recognition.onstart = () => {
                    recitationStatus.style.display = 'block';
                };

                recognition.onend = () => {
                    recitationStatus.style.display = 'none';
                    startRecitationBtn.innerHTML = '<i class="fas fa-play-circle me-2"></i>Restart Reciting';
                };

                recognition.onresult = (event) => {
                    let interimTranscript = '';
                    let finalTranscript = '';
                    for (let i = event.resultIndex; i < event.results.length; ++i) {
                        if (event.results[i].isFinal) {
                            finalTranscript += event.results[i][0].transcript;
                        } else {
                            interimTranscript += event.results[i][0].transcript;
                        }
                    }
                    
                    const spokenWords = (finalTranscript + interimTranscript).trim().split(/\s+/);
                    updateHighlights(spokenWords);
                };

                startRecitationBtn.addEventListener('click', () => {
                    recognition.start();
                    startRecitationBtn.innerHTML = '<i class="fas fa-stop-circle me-2"></i>Stop Reciting';
                });

            } else {
                transcriptContainer.innerHTML = '<p class="text-danger">Speech recognition not supported in this browser.</p>';
            }

            function updateHighlights(spokenWords) {
                const transcriptSpans = transcriptContainer.querySelectorAll('span');
                transcriptSpans.forEach((span, index) => {
                    if (index < spokenWords.length) {
                        // Normalize words for comparison (remove punctuation, etc.)
                        const originalWord = span.textContent.toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g,"");
                        const spokenWord = spokenWords[index].toLowerCase().replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g,"");
                        
                        if (originalWord === spokenWord) {
                            span.className = 'correct';
                        } else {
                            span.className = 'incorrect';
                        }
                    } else {
                        span.className = '';
                    }
                });
            }
        }
    });
    </script>
</body>
</html> 