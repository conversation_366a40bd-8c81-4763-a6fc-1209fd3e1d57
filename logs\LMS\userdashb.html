<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - EduFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar { transition: all 0.3s ease; }
        .content-area { margin-left: 0; }
        @media (min-width: 1024px) {
            .sidebar { transform: translateX(0) !important; }
            .content-area { margin-left: 256px; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Mobile Header -->
    <header class="lg:hidden fixed top-0 left-0 right-0 bg-white shadow-md z-40 flex items-center justify-between px-4 py-3">
        <button id="mobile-menu-toggle" class="text-gray-600 hover:text-gray-900">
            <i class="fas fa-bars text-xl"></i>
        </button>
        <div class="flex items-center">
            <i class="fas fa-graduation-cap text-2xl text-indigo-600 mr-2"></i>
            <span class="text-xl font-bold">EduFlow</span>
        </div>
        <button class="relative">
            <i class="fas fa-bell text-xl text-gray-600"></i>
            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
        </button>
    </header>

    <!-- Sidebar -->
    <aside id="sidebar" class="sidebar fixed top-0 left-0 w-64 h-full bg-white shadow-lg z-50 transform -translate-x-full lg:translate-x-0">
        <div class="flex items-center justify-between p-4 border-b">
            <div class="flex items-center">
                <i class="fas fa-graduation-cap text-2xl text-indigo-600 mr-2"></i>
                <span class="text-xl font-bold">EduFlow</span>
            </div>
            <button id="close-sidebar" class="lg:hidden text-gray-600 hover:text-gray-900">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <nav class="p-4 space-y-2">
            <a href="#" class="flex items-center px-4 py-3 text-indigo-600 bg-indigo-50 rounded-lg">
                <i class="fas fa-tachometer-alt mr-3"></i>
                <span>Dashboard</span>
            </a>
            <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-book mr-3"></i>
                <span>My Courses</span>
            </a>
            <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-calendar mr-3"></i>
                <span>Schedule</span>
            </a>
            <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-chart-bar mr-3"></i>
                <span>Progress</span>
            </a>
            <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-users mr-3"></i>
                <span>Discussion</span>
            </a>
            <a href="#" class="flex items-center px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg">
                <i class="fas fa-cog mr-3"></i>
                <span>Settings</span>
            </a>
        </nav>

        <div class="absolute bottom-0 left-0 right-0 p-4 border-t">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b842?w=40&h=40&fit=crop&crop=face" 
                     alt="Profile" class="w-10 h-10 rounded-full mr-3">
                <div>
                    <p class="font-semibold text-gray-900">Sarah Johnson</p>
                    <p class="text-sm text-gray-600">Student</p>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="content-area pt-16 lg:pt-0">
        <!-- Desktop Header -->
        <header class="hidden lg:flex items-center justify-between bg-white shadow-sm px-8 py-4">
            <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
            <div class="flex items-center space-x-4">
                <button class="relative">
                    <i class="fas fa-bell text-xl text-gray-600"></i>
                    <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                </button>
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b842?w=40&h=40&fit=crop&crop=face" 
                     alt="Profile" class="w-10 h-10 rounded-full">
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="p-4 lg:p-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Courses</p>
                            <p class="text-3xl font-bold text-gray-900">12</p>
                        </div>
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-book text-indigo-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Completed</p>
                            <p class="text-3xl font-bold text-gray-900">8</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Hours Learned</p>
                            <p class="text-3xl font-bold text-gray-900">64</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-purple-600 text-xl"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Certificates</p>
                            <p class="text-3xl font-bold text-gray-900">5</p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-award text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Continue Learning & Upcoming -->
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Continue Learning -->
                <div class="lg:col-span-2">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Continue Learning</h2>
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-xl shadow-sm flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=80&h=80&fit=crop" 
                                 alt="Course" class="w-20 h-20 rounded-lg object-cover">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900">Web Development Bootcamp</h3>
                                <p class="text-sm text-gray-600">Chapter 12: Advanced React</p>
                                <div class="mt-2 bg-gray-200 rounded-full h-2">
                                    <div class="bg-indigo-600 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">75% complete</p>
                            </div>
                            <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition">
                                Continue
                            </button>
                        </div>

                        <div class="bg-white p-4 rounded-xl shadow-sm flex items-center space-x-4">
                            <img src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=80&h=80&fit=crop" 
                                 alt="Course" class="w-20 h-20 rounded-lg object-cover">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900">Data Science Fundamentals</h3>
                                <p class="text-sm text-gray-600">Chapter 8: Machine Learning Basics</p>
                                <div class="mt-2 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">45% complete</p>
                            </div>
                            <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition">
                                Continue
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Upcoming -->
                <div>
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Upcoming</h2>
                    <div class="bg-white rounded-xl shadow-sm p-4 space-y-4">
                        <div class="border-l-4 border-indigo-600 pl-4">
                            <h4 class="font-semibold text-gray-900">Live Session: React Hooks</h4>
                            <p class="text-sm text-gray-600">Today, 3:00 PM</p>
                        </div>
                        <div class="border-l-4 border-green-600 pl-4">
                            <h4 class="font-semibold text-gray-900">Assignment Due</h4>
                            <p class="text-sm text-gray-600">Tomorrow, 11:59 PM</p>
                        </div>
                        <div class="border-l-4 border-purple-600 pl-4">
                            <h4 class="font-semibold text-gray-900">Quiz: JavaScript Basics</h4>
                            <p class="text-sm text-gray-600">Friday, 2:00 PM</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="mt-8 bg-white rounded-xl shadow-sm p-6">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-indigo-600"></i>
                        </div>
                        <div>
                            <p class="text-gray-900">Started "Advanced React" lesson</p>
                            <p class="text-sm text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <div>
                            <p class="text-gray-900">Completed "CSS Grid" quiz</p>
                            <p class="text-sm text-gray-500">Yesterday, 4:30 PM</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-certificate text-purple-600"></i>
                        </div>
                        <div>
                            <p class="text-gray-900">Earned "JavaScript Basics" certificate</p>
                            <p class="text-sm text-gray-500">3 days ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Overlay for mobile -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

    <script>
        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const closeSidebar = document.getElementById('close-sidebar');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        function toggleSidebar() {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        }

        mobileMenuToggle.addEventListener('click', toggleSidebar);
        closeSidebar.addEventListener('click', toggleSidebar);
        overlay.addEventListener('click', toggleSidebar);

        // Close sidebar when clicking outside on mobile
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.add('hidden');
            }
        });
    </script>
</body>
</html>