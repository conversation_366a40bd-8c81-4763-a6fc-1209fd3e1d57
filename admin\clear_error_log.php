<?php
/**
 * Clear Error Log Endpoint
 * Allows admin to clear error logs
 */

// Load configuration
require_once '../config/db_config.php';

// Check if user is admin
if (!isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Set JSON response header
header('Content-Type: application/json');

// Handle POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['action']) || $input['action'] !== 'clear_log') {
        throw new Exception('Invalid action');
    }
    
    $success = true;
    $messages = [];
    
    // Clear custom error log
    $errorLogFile = __DIR__ . '/error_log.txt';
    if (file_exists($errorLogFile)) {
        if (file_put_contents($errorLogFile, '') !== false) {
            $messages[] = 'Custom error log cleared';
        } else {
            $success = false;
            $messages[] = 'Failed to clear custom error log';
        }
    }
    
    // Clear errors.log if it exists
    $errorsLogFile = __DIR__ . '/errors.log';
    if (file_exists($errorsLogFile)) {
        if (file_put_contents($errorsLogFile, '') !== false) {
            $messages[] = 'Errors log cleared';
        } else {
            $success = false;
            $messages[] = 'Failed to clear errors log';
        }
    }
    
    // Log the action
    $adminUsername = $_SESSION['admin_username'] ?? 'Unknown';
    $logMessage = "[" . date('Y-m-d H:i:s') . "] Admin '$adminUsername' cleared error logs\n";
    
    // Create admin action log if it doesn't exist
    $adminLogFile = __DIR__ . '/admin_actions.log';
    file_put_contents($adminLogFile, $logMessage, FILE_APPEND | LOCK_EX);
    
    if ($success) {
        echo json_encode([
            'success' => true, 
            'message' => 'Error logs cleared successfully',
            'details' => $messages
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Some error logs could not be cleared',
            'details' => $messages
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Failed to clear error log: ' . $e->getMessage()
    ]);
    
    // Log the error
    error_log("Clear error log failed: " . $e->getMessage());
}
?>
