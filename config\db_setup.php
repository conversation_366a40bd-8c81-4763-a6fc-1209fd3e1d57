<?php
/**
 * Database Setup Helper for universalreciters.name.ng
 * This will help you configure the correct database settings
 */

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? '';
    $db_user = $_POST['db_user'] ?? '';
    $db_pass = $_POST['db_pass'] ?? '';
    
    // Test the database connection
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        
        if ($conn->connect_error) {
            throw new Exception('Connection failed: ' . $conn->connect_error);
        }
        
        // Connection successful, update the config file
        $configFile = __DIR__ . '/server_config.php';
        $content = file_get_contents($configFile);
        
        // Replace the database configuration
        $content = preg_replace(
            "/define\('DB_HOST', '[^']*'\);/",
            "define('DB_HOST', '$db_host');",
            $content
        );
        $content = preg_replace(
            "/define\('DB_NAME', '[^']*'\);/",
            "define('DB_NAME', '$db_name');",
            $content
        );
        $content = preg_replace(
            "/define\('DB_USER', '[^']*'\);/",
            "define('DB_USER', '$db_user');",
            $content
        );
        $content = preg_replace(
            "/define\('DB_PASS', '[^']*'\);/",
            "define('DB_PASS', '$db_pass');",
            $content
        );
        
        if (file_put_contents($configFile, $content)) {
            $message = 'Database configuration updated successfully! You can now try the admin login.';
            $messageType = 'success';
        } else {
            $message = 'Database connection successful, but failed to update config file. Please check file permissions.';
            $messageType = 'warning';
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        $message = 'Database connection failed: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Database Setup - Universal Reciters
                        </h4>
                        <small>Configure your production database settings</small>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?>">
                            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Before You Start:</h6>
                            <ul class="mb-0">
                                <li>Make sure your database server is running</li>
                                <li>Have your database credentials ready</li>
                                <li>Ensure your database user has CREATE, INSERT, SELECT, UPDATE permissions</li>
                                <li>Your database should already exist (create it in cPanel/phpMyAdmin if needed)</li>
                            </ul>
                        </div>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="db_host" class="form-label">
                                    <i class="fas fa-server me-2"></i>Database Host
                                </label>
                                <input type="text" class="form-control" id="db_host" name="db_host" 
                                       value="localhost" required>
                                <div class="form-text">Usually 'localhost' for most hosting providers</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_name" class="form-label">
                                    <i class="fas fa-database me-2"></i>Database Name
                                </label>
                                <input type="text" class="form-control" id="db_name" name="db_name" 
                                       placeholder="e.g., universalreciters_recite" required>
                                <div class="form-text">The name of your MySQL database</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_user" class="form-label">
                                    <i class="fas fa-user me-2"></i>Database Username
                                </label>
                                <input type="text" class="form-control" id="db_user" name="db_user" 
                                       placeholder="e.g., universalreciters_user" required>
                                <div class="form-text">Your MySQL username</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="db_pass" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Database Password
                                </label>
                                <input type="password" class="form-control" id="db_pass" name="db_pass" 
                                       placeholder="Your database password" required>
                                <div class="form-text">Your MySQL password</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plug me-2"></i>Test Connection & Update Config
                                </button>
                            </div>
                        </form>
                        
                        <?php if ($messageType === 'success'): ?>
                        <div class="mt-4 text-center">
                            <a href="../admin/production_fix.php" class="btn btn-success">
                                <i class="fas fa-tools me-2"></i>Run Production Fix
                            </a>
                            <a href="../admin/login.php" class="btn btn-outline-success">
                                <i class="fas fa-sign-in-alt me-2"></i>Try Admin Login
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-question-circle me-2"></i>Need Help Finding Your Database Info?</h6>
                                    <ul class="small mb-0">
                                        <li><strong>cPanel:</strong> Go to MySQL Databases section</li>
                                        <li><strong>Hosting Provider:</strong> Check your hosting control panel</li>
                                        <li><strong>Database Name:</strong> Usually prefixed with your username</li>
                                        <li><strong>Username:</strong> Often the same as database name or prefixed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Your database credentials are stored securely in the config file.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Show/hide password
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('db_pass');
            const toggleButton = document.createElement('button');
            toggleButton.type = 'button';
            toggleButton.className = 'btn btn-outline-secondary btn-sm';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
            toggleButton.style.position = 'absolute';
            toggleButton.style.right = '10px';
            toggleButton.style.top = '50%';
            toggleButton.style.transform = 'translateY(-50%)';
            
            passwordField.parentNode.style.position = 'relative';
            passwordField.parentNode.appendChild(toggleButton);
            
            toggleButton.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    passwordField.type = 'password';
                    toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });
    </script>
</body>
</html>
