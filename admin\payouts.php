<?php
// Force error reporting for debugging
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

$page_title = 'Manage Payouts';
require_once __DIR__ . '/../components/admin_header.php';

// Function to process withdrawal requests (defined first)
if (!function_exists('processWithdrawalRequest')) {
    function processWithdrawalRequest($requestId, $action, $adminNotes) {
        $logPrefix = "[Withdrawal #$requestId]";
        error_log("$logPrefix Processing action '$action'.");

        try {
            $processingConn = getConnection();
            $processingConn->begin_transaction();
            error_log("$logPrefix Transaction started.");

            // Step 1: Find the request in EITHER the new or legacy table
            $request = null;
            $sourceTable = '';

            // Try the modern `withdrawal_requests` table first
            $stmtModern = $processingConn->prepare("SELECT * FROM withdrawal_requests WHERE id = ?");
            $stmtModern->bind_param("i", $requestId);
            $stmtModern->execute();
            $resultModern = $stmtModern->get_result();
            if ($resultModern->num_rows > 0) {
                $request = $resultModern->fetch_assoc();
                $sourceTable = 'withdrawal_requests';
            }
            $stmtModern->close();

            // If not found, try the legacy `transactions` table
            if (!$request) {
                $stmtLegacy = $processingConn->prepare("SELECT * FROM transactions WHERE id = ? AND transaction_type = 'withdrawal'");
                $stmtLegacy->bind_param("i", $requestId);
                $stmtLegacy->execute();
                $resultLegacy = $stmtLegacy->get_result();
                if ($resultLegacy->num_rows > 0) {
                    $request = $resultLegacy->fetch_assoc();
                    $sourceTable = 'transactions';
                }
                $stmtLegacy->close();
            }

            if (!$request) {
                throw new Exception("Request not found in `withdrawal_requests` or `transactions` table.");
            }

            $currentStatus = $request['status'];
            error_log("$logPrefix Found request in `$sourceTable`. Current status: '$currentStatus'.");

            if ($currentStatus !== 'pending') {
                $processingConn->rollback();
                return "Request #{$requestId} could not be processed because its status is already '$currentStatus'.";
            }

            // Step 2: Update the status in the correct table
            $newStatus = $action === 'approve' ? 'completed' : 'rejected';
            $adminUsername = $_SESSION['admin_username'] ?? 'admin';
            $combinedNotes = "Processed by: $adminUsername\n" . $adminNotes;
            $affectedRows = 0;

            if ($sourceTable === 'withdrawal_requests') {
                $updateStmt = $processingConn->prepare("UPDATE withdrawal_requests SET status = ?, admin_notes = ?, processed_at = NOW() WHERE id = ?");
                $updateStmt->bind_param("ssi", $newStatus, $combinedNotes, $requestId);
            } else { // It's a legacy transaction
                $updateStmt = $processingConn->prepare("UPDATE transactions SET status = ? WHERE id = ?");
                $updateStmt->bind_param("si", $newStatus, $requestId);
            }
            
            $updateStmt->execute();
            $affectedRows = $updateStmt->affected_rows;
            $updateStmt->close();
            error_log("$logPrefix UPDATE on `$sourceTable` executed. Rows affected: $affectedRows.");

            // Step 3: Refund if rejected (applies to both tables)
            if ($affectedRows > 0 && $action === 'reject') {
                $refundStmt = $processingConn->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
                $refundStmt->bind_param("di", $request['amount'], $request['user_id']);
                $refundStmt->execute();
                $refundStmt->close();
            }

            // Step 4: Commit and Verify
            $processingConn->commit();
            error_log("$logPrefix Transaction committed.");

            // Final verification
            $verifyStmt = $processingConn->prepare("SELECT status FROM `$sourceTable` WHERE id = ?");
            $verifyStmt->bind_param("i", $requestId);
            $verifyStmt->execute();
            $verifiedStatus = $verifyStmt->get_result()->fetch_assoc()['status'] ?? 'verify_failed';
            $verifyStmt->close();
            error_log("$logPrefix VERIFIED status in DB is now: '$verifiedStatus'");

            if ($affectedRows > 0 && $verifiedStatus === $newStatus) {
                return "Success! Request #{$requestId} was updated. Verified status in DB is now: '$verifiedStatus'.";
            } elseif ($affectedRows > 0) {
                 return "CRITICAL ERROR on Request #{$requestId}: Update reported success, but verification failed. Expected '$newStatus' but found '$verifiedStatus'.";
            } else {
                return "Request #{$requestId} was not updated. It may have already been processed.";
            }

        } catch (Exception $e) {
            if (isset($processingConn) && $processingConn->in_transaction) {
                $processingConn->rollback();
            }
            logError("$logPrefix CRITICAL ERROR: " . $e->getMessage());
            return "An error occurred: " . $e->getMessage();
        }
    }
} // End function_exists check

// Initialize variables
$message = '';
$messageType = '';
$csrfToken = generateCSRFToken(); // Generate CSRF token once

// Check for approve/reject actions first
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['approve', 'reject'])) {
    $action = $_POST['action'];
    $requestId = intval($_POST['request_id'] ?? 0);
    $adminNotes = sanitize($_POST['admin_notes'] ?? '');

    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
        $messageType = 'danger';
    } elseif ($requestId > 0) {
        $message = processWithdrawalRequest($requestId, $action, $adminNotes);
        $messageType = strpos($message, 'Success') !== false ? 'success' : 'danger';
    } else {
        $message = 'Invalid request ID.';
        $messageType = 'danger';
    }

    // After processing, redirect to show all requests
    header("Location: payouts.php?status=all&message=" . urlencode($message) . "&messageType=$messageType");
    exit;
}

// Handle delete action separately
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_request') {
    // --- Start Debugging ---
    error_log("--- DELETE ACTION TRIGGERED ---");
    $raw_post_data = file_get_contents('php://input');
    error_log("Raw POST data: " . $raw_post_data);
    error_log("Received POST array: " . print_r($_POST, true));
    // --- End Debugging ---

    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please refresh and try again.';
        $messageType = 'danger';
        error_log("CSRF token validation failed.");
    } else {
        $deleteRequestId = intval($_POST['request_id'] ?? 0);
        $sourceTable = $_POST['source_table'] ?? '';
        error_log("Processing delete for ID: $deleteRequestId from table: $sourceTable");

        if ($deleteRequestId > 0 && in_array($sourceTable, ['withdrawal_requests', 'transactions'])) {
            try {
                error_log("Attempting to execute DELETE query...");
                $deleteQuery = "DELETE FROM `$sourceTable` WHERE id = ?";
                $deleteResult = executeQuery($deleteQuery, 'i', [$deleteRequestId]);

                if ($deleteResult && $deleteResult->affected_rows > 0) {
                    $message = "Success! Request #{$deleteRequestId} has been permanently deleted.";
                    $messageType = 'success';
                    error_log("DELETE successful. Rows affected: " . $deleteResult->affected_rows);
                } else {
                    $message = "Notice: Could not delete Request #{$deleteRequestId}. It may have already been removed. (Rows affected: 0)";
                    $messageType = 'warning';
                    error_log("DELETE query executed but no rows were affected.");
                }
            } catch (Exception $e) {
                $message = "Database error while deleting: " . $e->getMessage();
                $messageType = 'danger';
                error_log("DELETE failed with exception: " . $e->getMessage());
            }
        } else {
            $message = 'Invalid request ID or source table for deletion. Could not proceed.';
            $messageType = 'danger';
            error_log("Validation failed for delete action. Request ID: $deleteRequestId, Source Table: '$sourceTable'");
        }
    }
}

// Get filter parameters from GET request, otherwise default to 'all'
$status = $_GET['status'] ?? 'all';

// Fetch withdrawals based on status
try {

// Always fetch from both tables and merge them
$withdrawals = [];
$legacy_withdrawals = [];

// 1. Fetch from modern `withdrawal_requests` table
$queryModern = "
    SELECT w.*, u.full_name as username, u.email
    FROM withdrawal_requests w
    JOIN users u ON w.user_id = u.id
";
if ($status !== 'all') {
    $queryModern .= " WHERE w.status = ?";
    $resultModern = executeQuery($queryModern, 's', [$status]);
} else {
    $resultModern = executeQuery($queryModern);
}
if ($resultModern) {
    $withdrawals = $resultModern->fetch_all(MYSQLI_ASSOC);
}

// 2. Fetch from legacy `transactions` table
$queryLegacy = "
    SELECT t.id, t.user_id, t.amount, t.description, t.status, t.created_at,
           u.full_name as username, u.email,
           'Legacy Transaction' as bank_name, 
           'See Description' as account_number, 
           u.full_name as account_holder_name,
           true as is_legacy
    FROM transactions t
    JOIN users u ON t.user_id = u.id
    WHERE t.transaction_type = 'withdrawal'
";
if ($status !== 'all') {
    $queryLegacy .= " AND t.status = ?";
    $resultLegacy = executeQuery($queryLegacy, 's', [$status]);
} else {
    $resultLegacy = executeQuery($queryLegacy);
}
if ($resultLegacy) {
    $legacy_withdrawals = $resultLegacy->fetch_all(MYSQLI_ASSOC);
}

// 3. Merge modern and legacy requests
$all_requests = array_merge($withdrawals, $legacy_withdrawals);

// 4. Sort the combined list by date
usort($all_requests, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// Assign the final sorted array to the variable used by the table
$withdrawals = $all_requests;

// Get summary statistics from both tables
// Update the summary query to use 'completed' instead of 'approved'
$summaryQuery = "
    SELECT
        COUNT(*) as total_requests,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as approved_requests,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as approved_amount
    FROM withdrawal_requests
";
$summaryResult = executeQuery($summaryQuery);
$summary = $summaryResult ? $summaryResult->fetch_assoc() : [
    'total_requests' => 0,
    'pending_requests' => 0,
    'approved_requests' => 0,
    'rejected_requests' => 0,
    'pending_amount' => 0,
    'approved_amount' => 0
];

// Add legacy transactions from transactions table
try {
    $legacySummaryResult = executeQuery("
        SELECT
            COUNT(*) as legacy_total,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as legacy_pending,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as legacy_completed,
            COUNT(CASE WHEN status IN ('rejected', 'failed', 'cancelled') THEN 1 END) as legacy_rejected,
            SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as legacy_pending_amount,
            SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as legacy_completed_amount
        FROM transactions
        WHERE transaction_type = 'withdrawal'
    ");
    if ($legacySummaryResult) {
        $legacySummary = $legacySummaryResult->fetch_assoc();
        $summary['total_requests'] += $legacySummary['legacy_total'];
        $summary['pending_requests'] += $legacySummary['legacy_pending'];
        $summary['approved_requests'] += $legacySummary['legacy_completed'];
        $summary['rejected_requests'] += $legacySummary['legacy_rejected'];
        $summary['pending_amount'] += $legacySummary['legacy_pending_amount'];
        $summary['approved_amount'] += $legacySummary['legacy_completed_amount'];
    }
} catch (Exception $e) {
    // Log error but continue - legacy data is optional
    error_log("Error fetching legacy withdrawal data: " . $e->getMessage());
}

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
    $withdrawals = [];
    $summary = [
        'total_requests' => 0,
        'pending_requests' => 0,
        'approved_requests' => 0,
        'rejected_requests' => 0,
        'pending_amount' => 0,
        'approved_amount' => 0
    ];
    logError("Payouts page error: " . $e->getMessage());
}
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Debug Information for Live Server -->
<?php if (isset($error) && !empty($error)): ?>
<div class="alert alert-info">
    <h6><i class="fas fa-info-circle me-2"></i>Debug Information:</h6>
    <small>
        Total Withdrawals: <?php echo count($withdrawals ?? []); ?><br>
        Status Filter: <?php echo htmlspecialchars($status); ?><br>
        <a href="debug-live-server.php" class="btn btn-sm btn-primary mt-2">
            <i class="fas fa-bug me-1"></i>Run Full Diagnostic
        </a>
    </small>
</div>
<?php endif; ?>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <h4><?php echo $summary['total_requests'] ?? 0; ?></h4>
                <p class="mb-0">Total Requests</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4><?php echo $summary['pending_requests'] ?? 0; ?></h4>
                <p class="mb-0">Pending</p>
                <small>₦<?php echo number_format($summary['pending_amount'] ?? 0, 2); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h4><?php echo $summary['approved_requests'] ?? 0; ?></h4>
                <p class="mb-0">Approved</p>
                <small>₦<?php echo number_format($summary['approved_amount'] ?? 0, 2); ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4><?php echo $summary['rejected_requests'] ?? 0; ?></h4>
                <p class="mb-0">Rejected</p>
            </div>
        </div>
    </div>
</div>

<div class="data-table-container">
    <div class="table-header">
        <h4 class="table-title">Withdrawal Requests</h4>
        <!-- Status Filter -->
        <div class="btn-group">
            <a href="?status=all" class="btn btn-sm <?php echo $status === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">All</a>
            <a href="?status=pending" class="btn btn-sm <?php echo $status === 'pending' ? 'btn-primary' : 'btn-outline-primary'; ?>">Pending</a>
            <a href="?status=approved" class="btn btn-sm <?php echo $status === 'approved' ? 'btn-primary' : 'btn-outline-primary'; ?>">Approved</a>
            <a href="?status=rejected" class="btn btn-sm <?php echo $status === 'rejected' ? 'btn-primary' : 'btn-outline-primary'; ?>">Rejected</a>
        </div>
    </div>
    <div class="table-responsive">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>Request ID</th>
                    <th>User Details</th>
                    <th>Amount</th>
                    <th>Bank Details</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($withdrawals)): ?>
                    <tr><td colspan="7" class="text-center py-5">No withdrawal requests found.</td></tr>
                <?php else: ?>
                    <?php foreach ($withdrawals as $withdrawal): ?>
                    <tr>
                        <td>#<?php echo $withdrawal['id']; ?></td>
                        <td>
                            <div class="fw-semibold"><?php echo htmlspecialchars($withdrawal['username']); ?></div>
                            <small class="text-muted"><?php echo htmlspecialchars($withdrawal['email']); ?></small>
                        </td>
                        <td><strong>₦<?php echo number_format($withdrawal['amount'], 2); ?></strong></td>
                        <td>
                            <div>
                                <strong>Bank:</strong> <?php echo htmlspecialchars($withdrawal['bank_name']); ?><br>
                                <strong>Account No:</strong> <?php echo htmlspecialchars($withdrawal['account_number']); ?><br>
                                <strong>Holder Name:</strong> <?php echo htmlspecialchars($withdrawal['account_holder_name']); ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo strtolower($withdrawal['status']) === 'completed' ? 'success' : (strtolower($withdrawal['status']) === 'pending' ? 'warning' : 'danger'); ?>">
                                <?php echo ucfirst(htmlspecialchars($withdrawal['status'])); ?>
                            </span>
                        </td>
                        <td><?php echo date('M j, Y H:i A', strtotime($withdrawal['created_at'])); ?></td>
                        <td class="text-end">
                            <?php if ($withdrawal['status'] === 'pending'): ?>
                            <button class="btn btn-sm btn-success me-1" onclick="processRequest(<?php echo $withdrawal['id']; ?>, 'approve')">
                                <i class="fas fa-check"></i> Approve
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="processRequest(<?php echo $withdrawal['id']; ?>, 'reject')">
                                <i class="fas fa-times"></i> Reject
                            </button>
                            <?php endif; ?>
                            <form method="POST" onsubmit="return confirm('Are you sure you want to permanently delete this request? This cannot be undone.');" style="display: inline-block; margin-left: 5px;">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="delete_request">
                                <input type="hidden" name="request_id" value="<?php echo $withdrawal['id']; ?>">
                                <input type="hidden" name="source_table" value="<?php echo (isset($withdrawal['is_legacy']) && $withdrawal['is_legacy']) ? 'transactions' : 'withdrawal_requests'; ?>">
                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Request">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>Withdrawal Request Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Request Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Request ID:</strong></td><td id="detail_id"></td></tr>
                            <tr><td><strong>Amount:</strong></td><td id="detail_amount"></td></tr>
                            <tr><td><strong>Status:</strong></td><td id="detail_status"></td></tr>
                            <tr><td><strong>Date Requested:</strong></td><td id="detail_created"></td></tr>
                            <tr><td><strong>Date Processed:</strong></td><td id="detail_processed"></td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">User Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Username:</strong></td><td id="detail_username"></td></tr>
                            <tr><td><strong>Email:</strong></td><td id="detail_email"></td></tr>
                            <tr><td><strong>Full Name:</strong></td><td id="detail_full_name"></td></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-primary">Bank Details</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Bank Name:</strong></td><td id="detail_bank_name"></td></tr>
                            <tr><td><strong>Account Number:</strong></td><td id="detail_account_number"></td></tr>
                            <tr><td><strong>Account Holder Name:</strong></td><td id="detail_account_holder"></td></tr>
                            <tr><td><strong>Account Name:</strong></td><td id="detail_account_name"></td></tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3" id="admin_section" style="display: none;">
                    <div class="col-12">
                        <h6 class="text-primary">Admin Information</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Processed By:</strong></td><td id="detail_processed_by"></td></tr>
                            <tr><td><strong>Admin Notes:</strong></td><td id="detail_admin_notes"></td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Process Request Modal -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="request_id" id="modalRequestId">
                <input type="hidden" name="action" id="modalAction">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Process Withdrawal Request</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                  placeholder="Optional notes about this decision..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modalMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewDetails(requestId, withdrawalData) {
    try {
        const withdrawal = JSON.parse(withdrawalData);

        // Populate request information
        document.getElementById('detail_id').textContent = '#' + withdrawal.id;
        document.getElementById('detail_amount').textContent = '₦' + parseFloat(withdrawal.amount).toLocaleString();
        document.getElementById('detail_status').innerHTML = '<span class="badge bg-' + getStatusClass(withdrawal.status) + '">' + withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1) + '</span>';
        document.getElementById('detail_created').textContent = new Date(withdrawal.created_at).toLocaleString();
        document.getElementById('detail_processed').textContent = withdrawal.processed_at ? new Date(withdrawal.processed_at).toLocaleString() : 'Not processed';

        // Populate user information
        document.getElementById('detail_username').textContent = withdrawal.username || 'N/A';
        document.getElementById('detail_email').textContent = withdrawal.email || 'N/A';
        document.getElementById('detail_full_name').textContent = withdrawal.username || 'N/A';

        // Populate bank details
        document.getElementById('detail_bank_name').textContent = withdrawal.bank_name || 'N/A';
        document.getElementById('detail_account_number').textContent = withdrawal.account_number || 'N/A';
        document.getElementById('detail_account_holder').textContent = withdrawal.account_holder_name || 'N/A';

        // Extract account name from admin_notes if available
        let accountName = 'N/A';
        if (withdrawal.admin_notes && withdrawal.admin_notes.includes('Account Name:')) {
            accountName = withdrawal.admin_notes.replace('Account Name: ', '').split('\n')[0];
        }
        document.getElementById('detail_account_name').textContent = accountName;

        // Show admin section if processed
        const adminSection = document.getElementById('admin_section');
        if (withdrawal.status !== 'pending') {
            // Extract processed by from admin notes if available
            let processedBy = 'System';
            if (withdrawal.admin_notes && withdrawal.admin_notes.includes('Processed by:')) {
                const lines = withdrawal.admin_notes.split('\n');
                const processedByLine = lines.find(line => line.includes('Processed by:'));
                if (processedByLine) {
                    processedBy = processedByLine.replace('Processed by: ', '');
                }
            }
            document.getElementById('detail_processed_by').textContent = processedBy;
            document.getElementById('detail_admin_notes').textContent = withdrawal.admin_notes || 'No notes';
            adminSection.style.display = 'block';
        } else {
            adminSection.style.display = 'none';
        }

        const modal = new bootstrap.Modal(document.getElementById('viewDetailsModal'));
        modal.show();

    } catch (error) {
        console.error('Error parsing withdrawal data:', error);
        alert('Error loading withdrawal details');
    }
}

function getStatusClass(status) {
    switch (status) {
        case 'pending': return 'warning';
        case 'completed': return 'success';
        case 'rejected': return 'danger';
        default: return 'secondary';
    }
}

function processRequest(requestId, action) {
    document.getElementById('modalRequestId').value = requestId;
    document.getElementById('modalAction').value = action;
    
    const modal = new bootstrap.Modal(document.getElementById('processModal'));
    const title = document.getElementById('modalTitle');
    const message = document.getElementById('modalMessage');
    const submitBtn = document.getElementById('modalSubmitBtn');
    
    if (action === 'approve') {
        title.textContent = 'Approve Withdrawal Request';
        message.textContent = 'This will approve the withdrawal request and mark it as processed.';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Approve Request';
    } else {
        title.textContent = 'Reject Withdrawal Request';
        message.textContent = 'This will reject the withdrawal request and refund the amount to the user\'s wallet.';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-2"></i>Reject Request';
    }
    
    modal.show();
}
</script>

<style>
    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }
</style>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>