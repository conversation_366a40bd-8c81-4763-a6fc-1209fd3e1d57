<?php
/**
 * Test script for withdrawal system
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Withdrawal System Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='test-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-money-bill-wave me-2'></i>Withdrawal System Test</h1>
                        <p class='text-muted'>Testing withdrawal requests and payout functionality</p>
                    </div>";

$tests = [];
$errors = [];

// Test 1: Database Connection
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-database me-2'></i>Test 1: Database Connection</h6>";

try {
    $conn = getConnection();
    if ($conn) {
        echo "<p class='mb-0'>✅ Database connection successful</p>";
        $tests['database'] = true;
    } else {
        echo "<p class='mb-0'>❌ Database connection failed</p>";
        $tests['database'] = false;
        $errors[] = "Database connection failed";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $tests['database'] = false;
    $errors[] = $e->getMessage();
}

echo "</div>";

// Test 2: Check Tables
echo "<div class='test-result " . ($tests['database'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-table me-2'></i>Test 2: Withdrawal Tables</h6>";

if ($tests['database']) {
    $tables = ['withdrawal_requests', 'transactions', 'users'];
    $tablesExist = 0;
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='mb-0'>✅ Table '$table' exists</p>";
            $tablesExist++;
        } else {
            echo "<p class='mb-0'>❌ Table '$table' missing</p>";
            $errors[] = "Table $table missing";
        }
    }
    
    $tests['tables'] = ($tablesExist == count($tables));
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database failed)</p>";
    $tests['tables'] = false;
}

echo "</div>";

// Test 3: Check Withdrawal Data
echo "<div class='test-result " . ($tests['database'] && $tests['tables'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-chart-bar me-2'></i>Test 3: Withdrawal Data</h6>";

if ($tests['database'] && $tests['tables']) {
    try {
        // Check withdrawal_requests table
        $withdrawalRequestsCount = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Withdrawal Requests: $withdrawalRequestsCount records</p>";
        
        if ($withdrawalRequestsCount > 0) {
            $pendingCount = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'pending'")->fetch_assoc()['count'];
            echo "<p class='mb-0'>✅ Pending Requests: $pendingCount</p>";
            
            // Show sample data
            $sampleResult = $conn->query("SELECT id, user_id, amount, bank_name, status, created_at FROM withdrawal_requests ORDER BY created_at DESC LIMIT 3");
            if ($sampleResult && $sampleResult->num_rows > 0) {
                echo "<p class='mb-2'>📋 Sample Withdrawal Requests:</p>";
                while ($row = $sampleResult->fetch_assoc()) {
                    echo "<small class='text-muted'>ID: {$row['id']}, User: {$row['user_id']}, Amount: ₦{$row['amount']}, Bank: {$row['bank_name']}, Status: {$row['status']}</small><br>";
                }
            }
        }
        
        // Check transactions table for legacy withdrawals
        $transactionWithdrawalsCount = $conn->query("SELECT COUNT(*) as count FROM transactions WHERE transaction_type = 'withdrawal'")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Legacy Transaction Withdrawals: $transactionWithdrawalsCount records</p>";
        
        if ($transactionWithdrawalsCount > 0) {
            $pendingTransactionsCount = $conn->query("SELECT COUNT(*) as count FROM transactions WHERE transaction_type = 'withdrawal' AND status = 'pending'")->fetch_assoc()['count'];
            echo "<p class='mb-0'>✅ Pending Legacy Withdrawals: $pendingTransactionsCount</p>";
            
            // Show sample legacy data
            $legacySampleResult = $conn->query("SELECT id, user_id, amount, description, status, created_at FROM transactions WHERE transaction_type = 'withdrawal' ORDER BY created_at DESC LIMIT 3");
            if ($legacySampleResult && $legacySampleResult->num_rows > 0) {
                echo "<p class='mb-2'>📋 Sample Legacy Withdrawals:</p>";
                while ($row = $legacySampleResult->fetch_assoc()) {
                    echo "<small class='text-muted'>ID: {$row['id']}, User: {$row['user_id']}, Amount: ₦{$row['amount']}, Description: {$row['description']}, Status: {$row['status']}</small><br>";
                }
            }
        }
        
        $tests['data'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Data query error: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['data'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database/tables failed)</p>";
    $tests['data'] = false;
}

echo "</div>";

// Test 4: Check User Wallet Balances
echo "<div class='test-result " . ($tests['database'] && $tests['tables'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-wallet me-2'></i>Test 4: User Wallet Balances</h6>";

if ($tests['database'] && $tests['tables']) {
    try {
        $usersWithBalance = $conn->query("SELECT COUNT(*) as count FROM users WHERE wallet_balance > 0")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Users with wallet balance: $usersWithBalance</p>";
        
        $totalWalletBalance = $conn->query("SELECT SUM(wallet_balance) as total FROM users")->fetch_assoc()['total'];
        echo "<p class='mb-0'>✅ Total wallet balance: ₦" . number_format($totalWalletBalance, 2) . "</p>";
        
        // Show users with highest balances
        $topUsersResult = $conn->query("SELECT id, username, full_name, wallet_balance FROM users WHERE wallet_balance > 0 ORDER BY wallet_balance DESC LIMIT 5");
        if ($topUsersResult && $topUsersResult->num_rows > 0) {
            echo "<p class='mb-2'>💰 Top Wallet Balances:</p>";
            while ($row = $topUsersResult->fetch_assoc()) {
                echo "<small class='text-muted'>User: {$row['username']} ({$row['full_name']}) - ₦" . number_format($row['wallet_balance'], 2) . "</small><br>";
            }
        }
        
        $tests['wallets'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Wallet query error: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['wallets'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database/tables failed)</p>";
    $tests['wallets'] = false;
}

echo "</div>";

// Test 5: Test Wallet Page Access
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-link me-2'></i>Test 5: Page Access</h6>";

$pages = [
    'wallet.php' => '../wallet.php',
    'payouts.php' => 'payouts.php'
];

foreach ($pages as $pageName => $pagePath) {
    if (file_exists($pagePath)) {
        echo "<p class='mb-0'>✅ $pageName - File exists and accessible</p>";
    } else {
        echo "<p class='mb-0'>❌ $pageName - File not found</p>";
        $errors[] = "$pageName not found";
    }
}

echo "</div>";

// Close database connection
if (isset($conn) && $conn) {
    $conn->close();
}

// Summary
$totalTests = count($tests);
$passedTests = count(array_filter($tests));

if (empty($errors)) {
    echo "<div class='alert alert-success'>
            <h5><i class='fas fa-check-circle me-2'></i>All Tests Passed! ($passedTests/$totalTests)</h5>
            <p class='mb-3'>The withdrawal system appears to be working correctly.</p>
            <div class='text-center'>
                <a href='payouts.php' class='btn btn-warning btn-lg me-2'>
                    <i class='fas fa-money-bill-wave me-2'></i>View Payouts Page
                </a>
                <a href='../wallet.php' class='btn btn-success btn-lg'>
                    <i class='fas fa-wallet me-2'></i>View Wallet Page
                </a>
            </div>
          </div>";
} else {
    echo "<div class='alert alert-warning'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Issues Found ($passedTests/$totalTests tests passed)</h5>
            <h6>Issues to fix:</h6>
            <ul class='mb-3'>";
    
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    
    echo "            </ul>
            <div class='text-center'>
                <a href='dashboard.php' class='btn btn-secondary btn-lg'>
                    <i class='fas fa-tachometer-alt me-2'></i>Back to Dashboard
                </a>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            This test helps diagnose withdrawal system issues.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
