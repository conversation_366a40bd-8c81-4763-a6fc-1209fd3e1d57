<?php
/**
 * Debug Page - Use this to check what's wrong on your live server
 * Upload this file and visit it to see detailed error information
 */

// Force error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>RECITE App Debug Information</h1>";

// Check PHP version
echo "<h2>PHP Information</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";

// Check file permissions
echo "<h2>File Permissions</h2>";
$checkDirs = [
    'config/',
    'uploads/',
    'assets/',
    'includes/'
];

foreach ($checkDirs as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        echo "<p><strong>$dir:</strong> $perms " . (is_writable($dir) ? "(Writable)" : "(Not Writable)") . "</p>";
    } else {
        echo "<p><strong>$dir:</strong> Directory not found</p>";
    }
}

// Test database connection
echo "<h2>Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    
    echo "<p><strong>Database Host:</strong> " . DB_HOST . "</p>";
    echo "<p><strong>Database Name:</strong> " . DB_NAME . "</p>";
    echo "<p><strong>Database User:</strong> " . DB_USER . "</p>";
    echo "<p><strong>Development Mode:</strong> " . (DEVELOPMENT_MODE ? 'Yes' : 'No') . "</p>";
    
    $conn = getConnection();
    if ($conn) {
        echo "<p style='color: green;'><strong>✅ Database Connection: SUCCESS</strong></p>";
        
        // Test a simple query
        $result = $conn->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p><strong>Users in database:</strong> " . $row['count'] . "</p>";
        }
        
        // Check if required tables exist
        $tables = ['users', 'transactions', 'point_transactions', 'user_points'];
        echo "<h3>Database Tables:</h3>";
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<p style='color: green;'>✅ Table '$table' exists</p>";
            } else {
                echo "<p style='color: red;'>❌ Table '$table' missing</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ Database Connection: FAILED</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Database Error:</strong> " . $e->getMessage() . "</p>";
}

// Check required PHP extensions
echo "<h2>PHP Extensions</h2>";
$requiredExtensions = ['mysqli', 'json', 'mbstring', 'fileinfo', 'gd'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $ext extension loaded</p>";
    } else {
        echo "<p style='color: red;'>❌ $ext extension missing</p>";
    }
}

// Check session
echo "<h2>Session Information</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "<p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";

// Check error logs
echo "<h2>Recent Error Logs</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "<p><strong>Error log location:</strong> $errorLog</p>";
    $errors = file_get_contents($errorLog);
    $recentErrors = array_slice(explode("\n", $errors), -10);
    echo "<pre>" . implode("\n", $recentErrors) . "</pre>";
} else {
    echo "<p>No error log found or configured</p>";
}

// Test file uploads
echo "<h2>File Upload Test</h2>";
$uploadDir = 'uploads/';
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0755, true)) {
        echo "<p style='color: green;'>✅ Created uploads directory</p>";
    } else {
        echo "<p style='color: red;'>❌ Cannot create uploads directory</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Uploads directory exists</p>";
}

if (is_writable($uploadDir)) {
    echo "<p style='color: green;'>✅ Uploads directory is writable</p>";
} else {
    echo "<p style='color: red;'>❌ Uploads directory is not writable</p>";
}

echo "<h2>Server Environment</h2>";
echo "<pre>";
print_r($_SERVER);
echo "</pre>";

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
