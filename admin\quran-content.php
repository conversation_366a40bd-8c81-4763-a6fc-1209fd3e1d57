<?php
$page_title = 'Qur\'an Content Management';
require_once __DIR__ . '/../components/admin_header.php';

// Check admin authentication  
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$conn = getConnection();
$message = '';
$error = '';

// Helper function to extract YouTube video ID from URL or return as-is if already an ID
function extractYouTubeId($input) {
    // If input is already a valid YouTube ID (11 chars, alphanumeric, - or _)
    if (preg_match('/^[a-zA-Z0-9_-]{11}$/', $input)) {
        return $input;
    }
    // Try to extract from common YouTube URL patterns
    if (preg_match('~(?:youtu\.be/|youtube\.com/(?:embed/|v/|watch\?v=|shorts/|watch\?.+&v=))([\w-]{11})~i', $input, $matches)) {
        return $matches[1];
    }
    // Fallback: return original input
    return $input;
}

// Handle form submission for adding/editing content
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'add_content') {
        // Check password protection
        $password = $_POST['content_password'] ?? '';
        if ($password !== '1@3Usazladan') {
            $error = 'Invalid password. Content management is protected.';
        } else {
            $surah_number = intval($_POST['surah_number']);
            $surah_name = sanitize($_POST['surah_name']);
            $youtube_id = extractYouTubeId(sanitize($_POST['youtube_id']));
            $arabic_text = $_POST['arabic_text']; // Don't sanitize Arabic text
            $unlock_price = floatval($_POST['unlock_price'] ?? 30.00);
            
            if (empty($surah_name) || empty($youtube_id) || empty($arabic_text) || $surah_number <= 0) {
                $error = 'All fields are required.';
            } else {
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO content (surah_number, surah_name, youtube_id, arabic_text, unlock_price) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param("isssd", $surah_number, $surah_name, $youtube_id, $arabic_text, $unlock_price);
                    
                    if ($stmt->execute()) {
                        $message = 'Qur\'an content added successfully!';
                    } else {
                        $error = 'Failed to add content.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
        }
    } elseif ($action === 'edit_content') {
        // Check password protection
        $password = $_POST['content_password'] ?? '';
        if ($password !== '1@3Usazladan') {
            $error = 'Invalid password. Content management is protected.';
        } else {
            $content_id = intval($_POST['content_id']);
            $surah_number = intval($_POST['surah_number']);
            $surah_name = sanitize($_POST['surah_name']);
            $youtube_id = extractYouTubeId(sanitize($_POST['youtube_id']));
            $arabic_text = $_POST['arabic_text']; // Don't sanitize Arabic text
            $translation = $_POST['translation'] ?? ''; // Add translation support
            $unlock_price = floatval($_POST['unlock_price'] ?? 30.00);

            if (empty($surah_name) || empty($youtube_id) || empty($arabic_text) || $surah_number <= 0 || $content_id <= 0) {
                $error = 'All required fields must be filled.';
            } else {
                try {
                    $stmt = $conn->prepare("
                        UPDATE content
                        SET surah_number = ?, surah_name = ?, youtube_id = ?, arabic_text = ?, translation = ?, unlock_price = ?
                        WHERE id = ?
                    ");
                    $stmt->bind_param("issssdi", $surah_number, $surah_name, $youtube_id, $arabic_text, $translation, $unlock_price, $content_id);

                    if ($stmt->execute()) {
                        $message = 'Content updated successfully!';
                    } else {
                        $error = 'Failed to update content.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            }
        }
    } elseif ($action === 'delete_content') {
        $content_id = intval($_POST['content_id']);
        if ($content_id > 0) {
            try {
                $stmt = $conn->prepare("DELETE FROM content WHERE id = ?");
                $stmt->bind_param("i", $content_id);
                if ($stmt->execute()) {
                    $message = 'Content deleted successfully!';
                } else {
                    $error = 'Failed to delete content.';
                }
            } catch (Exception $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Fetch all Qur'an content
$content_list = [];
try {
    $result = $conn->query("SELECT * FROM content ORDER BY surah_number ASC");
    while ($row = $result->fetch_assoc()) {
        $content_list[] = $row;
    }
} catch (Exception $e) {
    $error = 'Failed to fetch content: ' . $e->getMessage();
}
?>

<div class="admin-content">
    <div class="content-header">
        <h2><i class="fas fa-book-quran"></i> Qur'an Content Management</h2>
        <p>Manage Surahs, YouTube videos, and Arabic text for the recitation platform</p>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Add New Content Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h4><i class="fas fa-plus"></i> Add New Qur'an Content</h4>
            <p class="text-muted">Protected by password: 1@3Usazladan</p>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <input type="hidden" name="action" value="add_content">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="surah_number">Surah Number</label>
                            <input type="number" class="form-control" id="surah_number" name="surah_number" min="1" max="114" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="surah_name">Surah Name</label>
                            <input type="text" class="form-control" id="surah_name" name="surah_name" placeholder="e.g., Al-Fatiha" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label for="youtube_id">YouTube Video URL or ID</label>
                            <input type="text" class="form-control" id="youtube_id" name="youtube_id" placeholder="Paste full YouTube URL or video ID" required>
                            <small class="form-text text-muted">Paste the full YouTube URL (e.g. https://www.youtube.com/watch?v=dQw4w9WgXcQ) or just the video ID.</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label for="unlock_price">Unlock Price (₦)</label>
                            <input type="number" class="form-control" id="unlock_price" name="unlock_price" value="30" step="0.01" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label for="arabic_text">Arabic Text</label>
                    <textarea class="form-control" id="arabic_text" name="arabic_text" rows="6" placeholder="Enter the Arabic text of the Surah..." required style="direction: rtl; text-align: right; font-family: 'Amiri', serif; font-size: 1.2rem;"></textarea>
                    <small class="form-text text-muted">Enter the complete Arabic text for this Surah</small>
                </div>

                <div class="form-group mb-3">
                    <label for="content_password">Content Management Password</label>
                    <input type="password" class="form-control" id="content_password" name="content_password" placeholder="Enter password to add content" required>
                    <small class="form-text text-muted">Required password: 1@3Usazladan</small>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Qur'an Content
                </button>
            </form>
        </div>
    </div>

    <!-- Existing Content List -->
    <div class="card">
        <div class="card-header">
            <h4><i class="fas fa-list"></i> Existing Qur'an Content</h4>
        </div>
        <div class="card-body">
            <?php if (empty($content_list)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-book-quran fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No Qur'an content found. Add some content to get started.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped modern-table">
                        <thead>
                            <tr>
                                <th>Surah #</th>
                                <th>Name</th>
                                <th>YouTube ID</th>
                                <th>Price</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($content_list as $content): ?>
                            <tr>
                                <td><?php echo $content['surah_number']; ?></td>
                                <td><?php echo htmlspecialchars($content['surah_name']); ?></td>
                                <td>
                                    <code><?php echo htmlspecialchars($content['youtube_id']); ?></code>
                                    <a href="https://www.youtube.com/watch?v=<?php echo urlencode($content['youtube_id']); ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fab fa-youtube"></i> View
                                    </a>
                                </td>
                                <td>₦<?php echo number_format($content['unlock_price'], 2); ?></td>
                                <td><?php echo date('M j, Y', strtotime($content['created_at'])); ?></td>
                                <td>
                                    <button class="btn btn-sm btn-primary me-2" onclick="editContent(<?php echo $content['id']; ?>)">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteContent(<?php echo $content['id']; ?>)">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Qur'an Content</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_content">
                    <input type="hidden" name="content_id" id="editContentId">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="editSurahNumber">Surah Number</label>
                                <input type="number" class="form-control" id="editSurahNumber" name="surah_number" min="1" max="114" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="editSurahName">Surah Name</label>
                                <input type="text" class="form-control" id="editSurahName" name="surah_name" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label for="editYoutubeId">YouTube Video URL or ID</label>
                                <input type="text" class="form-control" id="editYoutubeId" name="youtube_id" placeholder="Paste full YouTube URL or video ID" required>
                                <small class="form-text text-muted">Paste the full YouTube URL (e.g. https://www.youtube.com/watch?v=dQw4w9WgXcQ) or just the video ID.</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="editUnlockPrice">Unlock Price (₦)</label>
                                <input type="number" class="form-control" id="editUnlockPrice" name="unlock_price" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="editArabicText">Arabic Text</label>
                        <textarea class="form-control" id="editArabicText" name="arabic_text" rows="6" required style="direction: rtl; text-align: right; font-family: 'Amiri', serif; font-size: 1.2rem;"></textarea>
                    </div>

                    <div class="form-group mb-3">
                        <label for="editTranslation">Translation (Optional)</label>
                        <textarea class="form-control" id="editTranslation" name="translation" rows="4"></textarea>
                    </div>

                    <div class="form-group mb-3">
                        <label for="editPassword">Content Management Password</label>
                        <input type="password" class="form-control" id="editPassword" name="content_password" required>
                        <small class="form-text text-muted">Required password: 1@3Usazladan</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Content</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_content">
    <input type="hidden" name="content_id" id="deleteContentId">
</form>
<style>
    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }
</style>
<script>
// Content data for editing
const contentData = <?php echo json_encode($content_list); ?>;

function editContent(contentId) {
    const content = contentData.find(c => c.id == contentId);
    if (content) {
        document.getElementById('editContentId').value = content.id;
        document.getElementById('editSurahNumber').value = content.surah_number;
        document.getElementById('editSurahName').value = content.surah_name;
        document.getElementById('editYoutubeId').value = content.youtube_id;
        document.getElementById('editUnlockPrice').value = content.unlock_price;
        document.getElementById('editArabicText').value = content.arabic_text;
        document.getElementById('editTranslation').value = content.translation || '';

        // Show modal
        new bootstrap.Modal(document.getElementById('editModal')).show();
    }
}

function deleteContent(contentId) {
    if (confirm('Are you sure you want to delete this Qur\'an content? This action cannot be undone and will affect all users who have unlocked this content.')) {
        document.getElementById('deleteContentId').value = contentId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
