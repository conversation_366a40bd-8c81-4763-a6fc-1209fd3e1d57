<?php
/**
 * Admin Login Test Script
 * Use this to test admin login functionality programmatically
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config.php';

$test_results = [];
$success = true;

// Test 1: Database Connection
try {
    $conn = getConnection();
    $test_results['database_connection'] = ['status' => 'PASS', 'message' => 'Database connected successfully'];
} catch (Exception $e) {
    $test_results['database_connection'] = ['status' => 'FAIL', 'message' => 'Database connection failed: ' . $e->getMessage()];
    $success = false;
}

// Test 2: Create Admin Table
if ($success) {
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $conn->query($createTableQuery);
        $test_results['admin_table'] = ['status' => 'PASS', 'message' => 'Admin table created/verified'];
    } catch (Exception $e) {
        $test_results['admin_table'] = ['status' => 'FAIL', 'message' => 'Failed to create admin table: ' . $e->getMessage()];
        $success = false;
    }
}

// Test 3: Create Default Admin
if ($success) {
    try {
        $username = 'admin';
        $password = '1@3Usazladan';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO admins (username, password) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE password = ?, updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->bind_param("sss", $username, $hashedPassword, $hashedPassword);
        $stmt->execute();
        $stmt->close();
        
        $test_results['default_admin'] = ['status' => 'PASS', 'message' => 'Default admin created/updated'];
    } catch (Exception $e) {
        $test_results['default_admin'] = ['status' => 'FAIL', 'message' => 'Failed to create default admin: ' . $e->getMessage()];
        $success = false;
    }
}

// Test 4: Verify Admin Login
if ($success) {
    try {
        $username = 'admin';
        $password = '1@3Usazladan';
        
        $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($admin = $result->fetch_assoc()) {
            if (password_verify($password, $admin['password'])) {
                $test_results['admin_login'] = ['status' => 'PASS', 'message' => 'Admin login verification successful'];
            } else {
                $test_results['admin_login'] = ['status' => 'FAIL', 'message' => 'Password verification failed'];
                $success = false;
            }
        } else {
            $test_results['admin_login'] = ['status' => 'FAIL', 'message' => 'Admin user not found'];
            $success = false;
        }
        $stmt->close();
    } catch (Exception $e) {
        $test_results['admin_login'] = ['status' => 'FAIL', 'message' => 'Login verification failed: ' . $e->getMessage()];
        $success = false;
    }
}

// Test 5: Session Functions
try {
    session_start();
    $_SESSION['test_admin_id'] = 1;
    $_SESSION['test_admin_username'] = 'admin';
    
    // Test isAdmin function
    if (function_exists('isAdmin')) {
        // Temporarily set admin session for testing
        $_SESSION['admin_id'] = 1;
        $_SESSION['admin_username'] = 'admin';
        
        $isAdminResult = isAdmin();
        
        // Clean up test session
        unset($_SESSION['admin_id']);
        unset($_SESSION['admin_username']);
        unset($_SESSION['test_admin_id']);
        unset($_SESSION['test_admin_username']);
        
        if ($isAdminResult) {
            $test_results['session_functions'] = ['status' => 'PASS', 'message' => 'Session functions working'];
        } else {
            $test_results['session_functions'] = ['status' => 'FAIL', 'message' => 'isAdmin function not working'];
            $success = false;
        }
    } else {
        $test_results['session_functions'] = ['status' => 'FAIL', 'message' => 'isAdmin function not found'];
        $success = false;
    }
} catch (Exception $e) {
    $test_results['session_functions'] = ['status' => 'FAIL', 'message' => 'Session test failed: ' . $e->getMessage()];
    $success = false;
}

// Test 6: CSRF Token Generation
try {
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        if (!empty($token)) {
            $test_results['csrf_token'] = ['status' => 'PASS', 'message' => 'CSRF token generation working'];
        } else {
            $test_results['csrf_token'] = ['status' => 'FAIL', 'message' => 'CSRF token is empty'];
            $success = false;
        }
    } else {
        $test_results['csrf_token'] = ['status' => 'FAIL', 'message' => 'generateCSRFToken function not found'];
        $success = false;
    }
} catch (Exception $e) {
    $test_results['csrf_token'] = ['status' => 'FAIL', 'message' => 'CSRF token test failed: ' . $e->getMessage()];
    $success = false;
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Test - RECITE App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            Admin Login Test Results
                        </h4>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                            <h5 class="mb-0">
                                <?php if ($success): ?>
                                    <i class="fas fa-check-circle me-2"></i>All tests passed! Admin login should work correctly.
                                <?php else: ?>
                                    <i class="fas fa-exclamation-triangle me-2"></i>Some tests failed. Please review the issues below.
                                <?php endif; ?>
                            </h5>
                        </div>
                        
                        <div class="list-group">
                            <?php foreach ($test_results as $test_name => $result): ?>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <h6 class="mb-1"><?php echo ucwords(str_replace('_', ' ', $test_name)); ?></h6>
                                    <span class="badge bg-<?php echo $result['status'] === 'PASS' ? 'success' : 'danger'; ?>">
                                        <?php echo $result['status']; ?>
                                    </span>
                                </div>
                                <p class="mb-0 text-muted"><?php echo htmlspecialchars($result['message']); ?></p>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if ($success): ?>
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Login Credentials:</h6>
                                <ul class="mb-0">
                                    <li><strong>Username:</strong> admin</li>
                                    <li><strong>Password:</strong> 1@3Usazladan</li>
                                </ul>
                            </div>
                            
                            <div class="text-center">
                                <a href="login.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>Go to Admin Login
                                </a>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="mt-4">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-tools me-2"></i>Troubleshooting Steps:</h6>
                                <ol class="mb-0">
                                    <li>Check your database configuration in <code>config/server_config.php</code></li>
                                    <li>Ensure your database server is running</li>
                                    <li>Verify database user has proper permissions</li>
                                    <li>Check server error logs for detailed error messages</li>
                                    <li>Run the diagnostic tool: <a href="diagnostic.php">diagnostic.php</a></li>
                                </ol>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>Security Note:</strong> Delete this test file after troubleshooting is complete.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
