<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | EduSphere LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --secondary: #10b981;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --accent: #f59e0b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: var(--dark);
        }
        
        .font-heading {
            font-family: 'Plus Jakarta Sans', sans-serif;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
        }
        
        .sidebar {
            transition: all 0.3s ease;
        }
        
        .progress-ring {
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .data-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .data-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
        }
        
        .data-table tbody tr:hover {
            background-color: #f8fafc;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }
        
        .floating {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="antialiased bg-slate-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-white w-64 border-r border-slate-200 flex-shrink-0 hidden md:block">
            <div class="flex items-center justify-center h-16 px-4 border-b border-slate-200">
                <a href="#" class="flex items-center">
                    <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <span class="font-heading text-xl font-bold text-slate-800">EduSphere</span>
                </a>
            </div>
            
            <div class="p-4">
                <!-- User Profile -->
                <div class="flex items-center mb-6 p-3 rounded-lg bg-slate-50">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Admin" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <h4 class="font-medium text-slate-800">Admin User</h4>
                        <p class="text-xs text-slate-500">Super Admin</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-indigo-50 text-indigo-700">
                        <i class="fas fa-tachometer-alt mr-3 text-indigo-600"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-users mr-3 text-slate-500"></i>
                        User Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-book mr-3 text-slate-500"></i>
                        Course Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chalkboard-teacher mr-3 text-slate-500"></i>
                        Instructor Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tasks mr-3 text-slate-500"></i>
                        Content Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-certificate mr-3 text-slate-500"></i>
                        Certifications
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chart-bar mr-3 text-slate-500"></i>
                        Analytics & Reports
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-cog mr-3 text-slate-500"></i>
                        System Settings
                    </a>
                </nav>
                
                <!-- Bottom Links -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-200">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-question-circle mr-3 text-slate-500"></i>
                        Help Center
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-sign-out-alt mr-3 text-slate-500"></i>
                        Sign Out
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Mobile sidebar -->
        <div class="sidebar md:hidden fixed inset-0 z-40 bg-white transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-sidebar">
            <div class="flex items-center justify-between h-16 px-4 border-b border-slate-200">
                <a href="#" class="flex items-center">
                    <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <span class="font-heading text-xl font-bold text-slate-800">EduSphere</span>
                </a>
                <button id="close-sidebar" class="text-slate-500 hover:text-slate-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="p-4 overflow-y-auto h-[calc(100%-4rem)]">
                <!-- User Profile -->
                <div class="flex items-center mb-6 p-3 rounded-lg bg-slate-50">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Admin" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <h4 class="font-medium text-slate-800">Admin User</h4>
                        <p class="text-xs text-slate-500">Super Admin</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-indigo-50 text-indigo-700">
                        <i class="fas fa-tachometer-alt mr-3 text-indigo-600"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-users mr-3 text-slate-500"></i>
                        User Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-book mr-3 text-slate-500"></i>
                        Course Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chalkboard-teacher mr-3 text-slate-500"></i>
                        Instructor Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tasks mr-3 text-slate-500"></i>
                        Content Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-certificate mr-3 text-slate-500"></i>
                        Certifications
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chart-bar mr-3 text-slate-500"></i>
                        Analytics & Reports
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-cog mr-3 text-slate-500"></i>
                        System Settings
                    </a>
                </nav>
                
                <!-- Bottom Links -->
                <div class="mt-4 pt-4 border-t border-slate-200">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-question-circle mr-3 text-slate-500"></i>
                        Help Center
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-sign-out-alt mr-3 text-slate-500"></i>
                        Sign Out
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white border-b border-slate-200">
                <div class="flex items-center justify-between h-16 px-4">
                    <div class="flex items-center">
                        <button id="open-sidebar" class="md:hidden text-slate-500 hover:text-slate-700 mr-2">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="font-heading text-xl font-bold text-slate-800">Admin Dashboard</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100 relative">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100 relative">
                            <i class="fas fa-envelope text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-blue-500 rounded-full"></span>
                        </button>
                        <div class="relative">
                            <button class="flex items-center focus:outline-none" id="user-menu-button">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Admin" class="w-8 h-8 rounded-full">
                                <span class="ml-2 text-sm font-medium text-slate-700 hidden md:inline">Admin</span>
                                <i class="fas fa-chevron-down ml-1 text-xs text-slate-500 hidden md:inline"></i>
                            </button>
                            
                            <!-- Dropdown menu -->
                            <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" id="user-menu">
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Sign out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-4 md:p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-indigo-100 text-indigo-600 mr-4">
                                <i class="fas fa-users text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Total Users</p>
                                <div class="flex items-end">
                                    <h3 class="text-2xl font-bold text-slate-800">2,548</h3>
                                    <span class="ml-2 text-sm text-green-500 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i> 12%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-green-100 text-green-600 mr-4">
                                <i class="fas fa-book-open text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Active Courses</p>
                                <div class="flex items-end">
                                    <h3 class="text-2xl font-bold text-slate-800">187</h3>
                                    <span class="ml-2 text-sm text-green-500 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i> 5%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-amber-100 text-amber-600 mr-4">
                                <i class="fas fa-chalkboard-teacher text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Instructors</p>
                                <div class="flex items-end">
                                    <h3 class="text-2xl font-bold text-slate-800">42</h3>
                                    <span class="ml-2 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-arrow-down mr-1"></i> 2%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-blue-100 text-blue-600 mr-4">
                                <i class="fas fa-dollar-sign text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Revenue</p>
                                <div class="flex items-end">
                                    <h3 class="text-2xl font-bold text-slate-800">$24,760</h3>
                                    <span class="ml-2 text-sm text-green-500 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i> 18%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity & Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Recent Activity -->
                    <div class="lg:col-span-2 bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="font-heading text-xl font-bold text-slate-800">Recent Activity</h2>
                            <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Activity 1 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-slate-700">
                                        <span class="font-medium">New user registered:</span> Sarah Johnson
                                    </p>
                                    <p class="text-xs text-slate-400 mt-1">10 minutes ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity 2 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                        <i class="fas fa-book"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-slate-700">
                                        <span class="font-medium">New course published:</span> Advanced Python Programming
                                    </p>
                                    <p class="text-xs text-slate-400 mt-1">2 hours ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity 3 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                        <i class="fas fa-certificate"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-slate-700">
                                        <span class="font-medium">Certificate issued:</span> Michael Chen completed Web Development
                                    </p>
                                    <p class="text-xs text-slate-400 mt-1">5 hours ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity 4 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center text-amber-600">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-slate-700">
                                        <span class="font-medium">System alert:</span> High server load detected
                                    </p>
                                    <p class="text-xs text-slate-400 mt-1">1 day ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <h2 class="font-heading text-xl font-bold text-slate-800 mb-6">Quick Actions</h2>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <a href="#" class="p-4 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition flex flex-col items-center justify-center text-center">
                                <div class="w-10 h-10 rounded-lg bg-indigo-100 text-indigo-600 flex items-center justify-center mb-2">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span class="text-sm font-medium text-slate-700">Add Course</span>
                            </a>
                            
                            <a href="#" class="p-4 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition flex flex-col items-center justify-center text-center">
                                <div class="w-10 h-10 rounded-lg bg-green-100 text-green-600 flex items-center justify-center mb-2">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <span class="text-sm font-medium text-slate-700">Add User</span>
                            </a>
                            
                            <a href="#" class="p-4 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition flex flex-col items-center justify-center text-center">
                                <div class="w-10 h-10 rounded-lg bg-blue-100 text-blue-600 flex items-center justify-center mb-2">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                                <span class="text-sm font-medium text-slate-700">Add Instructor</span>
                            </a>
                            
                            <a href="#" class="p-4 rounded-lg border border-slate-200 hover:border-indigo-300 hover:bg-indigo-50 transition flex flex-col items-center justify-center text-center">
                                <div class="w-10 h-10 rounded-lg bg-purple-100 text-purple-600 flex items-center justify-center mb-2">
                                    <i class="fas fa-bullhorn"></i>
                                </div>
                                <span class="text-sm font-medium text-slate-700">Send Announcement</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Users & Courses Tables -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Recent Users -->
                    <div class="bg-white rounded-xl border border-slate-200 shadow-sm">
                        <div class="p-6 border-b border-slate-200">
                            <div class="flex items-center justify-between">
                                <h2 class="font-heading text-xl font-bold text-slate-800">Recent Users</h2>
                                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-200 data-table">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Email</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Role</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Sarah Johnson</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Student</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Michael Chen</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Instructor</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/68.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Emma Rodriguez</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Student</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/75.jpg" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">David Wilson</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500"><EMAIL></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Admin</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Recent Courses -->
                    <div class="bg-white rounded-xl border border-slate-200 shadow-sm">
                        <div class="p-6 border-b border-slate-200">
                            <div class="flex items-center justify-between">
                                <h2 class="font-heading text-xl font-bold text-slate-800">Recent Courses</h2>
                                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-200 data-table">
                                <thead>
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Course</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Instructor</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Students</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded object-cover" src="https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Data Science Fundamentals</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Michael Chen</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">342</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Published
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded object-cover" src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Web Development Bootcamp</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Sarah Johnson</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">587</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Published
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded object-cover" src="https://images.unsplash.com/photo-1605379399642-870262d3d051?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Digital Marketing</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">David Wilson</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">215</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Draft
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <img class="h-10 w-10 rounded object-cover" src="https://images.unsplash.com/photo-1542626991-cbc4e32524cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="">
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-slate-900">Python for Beginners</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">Emma Rodriguez</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">478</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Published
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Revenue Chart -->
                <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm mb-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="font-heading text-xl font-bold text-slate-800">Revenue Overview</h2>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-xs font-medium rounded-lg bg-indigo-100 text-indigo-700">Monthly</button>
                            <button class="px-3 py-1 text-xs font-medium rounded-lg text-slate-700 hover:bg-slate-100">Quarterly</button>
                            <button class="px-3 py-1 text-xs font-medium rounded-lg text-slate-700 hover:bg-slate-100">Yearly</button>
                        </div>
                    </div>
                    
                    <!-- Chart Placeholder -->
                    <div class="h-80 bg-slate-100 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-4xl text-slate-400 mb-2"></i>
                            <p class="text-slate-500">Revenue chart will be displayed here</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Mobile sidebar toggle
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const openSidebarBtn = document.getElementById('open-sidebar');
        const closeSidebarBtn = document.getElementById('close-sidebar');
        
        openSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.remove('-translate-x-full');
        });
        
        closeSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.add('-translate-x-full');
        });
        
        // User menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');
        
        userMenuButton.addEventListener('click', () => {
            userMenu.classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!userMenuButton.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>