<?php
/**
 * Debug script for registration and payment issues
 */
require_once 'config/db_config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Registration Debug - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <script src='https://js.paystack.co/v1/inline.js'></script>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .debug-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='debug-card p-4'>
                    <h2 class='text-center mb-4'>Registration & Payment Debug</h2>";

// Test 1: Check Paystack Configuration
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: Paystack Configuration</h5>";
if (defined('PAYSTACK_PUBLIC_KEY') && !empty(PAYSTACK_PUBLIC_KEY)) {
    echo "✅ PAYSTACK_PUBLIC_KEY is defined: " . substr(PAYSTACK_PUBLIC_KEY, 0, 20) . "...<br>";
} else {
    echo "❌ PAYSTACK_PUBLIC_KEY is not defined or empty<br>";
}

if (defined('PAYSTACK_SECRET_KEY') && !empty(PAYSTACK_SECRET_KEY)) {
    echo "✅ PAYSTACK_SECRET_KEY is defined: " . substr(PAYSTACK_SECRET_KEY, 0, 20) . "...<br>";
} else {
    echo "❌ PAYSTACK_SECRET_KEY is not defined or empty<br>";
}
echo "</div>";

// Test 2: Check Database Connection
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: Database Connection</h5>";
try {
    $conn = getConnection();
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Check users table
        $result = $conn->query("SHOW TABLES LIKE 'users'");
        if ($result && $result->num_rows > 0) {
            echo "✅ Users table exists<br>";
            
            // Check table structure
            $result = $conn->query("DESCRIBE users");
            $columns = [];
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
            echo "✅ Users table columns: " . implode(', ', $columns) . "<br>";
        } else {
            echo "❌ Users table does not exist<br>";
        }
        
        $conn->close();
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Session Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: Session Status</h5>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "✅ Session ID: " . session_id() . "<br>";
    
    if (isset($_SESSION['pending_user_id'])) {
        echo "✅ Pending user ID in session: " . $_SESSION['pending_user_id'] . "<br>";
    } else {
        echo "⚠️ No pending user ID in session<br>";
    }
    
    if (isset($_SESSION['user_email'])) {
        echo "✅ User email in session: " . $_SESSION['user_email'] . "<br>";
    } else {
        echo "⚠️ No user email in session<br>";
    }
} else {
    echo "❌ Session is not active<br>";
}
echo "</div>";

// Test 4: JavaScript/Paystack Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: Paystack JavaScript Test</h5>";
echo "<button id='testPaystackBtn' class='btn btn-primary'>Test Paystack Popup</button>";
echo "<div id='paystackResult' class='mt-2'></div>";
echo "</div>";

// Test 5: Registration Form Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 5: Test Registration Form</h5>";
echo "<form method='POST' action='register.php' class='row g-3'>";
echo "<input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>";
echo "<div class='col-md-6'>";
echo "<input type='text' name='full_name' class='form-control' placeholder='Full Name' value='Test User' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='email' name='email' class='form-control' placeholder='Email' value='test" . time() . "@example.com' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='password' name='password' class='form-control' placeholder='Password' value='password123' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='text' name='ward' class='form-control' placeholder='Ward' value='Test Ward' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='text' name='lga' class='form-control' placeholder='LGA' value='Test LGA' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<input type='text' name='state' class='form-control' placeholder='State' value='Test State' required>";
echo "</div>";
echo "<div class='col-12'>";
echo "<button type='submit' class='btn btn-success'>Test Registration</button>";
echo "</div>";
echo "</form>";
echo "</div>";

// Test 6: Check Recent Registrations
echo "<div class='alert alert-info'>";
echo "<h5>Test 6: Recent Registrations</h5>";
try {
    $conn = getConnection();
    $result = $conn->query("SELECT id, full_name, email, is_active, payment_verified, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>ID</th><th>Name</th><th>Email</th><th>Active</th><th>Paid</th><th>Created</th></tr></thead>";
        echo "<tbody>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['full_name']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>" . ($row['is_active'] ? '✅' : '❌') . "</td>";
            echo "<td>" . ($row['payment_verified'] ? '✅' : '❌') . "</td>";
            echo "<td>" . date('M j, H:i', strtotime($row['created_at'])) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "⚠️ No users found in database<br>";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "❌ Error fetching users: " . $e->getMessage() . "<br>";
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='register.php' class='btn btn-primary btn-lg me-2'>
                            <i class='fas fa-user-plus me-2'></i>Go to Registration Page
                        </a>
                        <a href='index.php' class='btn btn-secondary btn-lg'>
                            <i class='fas fa-home me-2'></i>Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test Paystack functionality
        document.getElementById('testPaystackBtn').addEventListener('click', function() {
            const resultDiv = document.getElementById('paystackResult');
            
            try {
                if (typeof PaystackPop === 'undefined') {
                    resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ PaystackPop is not loaded</div>';
                    return;
                }
                
                resultDiv.innerHTML = '<div class=\"alert alert-info\">✅ PaystackPop is loaded. Testing popup...</div>';
                
                var handler = PaystackPop.setup({
                    key: '" . PAYSTACK_PUBLIC_KEY . "',
                    email: '<EMAIL>',
                    amount: 100000, // ₦1000 in kobo
                    currency: 'NGN',
                    ref: 'TEST_' + Math.floor((Math.random() * 1000000000) + 1),
                    metadata: {
                        user_id: 999,
                        purpose: 'test'
                    },
                    callback: function(response) {
                        resultDiv.innerHTML = '<div class=\"alert alert-success\">✅ Payment successful! Reference: ' + response.reference + '</div>';
                    },
                    onClose: function() {
                        resultDiv.innerHTML = '<div class=\"alert alert-warning\">⚠️ Payment popup was closed</div>';
                    }
                });
                
                handler.openIframe();
                
            } catch (error) {
                resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ JavaScript error: ' + error.message + '</div>';
            }
        });
        
        // Check if PaystackPop is loaded on page load
        document.addEventListener('DOMContentLoaded', function() {
            const resultDiv = document.getElementById('paystackResult');
            if (typeof PaystackPop !== 'undefined') {
                resultDiv.innerHTML = '<div class=\"alert alert-success\">✅ PaystackPop is loaded and ready</div>';
            } else {
                resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ PaystackPop is not loaded</div>';
            }
        });
    </script>
</body>
</html>";
?>
