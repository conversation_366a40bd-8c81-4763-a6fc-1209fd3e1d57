<?php
/**
 * API Endpoint to create replies to posts or other replies
 */

require_once '../config/db_config.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get request data
$postId = intval($_POST['post_id'] ?? 0);
$parentReplyId = intval($_POST['parent_reply_id'] ?? 0);
$content = trim($_POST['content'] ?? '');

if ($postId <= 0 || empty($content)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid post ID or empty content']);
    exit;
}

try {
    $conn = getConnection();
    
    // Create the reply
    $stmt = $conn->prepare("INSERT INTO forum_replies (post_id, user_id, content, parent_reply_id, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->bind_param("iisi", $postId, $userId, $content, $parentReplyId);
    
    if ($stmt->execute()) {
        $replyId = $conn->insert_id;
        
        // Update post reply count
        $updateStmt = $conn->prepare("UPDATE forum_posts SET reply_count = reply_count + 1 WHERE id = ?");
        $updateStmt->bind_param("i", $postId);
        $updateStmt->execute();
        $updateStmt->close();
        
        // Get the created reply with user info
        $getReplyStmt = $conn->prepare("
            SELECT r.*, u.full_name, u.profile_picture
            FROM forum_replies r
            JOIN users u ON r.user_id = u.id
            WHERE r.id = ?
        ");
        $getReplyStmt->bind_param("i", $replyId);
        $getReplyStmt->execute();
        $replyResult = $getReplyStmt->get_result();
        $reply = $replyResult->fetch_assoc();
        $getReplyStmt->close();
        
        $stmt->close();
        $conn->close();
        
        echo json_encode([
            'success' => true,
            'message' => 'Reply posted successfully!',
            'reply' => [
                'id' => $reply['id'],
                'content' => $reply['content'],
                'created_at' => $reply['created_at'],
                'parent_reply_id' => $reply['parent_reply_id'],
                'like_count' => 0,
                'user' => [
                    'id' => $reply['user_id'],
                    'name' => $reply['full_name'],
                    'profile_picture' => $reply['profile_picture']
                ],
                'user_liked' => false
            ]
        ]);
    } else {
        $stmt->close();
        $conn->close();
        echo json_encode(['error' => 'Failed to create reply']);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?> 