<?php
/**
 * Create Test User for Social Interaction Testing
 */

require_once 'config/database.php';

try {
    $conn = getConnection();
    
    // Check if test user already exists
    $checkUser = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $email = '<EMAIL>';
    $checkUser->bind_param("s", $email);
    $checkUser->execute();
    $result = $checkUser->get_result();
    
    if ($result->num_rows > 0) {
        echo "✅ Test user already exists!<br>";
        $user = $result->fetch_assoc();
        $userId = $user['id'];

        // Update user to ensure payment is verified
        $updateUser = $conn->prepare("UPDATE users SET payment_verified = 1, is_active = 1 WHERE id = ?");
        $updateUser->bind_param("i", $userId);
        $updateUser->execute();
        echo "✅ Test user payment status updated!<br>";
    } else {
        // Create test user
        $insertUser = $conn->prepare("
            INSERT INTO users (full_name, email, password_hash, ward, lga, state, referral_code, is_active, payment_verified) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 1, 1)
        ");
        
        $fullName = 'Test User';
        $email = '<EMAIL>';
        $passwordHash = password_hash('test123', PASSWORD_DEFAULT);
        $ward = 'Test Ward';
        $lga = 'Test LGA';
        $state = 'Test State';
        $referralCode = 'TEST' . rand(1000, 9999);
        
        $insertUser->bind_param("sssssss", $fullName, $email, $passwordHash, $ward, $lga, $state, $referralCode);
        
        if ($insertUser->execute()) {
            $userId = $conn->insert_id;
            echo "✅ Test user created successfully!<br>";
        } else {
            throw new Exception("Failed to create test user: " . $insertUser->error);
        }
    }
    
    // Create some test recordings if they don't exist
    $checkRecordings = $conn->prepare("SELECT COUNT(*) as count FROM screen_records WHERE user_id = ?");
    $checkRecordings->bind_param("i", $userId);
    $checkRecordings->execute();
    $recordingCount = $checkRecordings->get_result()->fetch_assoc()['count'];
    
    if ($recordingCount == 0) {
        // Create test recordings
        $insertRecording = $conn->prepare("
            INSERT INTO screen_records (user_id, file_path, file_size, duration, title, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $testRecordings = [
            ['uploads/test_recording_1.mp4', 1024000, 120, 'Test Recitation 1'],
            ['uploads/test_recording_2.mp4', 2048000, 180, 'Test Recitation 2'],
            ['uploads/test_recording_3.mp4', 1536000, 150, 'Test Recitation 3']
        ];
        
        foreach ($testRecordings as $recording) {
            $insertRecording->bind_param("isids", $userId, $recording[0], $recording[1], $recording[2], $recording[3]);
            $insertRecording->execute();
        }
        
        echo "✅ Test recordings created successfully!<br>";
    } else {
        echo "✅ Test recordings already exist!<br>";
    }
    
    echo "<br><h3>🎉 Test Setup Complete!</h3>";
    echo "<p><strong>Test User Credentials:</strong></p>";
    echo "<p>Email: <EMAIL></p>";
    echo "<p>Password: test123</p>";
    echo "<p><a href='login.php'>Login Now</a> | <a href='streams.php'>View Streams</a></p>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
    error_log("Test user creation error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test User Creation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container mt-4">
</body>
</html>
