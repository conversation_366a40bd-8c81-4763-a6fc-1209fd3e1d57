<?php
/**
 * Complete registration and payment flow test
 */
require_once 'config/db_config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Registration Flow Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <script src='https://js.paystack.co/v1/inline.js'></script>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Registration & Payment Flow Test</h2>";

// Test 1: Database Connection and Structure
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: Database Setup</h5>";
try {
    $conn = getConnection();
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Check users table structure
        $result = $conn->query("DESCRIBE users");
        $columns = [];
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        
        $requiredColumns = ['id', 'full_name', 'email', 'password_hash', 'ward', 'lga', 'state', 'is_active', 'payment_verified'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            echo "✅ All required columns exist in users table<br>";
        } else {
            echo "❌ Missing columns: " . implode(', ', $missingColumns) . "<br>";
        }
        
        $conn->close();
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 2: executeQuery Function Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: executeQuery Function Test</h5>";
try {
    // Test SELECT query
    $selectResult = executeQuery("SELECT COUNT(*) as count FROM users");
    if ($selectResult) {
        $count = $selectResult->fetch_assoc()['count'];
        echo "✅ SELECT query works: Found $count users<br>";
    } else {
        echo "❌ SELECT query failed<br>";
    }
    
    // Test INSERT query (we'll insert a test user and then delete it)
    $testEmail = 'test_' . time() . '@example.com';
    $insertResult = executeQuery(
        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
        'ssssss',
        ['Test User', $testEmail, password_hash('test123', PASSWORD_DEFAULT), 'Test Ward', 'Test LGA', 'Test State']
    );
    
    if ($insertResult && $insertResult->success && $insertResult->insert_id > 0) {
        $testUserId = $insertResult->insert_id;
        echo "✅ INSERT query works: Created user with ID $testUserId<br>";

        // Clean up test user
        $deleteResult = executeQuery("DELETE FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($deleteResult && $deleteResult->success) {
            echo "✅ Test user cleaned up<br>";
        } else {
            echo "⚠️ Test user cleanup may have failed<br>";
        }
    } else {
        echo "❌ INSERT query failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ executeQuery test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Paystack Configuration
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: Paystack Configuration</h5>";
if (defined('PAYSTACK_PUBLIC_KEY') && !empty(PAYSTACK_PUBLIC_KEY)) {
    echo "✅ PAYSTACK_PUBLIC_KEY is configured<br>";
    echo "Key: " . substr(PAYSTACK_PUBLIC_KEY, 0, 20) . "...<br>";
} else {
    echo "❌ PAYSTACK_PUBLIC_KEY is not configured<br>";
}

if (defined('PAYSTACK_SECRET_KEY') && !empty(PAYSTACK_SECRET_KEY)) {
    echo "✅ PAYSTACK_SECRET_KEY is configured<br>";
} else {
    echo "❌ PAYSTACK_SECRET_KEY is not configured<br>";
}
echo "</div>";

// Test 4: Session Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: Session Management</h5>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ Session is not active<br>";
}

// Test session variables
$_SESSION['test_var'] = 'test_value';
if (isset($_SESSION['test_var']) && $_SESSION['test_var'] === 'test_value') {
    echo "✅ Session variables work correctly<br>";
    unset($_SESSION['test_var']);
} else {
    echo "❌ Session variables not working<br>";
}
echo "</div>";

// Test 5: CSRF Token Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 5: CSRF Token System</h5>";
try {
    $token = generateCSRFToken();
    if ($token && validateCSRFToken($token)) {
        echo "✅ CSRF token generation and validation works<br>";
    } else {
        echo "❌ CSRF token system not working<br>";
    }
} catch (Exception $e) {
    echo "❌ CSRF token error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 6: JavaScript/Paystack Test
echo "<div class='alert alert-info'>";
echo "<h5>Test 6: Paystack JavaScript Integration</h5>";
echo "<button id='testPaystackBtn' class='btn btn-primary'>Test Paystack Popup</button>";
echo "<div id='paystackResult' class='mt-2'></div>";
echo "</div>";

// Test 7: File Permissions
echo "<div class='alert alert-info'>";
echo "<h5>Test 7: File Permissions</h5>";
$files = ['register.php', 'verify_payment.php', 'config/db_config.php'];
foreach ($files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "✅ $file is readable<br>";
        } else {
            echo "❌ $file is not readable<br>";
        }
    } else {
        echo "❌ $file does not exist<br>";
    }
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='test-registration-simple.php' class='btn btn-success btn-lg me-2'>
                            <i class='fas fa-user-plus me-2'></i>Test Simple Registration
                        </a>
                        <a href='register.php' class='btn btn-primary btn-lg me-2'>
                            <i class='fas fa-user-plus me-2'></i>Go to Real Registration
                        </a>
                        <a href='debug-registration.php' class='btn btn-info btn-lg'>
                            <i class='fas fa-bug me-2'></i>Full Debug
                        </a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>What This Test Checks:</h6>
                        <ul class='small'>
                            <li>✅ Database connection and table structure</li>
                            <li>✅ executeQuery function for SELECT and INSERT</li>
                            <li>✅ Paystack API key configuration</li>
                            <li>✅ Session management</li>
                            <li>✅ CSRF token system</li>
                            <li>✅ Paystack JavaScript integration</li>
                            <li>✅ File permissions and accessibility</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test Paystack functionality
        document.getElementById('testPaystackBtn').addEventListener('click', function() {
            const resultDiv = document.getElementById('paystackResult');
            
            try {
                if (typeof PaystackPop === 'undefined') {
                    resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ PaystackPop is not loaded</div>';
                    return;
                }
                
                resultDiv.innerHTML = '<div class=\"alert alert-info\">✅ PaystackPop is loaded. Testing popup...</div>';
                
                var handler = PaystackPop.setup({
                    key: '" . PAYSTACK_PUBLIC_KEY . "',
                    email: '<EMAIL>',
                    amount: 100000, // ₦1000 in kobo
                    currency: 'NGN',
                    ref: 'TEST_' + Math.floor((Math.random() * 1000000000) + 1),
                    metadata: {
                        user_id: 999,
                        purpose: 'test'
                    },
                    callback: function(response) {
                        resultDiv.innerHTML = '<div class=\"alert alert-success\">✅ Payment successful! Reference: ' + response.reference + '</div>';
                    },
                    onClose: function() {
                        resultDiv.innerHTML = '<div class=\"alert alert-warning\">⚠️ Payment popup was closed</div>';
                    }
                });
                
                handler.openIframe();
                
            } catch (error) {
                resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ JavaScript error: ' + error.message + '</div>';
            }
        });
        
        // Check if PaystackPop is loaded on page load
        document.addEventListener('DOMContentLoaded', function() {
            const resultDiv = document.getElementById('paystackResult');
            if (typeof PaystackPop !== 'undefined') {
                resultDiv.innerHTML = '<div class=\"alert alert-success\">✅ PaystackPop is loaded and ready</div>';
            } else {
                resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ PaystackPop is not loaded</div>';
            }
        });
    </script>
</body>
</html>";
?>
