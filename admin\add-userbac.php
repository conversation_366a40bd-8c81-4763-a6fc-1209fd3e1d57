<?php
$page_title = 'Add New User';
require_once __DIR__ . '/../components/admin_header.php';

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        // Get form data
        $username = sanitize($_POST['username'] ?? '');
        $email = sanitize($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $fullName = sanitize($_POST['full_name'] ?? '');
        $phoneNumber = sanitize($_POST['phone_number'] ?? '');
        $ward = sanitize($_POST['ward'] ?? '');
        $state = sanitize($_POST['state'] ?? '');
        $country = sanitize($_POST['country'] ?? 'Nigeria');
        $referralCode = sanitize($_POST['referral_code'] ?? '');
        $initialBalance = floatval($_POST['initial_balance'] ?? 0);
        
        // Validation
        $errors = [];
        
        if (empty($username)) $errors[] = 'Username is required';
        if (empty($email)) $errors[] = 'Email is required';
        if (!validateEmail($email)) $errors[] = 'Invalid email format';
        if (empty($password)) $errors[] = 'Password is required';
        if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';
        if (empty($fullName)) $errors[] = 'Full name is required';
        if (empty($phoneNumber)) $errors[] = 'Phone number is required';
        if (empty($ward)) $errors[] = 'Ward is required';
        if (empty($state)) $errors[] = 'State is required';
        
        if (empty($errors)) {
            try {
                $conn = getConnection();
                
                // Check if username or email already exists
                $checkStmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
                $checkStmt->bind_param("ss", $username, $email);
                $checkStmt->execute();
                $result = $checkStmt->get_result();
                
                if ($result->num_rows > 0) {
                    $errors[] = 'Username or email already exists';
                } else {
                    // Generate unique referral code if not provided
                    if (empty($referralCode)) {
                        do {
                            $referralCode = generateReferralCode();
                            $refCheckStmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ?");
                            $refCheckStmt->bind_param("s", $referralCode);
                            $refCheckStmt->execute();
                            $refExists = $refCheckStmt->get_result()->num_rows > 0;
                            $refCheckStmt->close();
                        } while ($refExists);
                    }
                    
                    // Hash password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert user
                    $insertStmt = $conn->prepare("
                        INSERT INTO users (username, email, password, full_name, phone_number, ward, state, country, referral_code, wallet_balance, is_active, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
                    ");
                    $insertStmt->bind_param("sssssssssd", $username, $email, $hashedPassword, $fullName, $phoneNumber, $ward, $state, $country, $referralCode, $initialBalance);
                    
                    if ($insertStmt->execute()) {
                        $userId = $conn->insert_id;
                        
                        // Log admin action
                        logAdminAction($_SESSION['admin_username'] ?? 'admin', 'CREATE_USER', $userId, "Created user: $username ($email)");
                        
                        // Create initial wallet transaction if balance > 0
                        if ($initialBalance > 0) {
                            $transactionStmt = $conn->prepare("
                                INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, status, created_at) 
                                VALUES (?, 'credit', ?, 'Initial balance by admin', 'completed', NOW())
                            ");
                            $transactionStmt->bind_param("id", $userId, $initialBalance);
                            $transactionStmt->execute();
                            $transactionStmt->close();
                        }
                        
                        $message = "User '$username' created successfully! User ID: $userId";
                        $messageType = 'success';
                        
                        // Clear form data
                        $username = $email = $fullName = $phoneNumber = $ward = $state = $country = '';
                        $initialBalance = 0;
                        
                    } else {
                        $errors[] = 'Failed to create user: ' . $insertStmt->error;
                    }
                    $insertStmt->close();
                }
                $checkStmt->close();
                $conn->close();
                
            } catch (Exception $e) {
                logError("Add user error: " . $e->getMessage());
                $errors[] = 'Database error occurred. Please try again.';
            }
        }
        
        if (!empty($errors)) {
            $message = implode('<br>', $errors);
            $messageType = 'danger';
        }
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </h5>
                </div>
                <div class="card-body">
                    
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>Basic Information
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide a valid username.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           minlength="6" required>
                                    <div class="invalid-feedback">Password must be at least 6 characters.</div>
                                    <div class="form-text">Minimum 6 characters required.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($fullName ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide the full name.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone_number" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                           value="<?php echo htmlspecialchars($phoneNumber ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide a valid phone number.</div>
                                </div>
                            </div>
                            
                            <!-- Location & Settings -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Location & Settings
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="ward" class="form-label">Ward *</label>
                                    <input type="text" class="form-control" id="ward" name="ward" 
                                           value="<?php echo htmlspecialchars($ward ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide the ward.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="state" class="form-label">State *</label>
                                    <input type="text" class="form-control" id="state" name="state" 
                                           value="<?php echo htmlspecialchars($state ?? ''); ?>" required>
                                    <div class="invalid-feedback">Please provide the state.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="country" class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="country" 
                                           value="<?php echo htmlspecialchars($country ?? 'Nigeria'); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="referral_code" class="form-label">Referral Code</label>
                                    <input type="text" class="form-control" id="referral_code" name="referral_code" 
                                           value="<?php echo htmlspecialchars($referralCode ?? ''); ?>">
                                    <div class="form-text">Leave empty to auto-generate a unique code.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="initial_balance" class="form-label">Initial Wallet Balance (₦)</label>
                                    <input type="number" class="form-control" id="initial_balance" name="initial_balance" 
                                           value="<?php echo $initialBalance ?? 0; ?>" min="0" step="0.01">
                                    <div class="form-text">Optional: Set an initial wallet balance for the user.</div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="d-flex justify-content-between">
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-generate referral code
document.getElementById('referral_code').addEventListener('focus', function() {
    if (this.value === '') {
        this.value = Math.random().toString(36).substr(2, 8).toUpperCase();
    }
});
</script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
