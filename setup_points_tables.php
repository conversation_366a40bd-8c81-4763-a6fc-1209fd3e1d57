<?php
/**
 * Setup Points System Database Tables
 */

require_once 'config/db_config.php';

echo "<h1>Setting up Points System Tables</h1>";

try {
    $conn = getConnection();
    
    // Create user_points table
    $sql = "CREATE TABLE IF NOT EXISTS `user_points` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `total_points` int NOT NULL DEFAULT 0,
        `current_streak` int NOT NULL DEFAULT 0,
        `best_streak` int NOT NULL DEFAULT 0,
        `last_activity_date` date NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_id` (`user_id`),
        KEY `total_points` (`total_points`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ user_points table created successfully<br>";
    } else {
        echo "❌ Error creating user_points table: " . $conn->error . "<br>";
    }
    
    // Create point_transactions table
    $sql = "CREATE TABLE IF NOT EXISTS `point_transactions` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `points` int NOT NULL,
        `transaction_type` varchar(50) NOT NULL,
        `description` text,
        `reference_id` varchar(100) NULL,
        `reference_type` varchar(50) NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `transaction_type` (`transaction_type`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ point_transactions table created successfully<br>";
    } else {
        echo "❌ Error creating point_transactions table: " . $conn->error . "<br>";
    }
    
    // Check if total_points column exists in users table
    $result = $conn->query("SHOW COLUMNS FROM users LIKE 'total_points'");
    if ($result->num_rows == 0) {
        // Add total_points column to users table
        $sql = "ALTER TABLE users ADD COLUMN total_points int NOT NULL DEFAULT 0 AFTER wallet_balance";
        if ($conn->query($sql)) {
            echo "✅ total_points column added to users table<br>";
        } else {
            echo "❌ Error adding total_points column: " . $conn->error . "<br>";
        }
    } else {
        echo "✅ total_points column already exists in users table<br>";
    }
    
    // Create recitation_evaluations table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS `recitation_evaluations` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `recording_id` varchar(100) NOT NULL,
        `recording_type` varchar(50) NOT NULL,
        `accuracy_score` int NOT NULL DEFAULT 100,
        `duration_seconds` int NOT NULL DEFAULT 0,
        `points_earned` int NOT NULL DEFAULT 0,
        `evaluation_status` varchar(20) NOT NULL DEFAULT 'pending',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `recording_id` (`recording_id`),
        KEY `evaluation_status` (`evaluation_status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ recitation_evaluations table created successfully<br>";
    } else {
        echo "❌ Error creating recitation_evaluations table: " . $conn->error . "<br>";
    }
    
    // Create referrals table if it doesn't exist
    $sql = "CREATE TABLE IF NOT EXISTS `referrals` (
        `id` int NOT NULL AUTO_INCREMENT,
        `referrer_id` int NOT NULL,
        `referred_id` int NOT NULL,
        `referral_code` varchar(20) NOT NULL,
        `status` varchar(20) NOT NULL DEFAULT 'pending',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `referred_id` (`referred_id`),
        KEY `referrer_id` (`referrer_id`),
        KEY `referral_code` (`referral_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ referrals table created successfully<br>";
    } else {
        echo "❌ Error creating referrals table: " . $conn->error . "<br>";
    }
    
    echo "<br>🎉 Points system tables setup completed!<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?> 