<?php
/**
 * Simple Forum Database Setup
 */

require_once 'config/db_config.php';

$conn = getConnection();

if (!$conn) {
    die("Database connection failed");
}

echo "Setting up forum database tables...\n";

try {
    // Create forum_posts table
    $sql = "CREATE TABLE IF NOT EXISTS `forum_posts` (
        `id` int NOT NULL AUTO_INCREMENT,
        `user_id` int NOT NULL,
        `title` varchar(255) NOT NULL,
        `content` text NOT NULL,
        `category` varchar(50) NOT NULL DEFAULT 'general',
        `like_count` int NOT NULL DEFAULT 0,
        `reply_count` int NOT NULL DEFAULT 0,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `category` (`category`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ forum_posts table created\n";
    }

    // Create forum_replies table
    $sql = "CREATE TABLE IF NOT EXISTS `forum_replies` (
        `id` int NOT NULL AUTO_INCREMENT,
        `post_id` int NOT NULL,
        `user_id` int NOT NULL,
        `content` text NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `post_id` (`post_id`),
        KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ forum_replies table created\n";
    }

    // Create forum_likes table
    $sql = "CREATE TABLE IF NOT EXISTS `forum_likes` (
        `id` int NOT NULL AUTO_INCREMENT,
        `post_id` int NULL,
        `reply_id` int NULL,
        `user_id` int NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_like` (`post_id`, `reply_id`, `user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
    
    if ($conn->query($sql)) {
        echo "✅ forum_likes table created\n";
    }

    echo "\n🎉 Forum database setup completed!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 