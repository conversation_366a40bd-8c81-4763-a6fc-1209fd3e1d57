<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Management | EduSphere LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --secondary: #10b981;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --accent: #f59e0b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: var(--dark);
        }
        
        .font-heading {
            font-family: 'Plus Jakarta Sans', sans-serif;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
        }
        
        .sidebar {
            transition: all 0.3s ease;
        }
        
        .data-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .data-table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
        }
        
        .data-table tbody tr:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
        }
        
        .status-badge i {
            margin-right: 0.25rem;
        }
        
        .course-card:hover .course-image {
            transform: scale(1.03);
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="antialiased bg-slate-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-white w-64 border-r border-slate-200 flex-shrink-0 hidden md:block">
            <div class="flex items-center justify-center h-16 px-4 border-b border-slate-200">
                <a href="#" class="flex items-center">
                    <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <span class="font-heading text-xl font-bold text-slate-800">EduSphere</span>
                </a>
            </div>
            
            <div class="p-4">
                <!-- User Profile -->
                <div class="flex items-center mb-6 p-3 rounded-lg bg-slate-50">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Admin" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <h4 class="font-medium text-slate-800">Admin User</h4>
                        <p class="text-xs text-slate-500">Super Admin</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tachometer-alt mr-3 text-slate-500"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-users mr-3 text-slate-500"></i>
                        User Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-indigo-50 text-indigo-700">
                        <i class="fas fa-book mr-3 text-indigo-600"></i>
                        Course Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chalkboard-teacher mr-3 text-slate-500"></i>
                        Instructor Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tasks mr-3 text-slate-500"></i>
                        Content Management
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-certificate mr-3 text-slate-500"></i>
                        Certifications
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chart-bar mr-3 text-slate-500"></i>
                        Analytics & Reports
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-cog mr-3 text-slate-500"></i>
                        System Settings
                    </a>
                </nav>
                
                <!-- Bottom Links -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-200">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-question-circle mr-3 text-slate-500"></i>
                        Help Center
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-sign-out-alt mr-3 text-slate-500"></i>
                        Sign Out
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white border-b border-slate-200">
                <div class="flex items-center justify-between h-16 px-4">
                    <div class="flex items-center">
                        <button id="open-sidebar" class="md:hidden text-slate-500 hover:text-slate-700 mr-2">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="font-heading text-xl font-bold text-slate-800">Course Management</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100 relative">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100 relative">
                            <i class="fas fa-envelope text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-blue-500 rounded-full"></span>
                        </button>
                        <div class="relative">
                            <button class="flex items-center focus:outline-none" id="user-menu-button">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Admin" class="w-8 h-8 rounded-full">
                                <span class="ml-2 text-sm font-medium text-slate-700 hidden md:inline">Admin</span>
                                <i class="fas fa-chevron-down ml-1 text-xs text-slate-500 hidden md:inline"></i>
                            </button>
                            
                            <!-- Dropdown menu -->
                            <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" id="user-menu">
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Sign out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-4 md:p-6">
                <!-- Page Header -->
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                    <div>
                        <h2 class="font-heading text-2xl font-bold text-slate-800">Manage Courses</h2>
                        <p class="text-slate-600">Create, organize, and monitor all courses in the system</p>
                    </div>
                    <div class="mt-4 md:mt-0 flex space-x-3">
                        <button class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition shadow-md hover:shadow-indigo-200 flex items-center">
                            <i class="fas fa-plus mr-2"></i> New Course
                        </button>
                        <button class="px-4 py-2 border border-slate-300 text-slate-700 hover:border-indigo-300 hover:text-indigo-600 font-medium rounded-lg transition flex items-center">
                            <i class="fas fa-download mr-2"></i> Export
                        </button>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="bg-white rounded-xl p-4 border border-slate-200 shadow-sm mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="md:col-span-2">
                            <label for="search" class="sr-only">Search</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-slate-400"></i>
                                </div>
                                <input type="text" id="search" class="block w-full pl-10 pr-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Search courses...">
                            </div>
                        </div>
                        <div>
                            <label for="category" class="sr-only">Category</label>
                            <select id="category" class="block w-full pl-3 pr-10 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Categories</option>
                                <option value="technology">Technology</option>
                                <option value="business">Business</option>
                                <option value="design">Design</option>
                                <option value="marketing">Marketing</option>
                                <option value="science">Science</option>
                            </select>
                        </div>
                        <div>
                            <label for="status" class="sr-only">Status</label>
                            <select id="status" class="block w-full pl-3 pr-10 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Statuses</option>
                                <option value="published">Published</option>
                                <option value="draft">Draft</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Courses Grid View -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-heading text-lg font-bold text-slate-800">Courses</h3>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 text-slate-500 hover:text-indigo-600 rounded-lg hover:bg-slate-100">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button class="p-2 text-indigo-600 bg-indigo-50 rounded-lg">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Course 1 -->
                        <div class="course-card bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-48 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image">
                                <div class="absolute top-3 right-3">
                                    <span class="status-badge bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle"></i> Published
                                    </span>
                                </div>
                            </div>
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="font-heading text-lg font-bold text-slate-800">Data Science Fundamentals</h3>
                                    <span class="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 rounded-full">Technology</span>
                                </div>
                                <p class="text-sm text-slate-600 mb-4">Master the essential skills for data analysis, visualization, and machine learning.</p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Instructor" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-xs text-slate-600">Michael Chen</span>
                                    </div>
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>342</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-slate-100 flex justify-between">
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>24h</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-slate-600 hover:text-slate-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 2 -->
                        <div class="course-card bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-48 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image">
                                <div class="absolute top-3 right-3">
                                    <span class="status-badge bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle"></i> Published
                                    </span>
                                </div>
                            </div>
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="font-heading text-lg font-bold text-slate-800">Web Development Bootcamp</h3>
                                    <span class="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 rounded-full">Technology</span>
                                </div>
                                <p class="text-sm text-slate-600 mb-4">Learn to build modern web applications with React, Node.js, and MongoDB.</p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Instructor" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-xs text-slate-600">Sarah Johnson</span>
                                    </div>
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>587</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-slate-100 flex justify-between">
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>36h</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-slate-600 hover:text-slate-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 3 -->
                        <div class="course-card bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-48 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1605379399642-870262d3d051?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image">
                                <div class="absolute top-3 right-3">
                                    <span class="status-badge bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-pencil-alt"></i> Draft
                                    </span>
                                </div>
                            </div>
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="font-heading text-lg font-bold text-slate-800">Digital Marketing Mastery</h3>
                                    <span class="text-xs font-medium px-2 py-1 bg-purple-100 text-purple-800 rounded-full">Marketing</span>
                                </div>
                                <p class="text-sm text-slate-600 mb-4">Comprehensive training on SEO, social media, content marketing, and analytics.</p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="Instructor" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-xs text-slate-600">David Wilson</span>
                                    </div>
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>215</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-slate-100 flex justify-between">
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>18h</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-slate-600 hover:text-slate-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 4 -->
                        <div class="course-card bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-48 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1542626991-cbc4e32524cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image">
                                <div class="absolute top-3 right-3">
                                    <span class="status-badge bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle"></i> Published
                                    </span>
                                </div>
                            </div>
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="font-heading text-lg font-bold text-slate-800">Python for Beginners</h3>
                                    <span class="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 rounded-full">Technology</span>
                                </div>
                                <p class="text-sm text-slate-600 mb-4">Learn Python programming from scratch with hands-on exercises and projects.</p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Instructor" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-xs text-slate-600">Emma Rodriguez</span>
                                    </div>
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>478</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-slate-100 flex justify-between">
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>15h</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-slate-600 hover:text-slate-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 5 -->
                        <div class="course-card bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-48 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image">
                                <div class="absolute top-3 right-3">
                                    <span class="status-badge bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle"></i> Published
                                    </span>
                                </div>
                            </div>
                            <div class="p-5">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="font-heading text-lg font-bold text-slate-800">Business Analytics</h3>
                                    <span class="text-xs font-medium px-2 py-1 bg-indigo-100 text-indigo-800 rounded-full">Business</span>
                                </div>
                                <p class="text-sm text-slate-600 mb-4">Transform data into insights and make better business decisions.</p>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="https://randomuser.me/api/portraits/men/42.jpg" alt="Instructor" class="w-6 h-6 rounded-full mr-2">
                                        <span class="text-xs text-slate-600">Robert Brown</span>
                                    </div>
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>321</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 pt-4 border-t border-slate-100 flex justify-between">
                                    <div class="flex items-center text-sm text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>20h</span>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-slate-600 hover:text-slate-900">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Add New Course Card -->
                        <div class="bg-white rounded-xl border-2 border-dashed border-slate-300 hover:border-indigo-300 transition flex items-center justify-center">
                            <button class="flex flex-col items-center p-8 text-center text-slate-500 hover:text-indigo-600">
                                <div class="w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center mb-3">
                                    <i class="fas fa-plus text-xl"></i>
                                </div>
                                <h3 class="font-medium text-slate-800 mb-1">Add New Course</h3>
                                <p class="text-sm">Create a new course from scratch</p>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Courses Table View (Hidden by default) -->
                <div class="hidden">
                    <div class="bg-white rounded-xl border border-slate-200 shadow-sm overflow-hidden mb-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-slate-200 data-table">
                                <thead class="bg-slate-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                            <div class="flex items-center">
                                                <input id="select-all-courses" name="select-all-courses" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                                                <span class="ml-2">Course</span>
                                            </div>
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Instructor</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Category</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Students</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-slate-200">
                                    <!-- Table rows would go here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-slate-200 rounded-b-lg shadow-sm sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50">
                            Previous
                        </a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50">
                            Next
                        </a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-slate-700">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">24</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-slate-300 bg-white text-sm font-medium text-slate-500 hover:bg-slate-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <a href="#" aria-current="page" class="z-10 bg-indigo-50 border-indigo-500 text-indigo-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                <a href="#" class="bg-white border-slate-300 text-slate-500 hover:bg-slate-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </a>
                                <a href="#" class="bg-white border-slate-300 text-slate-500 hover:bg-slate-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </a>
                                <span class="relative inline-flex items-center px-4 py-2 border border-slate-300 bg-white text-sm font-medium text-slate-700">
                                    ...
                                </span>
                                <a href="#" class="bg-white border-slate-300 text-slate-500 hover:bg-slate-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    8
                                </a>
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-slate-300 bg-white text-sm font-medium text-slate-500 hover:bg-slate-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Mobile sidebar toggle
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const openSidebarBtn = document.getElementById('open-sidebar');
        const closeSidebarBtn = document.getElementById('close-sidebar');
        
        openSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.remove('-translate-x-full');
        });
        
        closeSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.add('-translate-x-full');
        });
        
        // User menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');
        
        userMenuButton.addEventListener('click', () => {
            userMenu.classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!userMenuButton.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });
        
        // Toggle between grid and list view
        const gridViewBtn = document.querySelectorAll('[aria-label="View buttons"] button');
        const coursesGridView = document.getElementById('courses-grid-view');
        const coursesTableView = document.getElementById('courses-table-view');
        
        gridViewBtn.forEach(btn => {
            btn.addEventListener('click', () => {
                gridViewBtn.forEach(b => b.classList.remove('text-indigo-600', 'bg-indigo-50'));
                btn.classList.add('text-indigo-600', 'bg-indigo-50');
                
                if (btn.querySelector('svg').classList.contains('fa-th-large')) {
                    coursesGridView.classList.remove('hidden');
                    coursesTableView.classList.add('hidden');
                } else {
                    coursesGridView.classList.add('hidden');
                    coursesTableView.classList.remove('hidden');
                }
            });
        });
    </script>
</body>
</html>