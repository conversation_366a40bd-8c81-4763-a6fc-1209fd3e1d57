# Centralized Database Configuration Guide

## Overview
All database credentials are now stored in **ONE FILE ONLY**: `config/db_config.php`

This eliminates the confusion of having database settings scattered across multiple files.

## Single Configuration File

### Location
```
config/db_config.php
```

### What's Included
- ✅ Database credentials (host, name, user, password)
- ✅ Application URLs and settings
- ✅ Paystack configuration
- ✅ Admin credentials
- ✅ Database connection function
- ✅ All utility functions
- ✅ Session management
- ✅ CSRF token functions

## Quick Setup

### Option 1: Use Configuration Tool
1. Visit: `https://universalreciters.name.ng/configure_database.php`
2. Enter your database credentials
3. Click "Update Database Configuration"
4. Done!

### Option 2: Manual Edit
Edit `config/db_config.php` and update these lines:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'univers1_recite_app');     // Your database name
define('DB_USER', 'univers1_recite_app');     // Your database username  
define('DB_PASS', 'your_database_password');  // Your database password
```

## Files Updated

### Modified Files
- ✅ `config.php` - Now loads only `db_config.php`
- ✅ `admin/login.php` - Uses centralized config
- ✅ All other files automatically use the centralized config

### Removed Dependencies
- ❌ `config/server_config.php` - No longer needed
- ❌ `config/database.php` - Functions moved to `db_config.php`
- ❌ Multiple configuration files - All consolidated

## Benefits

### Before (Multiple Files)
```
config/
├── server_config.php    (database settings)
├── database.php         (functions)
├── production_setup.php (more settings)
└── config.php          (main config)
```

### After (Single File)
```
config/
└── db_config.php       (EVERYTHING in one place)
```

## Database Credentials Format

Based on your error message, your credentials should be:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'univers1_recite_app');
define('DB_USER', 'univers1_recite_app');
define('DB_PASS', 'your_actual_password_here');
```

**Note:** Make sure there are no extra spaces in the username or password.

## Error Resolution

### Previous Error
```
Access denied for user 'univers1_recite_app '@'localhost'
```

This was caused by:
1. Extra space in username
2. Incorrect password
3. Multiple configuration files conflicting

### Solution
With the centralized config, you only need to update credentials in ONE place:
`config/db_config.php`

## Testing

### Test Database Connection
1. Visit: `https://universalreciters.name.ng/configure_database.php`
2. Enter your credentials
3. Click "Update Database Configuration"
4. If successful, try admin login

### Test Admin Login
- **URL:** `https://universalreciters.name.ng/admin/login.php`
- **Username:** admin
- **Password:** 1@3Usazladan

## File Structure

### New Structure
```
config/
└── db_config.php          # SINGLE configuration file
    ├── Database credentials
    ├── Connection function
    ├── Utility functions
    ├── Session management
    ├── CSRF functions
    └── Admin functions

admin/
└── login.php              # Uses db_config.php

configure_database.php     # Setup tool
```

## Security Notes

1. **Single Point of Configuration** - Easier to secure one file
2. **No Scattered Credentials** - All sensitive data in one place
3. **Consistent Settings** - No conflicts between files
4. **Easy Backup** - Just backup `db_config.php`

## Next Steps

1. **Configure Database:**
   - Use `configure_database.php` OR
   - Manually edit `config/db_config.php`

2. **Import Database:**
   - Use `database/recite_app_fixed.sql` (collation fixed)
   - OR use `database/admin_table_fixed.sql` (admin table only)

3. **Test Login:**
   - Visit `admin/login.php`
   - Use: admin / 1@3Usazladan

4. **Cleanup:**
   - Delete setup files after successful configuration
   - Keep only `config/db_config.php`

## Support

If you still have issues:
1. Check the error logs in `/logs/php_errors.log`
2. Verify database credentials in your hosting control panel
3. Ensure database user has proper permissions
4. Make sure database exists

The centralized configuration makes troubleshooting much easier since everything is in one place!
