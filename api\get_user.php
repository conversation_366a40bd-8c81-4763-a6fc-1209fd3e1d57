<?php
/**
 * Get User API for Admin Panel
 * Returns user data for editing
 */

// Turn off error reporting for clean JSON
error_reporting(0);
ini_set('display_errors', 0);

// Prevent any output before JSON
ob_start();

try {
    require_once '../config/db_config.php';
} catch (Exception $e) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Config error: ' . $e->getMessage()]);
    exit;
}

// Clear any previous output
ob_clean();

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check admin authentication
$referer = $_SERVER['HTTP_REFERER'] ?? '';
$isAdminRequest = strpos($referer, '/admin/') !== false;

// Allow access if request comes from admin directory or if admin is logged in
if (!$isAdminRequest && (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username']))) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Admin authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = intval($_GET['id'] ?? 0);

if (!$userId) {
    echo json_encode(['success' => false, 'message' => 'Invalid user ID']);
    exit;
}

try {
    $conn = getConnection();

    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Fetch user data
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    if (!$stmt) {
        throw new Exception('Failed to prepare statement');
    }

    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        // Remove sensitive data
        unset($user['password_hash']);

        $response = [
            'success' => true,
            'user' => $user
        ];
    } else {
        $response = ['success' => false, 'message' => 'User not found'];
    }

    $stmt->close();
    $conn->close();

} catch (Exception $e) {
    error_log("Get user error: " . $e->getMessage());
    $response = ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
}

// Ensure clean JSON output
ob_clean();
echo json_encode($response);
exit;
?>
