<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SupportAI - AI-Powered Virtual Customer Support</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #f8fafc;
            --text-dark: #1e293b;
            --text-light: #64748b;
            --accent-color: #10b981;
            --border-color: #e2e8f0;
            --gradient-primary: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        nav {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-cta {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-secondary {
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            background: white;
            color: var(--text-dark);
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--secondary-color);
        }

        .btn-primary {
            padding: 0.5rem 1.5rem;
            background: var(--gradient-primary);
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        /* Mobile menu */
        .mobile-menu {
            display: none;
            flex-direction: column;
            cursor: pointer;
        }

        .mobile-menu span {
            width: 25px;
            height: 3px;
            background: var(--text-dark);
            margin: 3px 0;
            transition: 0.3s;
        }

        /* Hero Section */
        .hero {
            padding: 8rem 2rem 6rem;
            background: var(--gradient-secondary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%232563eb" stop-opacity="0.1"/><stop offset="100%" stop-color="%232563eb" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="300" fill="url(%23a)"/><circle cx="800" cy="800" r="400" fill="url(%23a)"/></svg>') no-repeat center/cover;
            opacity: 0.5;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }

        .hero-text .highlight {
            color: var(--primary-color);
        }

        .hero-text p {
            font-size: 1.25rem;
            color: var(--text-light);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-cta {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 0.75rem;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-top: 3rem;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-image {
            width: 100%;
            max-width: 500px;
            height: 400px;
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 50px rgba(37, 99, 235, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .chat-demo {
            width: 90%;
            height: 90%;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
        }

        .chat-message {
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            max-width: 80%;
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
        }

        .chat-message.user {
            background: var(--primary-color);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 0.25rem;
        }

        .chat-message.ai {
            background: var(--secondary-color);
            color: var(--text-dark);
            border-bottom-left-radius: 0.25rem;
        }

        .chat-message:nth-child(1) { animation-delay: 0.5s; }
        .chat-message:nth-child(2) { animation-delay: 1s; }
        .chat-message:nth-child(3) { animation-delay: 1.5s; }

        /* Features Section */
        .features {
            padding: 6rem 2rem;
            background: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            padding: 2rem;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(37, 99, 235, 0.1);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .feature-card p {
            color: var(--text-light);
            line-height: 1.6;
        }

        /* Benefits Section */
        .benefits {
            padding: 6rem 2rem;
            background: var(--secondary-color);
        }

        .benefits-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .benefits-list {
            list-style: none;
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .benefit-item:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(37, 99, 235, 0.1);
        }

        .benefit-icon {
            width: 40px;
            height: 40px;
            background: var(--accent-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .benefit-text h4 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .benefit-text p {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .benefits-visual {
            position: relative;
        }

        .metrics-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(37, 99, 235, 0.1);
            text-align: center;
        }

        .metric {
            margin-bottom: 2rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Pricing Section */
        .pricing {
            padding: 6rem 2rem;
            background: white;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .pricing-card.popular {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        .pricing-card.popular::before {
            content: 'Most Popular';
            position: absolute;
            top: -1rem;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 2rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(37, 99, 235, 0.1);
        }

        .pricing-card.popular:hover {
            transform: scale(1.05) translateY(-10px);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .plan-period {
            color: var(--text-light);
            margin-bottom: 2rem;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 2rem;
            text-align: left;
        }

        .plan-features li {
            padding: 0.5rem 0;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .plan-features .check {
            color: var(--accent-color);
            font-weight: 600;
        }

        /* CTA Section */
        .cta {
            padding: 6rem 2rem;
            background: var(--gradient-primary);
            color: white;
            text-align: center;
        }

        .cta h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-white {
            background: white;
            color: var(--primary-color);
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-white:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
        }

        /* Footer */
        footer {
            background: var(--text-dark);
            color: white;
            padding: 3rem 2rem 1rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 2rem;
        }

        .footer-brand h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .footer-brand p {
            color: #94a3b8;
            line-height: 1.6;
        }

        .footer-links h4 {
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links a {
            color: #94a3b8;
            text-decoration: none;
            line-height: 2;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            margin-top: 2rem;
            padding-top: 2rem;
            text-align: center;
            color: #94a3b8;
        }

        /* Animations */
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links, .nav-cta {
                display: none;
            }

            .mobile-menu {
                display: flex;
            }

            .hero-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .hero-cta {
                justify-content: center;
            }

            .hero-stats {
                justify-content: center;
            }

            .benefits-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card.popular {
                transform: none;
            }

            .pricing-card.popular:hover {
                transform: translateY(-10px);
            }

            .footer-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .section-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .hero {
                padding: 6rem 1rem 4rem;
            }

            .hero-text h1 {
                font-size: 2rem;
            }

            .hero-text p {
                font-size: 1rem;
            }

            .btn-large {
                padding: 0.75rem 1.5rem;
                font-size: 1rem;
            }

            .features, .benefits, .pricing, .cta {
                padding: 4rem 1rem;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Loading animation for chat demo */
        .typing-indicator {
            display: flex;
            gap: 4px;
            padding: 1rem;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: var(--text-light);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <a href="#" class="logo">SupportAI</a>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#benefits">Benefits</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="nav-cta">
                <a href="#" class="btn-secondary">Sign In</a>
                <a href="#" class="btn-primary">Get Started</a>
            </div>
            <div class="mobile-menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Transform Customer Support with <span class="highlight">AI Virtual Assistants</span></h1>
                <p>Deliver instant, intelligent, and personalized customer support 24/7. Reduce response times by 95% and boost customer satisfaction with our advanced AI technology.</p>
                <div class="hero-cta">
                    <a href="#" class="btn-primary btn-large">Start Free Trial</a>
                    <a href="#" class="btn-secondary btn-large">Watch Demo</a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">Faster Response</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Availability</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Languages</div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-image">
                    <div class="chat-demo">
                        <div class="chat-message user">Hi, I need help with my order status</div>
                        <div class="chat-message ai">Hello! I'd be happy to help you check your order status. Could you please provide your order number?</div>
                        <div class="chat-message user">#ORD12345</div>
                        <div class="chat-message ai">Perfect! Your order #ORD12345 is currently being prepared and will ship tomorrow. You'll receive tracking info via email.</div>
                        <div class="typing-indicator">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful AI Features</h2>
                <p class="section-subtitle">Everything you need to deliver exceptional customer support experiences</p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>Intelligent Conversations</h3>
                    <p>Advanced natural language processing that understands context, emotions, and intent to provide human-like responses.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>24/7 Availability</h3>
                    <p>Never miss a customer inquiry. Our AI assistants work around the clock to provide instant support whenever needed.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3>Multi-language Support</h3>
                    <p>Communicate with customers in over 50 languages with real-time translation and culturally appropriate responses.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Analytics & Insights</h3>
                    <p>Comprehensive dashboards and reports to track performance, identify trends, and optimize customer experience.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <h3>Easy Integration</h3>
                    <p>Seamlessly integrate with your existing tools and platforms including CRM, helpdesk, and e-commerce systems.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3>Human Handoff</h3>
                    <p>Smart escalation to human agents when needed, with complete conversation context and customer history.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits" id="benefits">
        <div class="container">
            <div class="benefits-content">
                <div class="benefits-text">
                    <div class="section-header" style="text-align: left; margin-bottom: 2rem;">
                        <h2 class="section-title">Why Choose SupportAI?</h2>
                        <p class="section-subtitle" style="margin: 0;">Transform your customer support operations with measurable results</p>
                    </div>
                    <ul class="benefits-list">
                        <li class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="benefit-text">
                                <h4>Reduce Response Time by 95%</h4>
                                <p>Instant responses to customer inquiries eliminate wait times and improve satisfaction</p>
                            </div>
                        </li>
                        <li class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="benefit-text">
                                <h4>Cut Support Costs by 60%</h4>
                                <p>Automate routine inquiries and scale support without proportional staffing increases</p>
                            </div>
                        </li>
                        <li class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="benefit-text">
                                <h4>Boost Customer Satisfaction</h4>
                                <p>Consistent, accurate, and empathetic responses lead to happier customers</p>
                            </div>
                        </li>
                        <li class="benefit-item">
                            <div class="benefit-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="benefit-text">
                                <h4>Increase Team Productivity</h4>
                                <p>Free up human agents to focus on complex issues and strategic tasks</p>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="benefits-visual">
                    <div class="metrics-card">
                        <div class="metric">
                            <div class="metric-value">10M+</div>
                            <div class="metric-label">Messages Processed</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">98%</div>
                            <div class="metric-label">Accuracy Rate</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">4.9/5</div>
                            <div class="metric-label">Customer Rating</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Simple, Transparent Pricing</h2>
                <p class="section-subtitle">Choose the perfect plan for your business needs</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3 class="plan-name">Starter</h3>
                    <div class="plan-price">$29</div>
                    <div class="plan-period">per month</div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> 1,000 conversations/month</li>
                        <li><span class="check">✓</span> Email integration</li>
                        <li><span class="check">✓</span> Basic analytics</li>
                        <li><span class="check">✓</span> 5 languages</li>
<li><span class="check">✓</span> Email support</li>
                   </ul>
                   <a href="#" class="btn-primary btn-large">Get Started</a>
               </div>
               <div class="pricing-card popular">
                   <h3 class="plan-name">Professional</h3>
                   <div class="plan-price">$99</div>
                   <div class="plan-period">per month</div>
                   <ul class="plan-features">
                       <li><span class="check">✓</span> 10,000 conversations/month</li>
                       <li><span class="check">✓</span> Multi-channel integration</li>
                       <li><span class="check">✓</span> Advanced analytics</li>
                       <li><span class="check">✓</span> 25+ languages</li>
                       <li><span class="check">✓</span> Custom branding</li>
                       <li><span class="check">✓</span> Priority support</li>
                       <li><span class="check">✓</span> API access</li>
                   </ul>
                   <a href="#" class="btn-primary btn-large">Get Started</a>
               </div>
               <div class="pricing-card">
                   <h3 class="plan-name">Enterprise</h3>
                   <div class="plan-price">Custom</div>
                   <div class="plan-period">contact sales</div>
                   <ul class="plan-features">
                       <li><span class="check">✓</span> Unlimited conversations</li>
                       <li><span class="check">✓</span> Full platform integration</li>
                       <li><span class="check">✓</span> Custom AI training</li>
                       <li><span class="check">✓</span> 50+ languages</li>
                       <li><span class="check">✓</span> White-label solution</li>
                       <li><span class="check">✓</span> Dedicated support</li>
                       <li><span class="check">✓</span> SLA guarantee</li>
                   </ul>
                   <a href="#" class="btn-primary btn-large">Contact Sales</a>
               </div>
           </div>
       </div>
   </section>

   <!-- CTA Section -->
   <section class="cta">
       <div class="container">
           <h2>Ready to Transform Your Customer Support?</h2>
           <p>Join thousands of businesses already using SupportAI to deliver exceptional customer experiences</p>
           <a href="#" class="btn-white">Start Your Free Trial Today</a>
       </div>
   </section>

   <!-- Footer -->
   <footer id="contact">
       <div class="footer-content">
           <div class="footer-brand">
               <h3>SupportAI</h3>
               <p>Revolutionizing customer support with intelligent AI assistants that deliver instant, personalized, and effective solutions 24/7.</p>
           </div>
           <div class="footer-links">
               <h4>Product</h4>
               <ul>
                   <li><a href="#">Features</a></li>
                   <li><a href="#">Integrations</a></li>
                   <li><a href="#">API Documentation</a></li>
                   <li><a href="#">Security</a></li>
               </ul>
           </div>
           <div class="footer-links">
               <h4>Company</h4>
               <ul>
                   <li><a href="#">About Us</a></li>
                   <li><a href="#">Careers</a></li>
                   <li><a href="#">Press</a></li>
                   <li><a href="#">Contact</a></li>
               </ul>
           </div>
           <div class="footer-links">
               <h4>Support</h4>
               <ul>
                   <li><a href="#">Help Center</a></li>
                   <li><a href="#">Community</a></li>
                   <li><a href="#">Status</a></li>
                   <li><a href="#">Privacy Policy</a></li>
               </ul>
           </div>
       </div>
       <div class="footer-bottom">
           <p>&copy; 2025 SupportAI. All rights reserved.</p>
       </div>
   </footer>

   <script>
       // Mobile menu toggle
       const mobileMenu = document.querySelector('.mobile-menu');
       const navLinks = document.querySelector('.nav-links');
       
       mobileMenu.addEventListener('click', () => {
           navLinks.classList.toggle('active');
       });

       // Smooth scrolling for navigation links
       document.querySelectorAll('a[href^="#"]').forEach(anchor => {
           anchor.addEventListener('click', function (e) {
               e.preventDefault();
               const target = document.querySelector(this.getAttribute('href'));
               if (target) {
                   target.scrollIntoView({
                       behavior: 'smooth',
                       block: 'start'
                   });
               }
           });
       });

       // Header background on scroll
       window.addEventListener('scroll', () => {
           const header = document.querySelector('header');
           if (window.scrollY > 100) {
               header.style.background = 'rgba(255, 255, 255, 0.98)';
               header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
           } else {
               header.style.background = 'rgba(255, 255, 255, 0.95)';
               header.style.boxShadow = 'none';
           }
       });

       // Intersection Observer for animations
       const observerOptions = {
           threshold: 0.1,
           rootMargin: '0px 0px -50px 0px'
       };

       const observer = new IntersectionObserver((entries) => {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   entry.target.classList.add('fade-in-up');
               }
           });
       }, observerOptions);

       // Observe elements for animation
       document.querySelectorAll('.feature-card, .benefit-item, .pricing-card').forEach(el => {
           observer.observe(el);
       });

       // Chat demo animation
       const chatMessages = document.querySelectorAll('.chat-message');
       const typingIndicator = document.querySelector('.typing-indicator');
       
       function startChatDemo() {
           chatMessages.forEach((msg, index) => {
               setTimeout(() => {
                   msg.style.opacity = '1';
                   msg.style.transform = 'translateY(0)';
               }, (index + 1) * 1000);
           });
           
           // Show typing indicator after last message
           setTimeout(() => {
               typingIndicator.style.display = 'flex';
           }, chatMessages.length * 1000 + 500);
       }

       // Start chat demo when hero section is visible
       const heroObserver = new IntersectionObserver((entries) => {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   setTimeout(startChatDemo, 1000);
                   heroObserver.unobserve(entry.target);
               }
           });
       }, { threshold: 0.5 });

       heroObserver.observe(document.querySelector('.hero'));

       // Counter animation for stats
       function animateCounter(element, target, duration = 2000) {
           let start = 0;
           const increment = target / (duration / 16);
           
           function updateCounter() {
               start += increment;
               if (start < target) {
                   element.textContent = Math.floor(start) + (element.dataset.suffix || '');
                   requestAnimationFrame(updateCounter);
               } else {
                   element.textContent = target + (element.dataset.suffix || '');
               }
           }
           updateCounter();
       }

       // Animate counters when visible
       const statsObserver = new IntersectionObserver((entries) => {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   const statNumbers = entry.target.querySelectorAll('.stat-number');
                   statNumbers.forEach(stat => {
                       const target = parseInt(stat.textContent);
                       if (!isNaN(target)) {
                           stat.dataset.suffix = stat.textContent.replace(target.toString(), '');
                           animateCounter(stat, target);
                       }
                   });
                   
                   const metricValues = entry.target.querySelectorAll('.metric-value');
                   metricValues.forEach(metric => {
                       const text = metric.textContent;
                       const numbers = text.match(/\d+/g);
                       if (numbers && numbers.length > 0) {
                           const target = parseInt(numbers[0]);
                           metric.dataset.suffix = text.replace(target.toString(), '');
                           animateCounter(metric, target);
                       }
                   });
                   
                   statsObserver.unobserve(entry.target);
               }
           });
       }, { threshold: 0.5 });

       statsObserver.observe(document.querySelector('.hero-stats'));
       const metricsCard = document.querySelector('.metrics-card');
       if (metricsCard) {
           statsObserver.observe(metricsCard);
       }

       // Form validation and submission (if needed)
       const ctaButtons = document.querySelectorAll('.btn-primary, .btn-white');
       ctaButtons.forEach(button => {
           button.addEventListener('click', (e) => {
               if (button.textContent.includes('Trial') || button.textContent.includes('Started')) {
                   e.preventDefault();
                   // Here you would typically redirect to a signup form or modal
                   alert('Redirecting to signup form...');
               }
           });
       });

       // Pricing card hover effects
       const pricingCards = document.querySelectorAll('.pricing-card');
       pricingCards.forEach(card => {
           card.addEventListener('mouseenter', () => {
               if (!card.classList.contains('popular')) {
                   card.style.borderColor = 'var(--primary-color)';
               }
           });
           
           card.addEventListener('mouseleave', () => {
               if (!card.classList.contains('popular')) {
                   card.style.borderColor = 'var(--border-color)';
               }
           });
       });

       // Add loading state to buttons
       function addLoadingState(button) {
           const originalText = button.textContent;
           button.textContent = 'Loading...';
           button.style.pointerEvents = 'none';
           button.style.opacity = '0.7';
           
           setTimeout(() => {
               button.textContent = originalText;
               button.style.pointerEvents = 'auto';
               button.style.opacity = '1';
           }, 2000);
       }

       // Enhanced mobile menu
       function createMobileMenu() {
           const mobileMenuHTML = `
               <div class="mobile-nav-overlay" style="display: none;">
                   <div class="mobile-nav-content">
                       <div class="mobile-nav-header">
                           <span class="logo">SupportAI</span>
                           <span class="mobile-close">&times;</span>
                       </div>
                       <ul class="mobile-nav-links">
                           <li><a href="#features">Features</a></li>
                           <li><a href="#benefits">Benefits</a></li>
                           <li><a href="#pricing">Pricing</a></li>
                           <li><a href="#contact">Contact</a></li>
                       </ul>
                       <div class="mobile-nav-cta">
                           <a href="#" class="btn-secondary">Sign In</a>
                           <a href="#" class="btn-primary">Get Started</a>
                       </div>
                   </div>
               </div>
           `;
           
           document.body.insertAdjacentHTML('beforeend', mobileMenuHTML);
           
           // Add styles for mobile menu
           const mobileStyles = `
               .mobile-nav-overlay {
                   position: fixed;
                   top: 0;
                   left: 0;
                   right: 0;
                   bottom: 0;
                   background: rgba(0, 0, 0, 0.5);
                   z-index: 2000;
                   backdrop-filter: blur(5px);
               }
               
               .mobile-nav-content {
                   position: absolute;
                   top: 0;
                   right: 0;
                   height: 100%;
                   width: 300px;
                   background: white;
                   padding: 2rem;
                   transform: translateX(100%);
                   transition: transform 0.3s ease;
               }
               
               .mobile-nav-overlay.active .mobile-nav-content {
                   transform: translateX(0);
               }
               
               .mobile-nav-header {
                   display: flex;
                   justify-content: space-between;
                   align-items: center;
                   margin-bottom: 2rem;
                   padding-bottom: 1rem;
                   border-bottom: 1px solid var(--border-color);
               }
               
               .mobile-close {
                   font-size: 2rem;
                   cursor: pointer;
                   color: var(--text-dark);
               }
               
               .mobile-nav-links {
                   list-style: none;
                   margin-bottom: 2rem;
               }
               
               .mobile-nav-links li {
                   margin-bottom: 1rem;
               }
               
               .mobile-nav-links a {
                   text-decoration: none;
                   color: var(--text-dark);
                   font-size: 1.1rem;
                   font-weight: 500;
               }
               
               .mobile-nav-cta {
                   display: flex;
                   flex-direction: column;
                   gap: 1rem;
               }
               
               @media (max-width: 768px) {
                   .mobile-nav-content {
                       width: 100%;
                   }
               }
           `;
           
           const styleSheet = document.createElement('style');
           styleSheet.textContent = mobileStyles;
           document.head.appendChild(styleSheet);
           
           // Handle mobile menu interactions
           const overlay = document.querySelector('.mobile-nav-overlay');
           const mobileMenuBtn = document.querySelector('.mobile-menu');
           const closeBtn = document.querySelector('.mobile-close');
           
           mobileMenuBtn.addEventListener('click', () => {
               overlay.style.display = 'block';
               setTimeout(() => overlay.classList.add('active'), 10);
           });
           
           function closeMobileMenu() {
               overlay.classList.remove('active');
               setTimeout(() => overlay.style.display = 'none', 300);
           }
           
           closeBtn.addEventListener('click', closeMobileMenu);
           overlay.addEventListener('click', (e) => {
               if (e.target === overlay) closeMobileMenu();
           });
           
           // Close menu when clicking on links
           document.querySelectorAll('.mobile-nav-links a').forEach(link => {
               link.addEventListener('click', closeMobileMenu);
           });
       }
       
       // Initialize mobile menu
       createMobileMenu();
       
       // Add scroll-triggered animations
       const scrollElements = document.querySelectorAll('.section-title, .section-subtitle');
       
       const elementInView = (el, dividend = 1) => {
           const elementTop = el.getBoundingClientRect().top;
           return (
               elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend
           );
       };
       
       const displayScrollElement = (element) => {
           element.classList.add('fade-in-up');
       };
       
       const handleScrollAnimation = () => {
           scrollElements.forEach((el) => {
               if (elementInView(el, 1.25)) {
                   displayScrollElement(el);
               }
           });
       };
       
       window.addEventListener('scroll', handleScrollAnimation);
   </script>

   <style>
       /* Additional mobile menu styles */
       .mobile-nav-overlay {
           position: fixed;
           top: 0;
           left: 0;
           right: 0;
           bottom: 0;
           background: rgba(0, 0, 0, 0.5);
           z-index: 2000;
           backdrop-filter: blur(5px);
           display: none;
       }
       
       .mobile-nav-content {
           position: absolute;
           top: 0;
           right: 0;
           height: 100%;
           width: 300px;
           background: white;
           padding: 2rem;
           transform: translateX(100%);
           transition: transform 0.3s ease;
           box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
       }
       
       .mobile-nav-overlay.active .mobile-nav-content {
           transform: translateX(0);
       }
       
       .mobile-nav-header {
           display: flex;
           justify-content: space-between;
           align-items: center;
           margin-bottom: 2rem;
           padding-bottom: 1rem;
           border-bottom: 1px solid var(--border-color);
       }
       
       .mobile-close {
           font-size: 2rem;
           cursor: pointer;
           color: var(--text-dark);
           transition: color 0.3s ease;
       }
       
       .mobile-close:hover {
           color: var(--primary-color);
       }
       
       .mobile-nav-links {
           list-style: none;
           margin-bottom: 2rem;
       }
       
       .mobile-nav-links li {
           margin-bottom: 1rem;
       }
       
       .mobile-nav-links a {
           text-decoration: none;
           color: var(--text-dark);
           font-size: 1.1rem;
           font-weight: 500;
           transition: color 0.3s ease;
           display: block;
           padding: 0.5rem 0;
       }
       
       .mobile-nav-links a:hover {
           color: var(--primary-color);
       }
       
       .mobile-nav-cta {
           display: flex;
           flex-direction: column;
           gap: 1rem;
       }
       
       /* Enhanced responsive design */
       @media (max-width: 768px) {
           .mobile-nav-content {
               width: 100%;
           }
           
           .hero-stats {
               flex-wrap: wrap;
           }
           
           .hero-cta {
               flex-direction: column;
               align-items: stretch;
           }
           
           .hero-cta .btn-large {
               text-align: center;
           }
       }
       
       @media (max-width: 480px) {
           .mobile-nav-content {
               padding: 1.5rem;
           }
           
           .hero-text h1 {
               line-height: 1.2;
           }
           
           .features-grid {
               padding: 0 1rem;
           }
           
           .feature-card {
               padding: 1.5rem;
           }
           
           .benefit-item {
               padding: 1rem;
           }
           
           .pricing-card {
               padding: 1.5rem;
           }
       }
       
       /* Additional animations */
       .chat-message {
           transform: translateY(20px);
           opacity: 0;
       }
       
       .typing-indicator {
           display: none;
       }
       
       /* Accessibility improvements */
       .btn-primary:focus,
       .btn-secondary:focus,
       .btn-white:focus {
           outline: 2px solid var(--primary-color);
           outline-offset: 2px;
       }
       
       /* Print styles */
       @media print {
           .hero,
           .cta {
               background: white !important;
               color: black !important;
           }
           
           .btn-primary,
           .btn-secondary,
           .btn-white {
               border: 1px solid black !important;
               background: white !important;
               color: black !important;
           }
       }
       
       /* High contrast mode support */
       @media (prefers-contrast: high) {
           :root {
               --border-color: #000;
               --text-light: #000;
           }
       }
       
       /* Reduced motion support */
       @media (prefers-reduced-motion: reduce) {
           * {
               animation-duration: 0.01ms !important;
               animation-iteration-count: 1 !important;
               transition-duration: 0.01ms !important;
           }
       }
   </style>
</body>
</html>