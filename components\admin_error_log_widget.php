<?php
/**
 * Admin Error Log Widget Component
 * Displays recent error log entries in a collapsible widget
 */

// Ensure this is only included in admin pages
if (!function_exists('isAdmin') || !isAdmin()) {
    return;
}

// Read recent errors
$errorLogFile = __DIR__ . '/../admin/error_log.txt';
$recentErrors = [];

if (file_exists($errorLogFile)) {
    $logContent = file_get_contents($errorLogFile);
    $logLines = array_filter(explode("\n", $logContent));
    $recentErrors = array_reverse(array_slice($logLines, -5));
}

// Count total errors
$totalErrors = 0;
if (file_exists($errorLogFile)) {
    $totalErrors = count(array_filter(explode("\n", file_get_contents($errorLogFile))));
}
?>

<!-- Error Log Widget -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <button class="btn btn-link p-0 text-decoration-none text-warning fw-bold" 
                                type="button" data-bs-toggle="collapse" data-bs-target="#errorLogWidget" 
                                aria-expanded="false">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            System Error Log
                            <span class="badge bg-warning text-dark ms-2"><?php echo $totalErrors; ?></span>
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                    </h6>
                    <div class="d-flex gap-2">
                        <a href="dashboard.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-chart-line me-1"></i>Dashboard
                        </a>
                        <?php if ($totalErrors > 0): ?>
                        <button class="btn btn-sm btn-outline-danger" onclick="clearErrorLogWidget()">
                            <i class="fas fa-trash me-1"></i>Clear
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="collapse" id="errorLogWidget">
                <div class="card-body">
                    <?php if ($totalErrors > 5): ?>
                    <div class="alert alert-info alert-sm">
                        <i class="fas fa-info-circle me-2"></i>
                        Showing last 5 of <?php echo $totalErrors; ?> total errors. 
                        <a href="dashboard.php" class="alert-link">View full error log</a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead>
                                <tr>
                                    <th width="20%">Time</th>
                                    <th width="15%">Type</th>
                                    <th width="65%">Error Message</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recentErrors)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center text-success py-4">
                                            <i class="fas fa-check-circle me-2"></i>
                                            <strong>No recent errors found!</strong><br>
                                            <small class="text-muted">System is running smoothly</small>
                                        </td>
                                    </tr>
                                <?php else:
                                    foreach ($recentErrors as $error):
                                        if (preg_match('/\[(.*?)\] (.*)/', $error, $matches)):
                                            $datetime = $matches[1];
                                            $message = $matches[2];
                                            
                                            // Determine error type and styling
                                            $errorType = 'Error';
                                            $badgeClass = 'bg-secondary';
                                            $textClass = 'text-secondary';
                                            
                                            if (strpos($message, 'Fatal error') !== false) {
                                                $errorType = 'Fatal';
                                                $badgeClass = 'bg-danger';
                                                $textClass = 'text-danger';
                                                $message = str_replace('PHP Fatal error: ', '', $message);
                                            } elseif (strpos($message, 'Warning') !== false) {
                                                $errorType = 'Warning';
                                                $badgeClass = 'bg-warning text-dark';
                                                $textClass = 'text-warning';
                                                $message = str_replace('PHP Warning: ', '', $message);
                                            } elseif (strpos($message, 'Notice') !== false) {
                                                $errorType = 'Notice';
                                                $badgeClass = 'bg-info';
                                                $textClass = 'text-info';
                                                $message = str_replace('PHP Notice: ', '', $message);
                                            } elseif (strpos($message, 'Parse error') !== false) {
                                                $errorType = 'Parse';
                                                $badgeClass = 'bg-danger';
                                                $textClass = 'text-danger';
                                                $message = str_replace('PHP Parse error: ', '', $message);
                                            }
                                            
                                            // Clean up file paths in message
                                            $message = preg_replace('/in .*?([^\/\\\\]+\.php) on line (\d+)/', 'in $1:$2', $message);
                                ?>
                                    <tr>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('M d, H:i', strtotime($datetime)); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $badgeClass; ?> badge-sm">
                                                <?php echo $errorType; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small class="<?php echo $textClass; ?>">
                                                <?php echo htmlspecialchars(substr($message, 0, 120)); ?>
                                                <?php if (strlen($message) > 120): ?>
                                                    <span class="text-muted">...</span>
                                                <?php endif; ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php 
                                        endif;
                                    endforeach;
                                endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if (!empty($recentErrors)): ?>
                    <div class="mt-3 pt-3 border-top">
                        <div class="row">
                            <div class="col-md-8">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    <strong>Tip:</strong> Regular errors may indicate system issues that need attention.
                                    Fatal errors can prevent the application from working properly.
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <small class="text-muted">
                                    Last updated: <?php echo date('M d, Y H:i'); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function clearErrorLogWidget() {
    if (confirm('Are you sure you want to clear the error log? This action cannot be undone.')) {
        fetch('../admin/clear_error_log.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({action: 'clear_log'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload the page to show cleared log
                location.reload();
            } else {
                alert('Failed to clear error log: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to clear error log. Please try again.');
        });
    }
}

// Auto-collapse if no errors
document.addEventListener('DOMContentLoaded', function() {
    const errorCount = <?php echo $totalErrors; ?>;
    const errorWidget = document.getElementById('errorLogWidget');
    
    // If there are recent errors, show a subtle notification
    if (errorCount > 0) {
        const button = errorWidget.previousElementSibling.querySelector('button');
        button.classList.add('text-warning');
        
        // Auto-expand if there are fatal errors
        const hasFatalErrors = <?php echo (strpos(implode('', $recentErrors), 'Fatal error') !== false) ? 'true' : 'false'; ?>;
        if (hasFatalErrors) {
            const collapse = new bootstrap.Collapse(errorWidget, {show: true});
        }
    }
});
</script>

<style>
.badge-sm {
    font-size: 0.7em;
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.card-header .btn-link:hover {
    text-decoration: none !important;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.02);
}
</style>
