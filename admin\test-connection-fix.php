<?php
/**
 * Test script to verify the connection fix works
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Connection Fix Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Connection Fix Test</h2>";

// Test the processWithdrawalRequest function
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_function'])) {
    $requestId = intval($_POST['request_id']);
    
    echo "<div class='alert alert-info'>Testing processWithdrawalRequest function for ID: $requestId</div>";
    
    // Include the function from payouts.php
    include_once 'payouts.php';
    
    // Test the function
    $result = processWithdrawalRequest($requestId, 'approve', 'Test approval from connection fix test');
    
    if (strpos($result, 'successfully') !== false) {
        echo "<div class='alert alert-success'>✅ Function worked: $result</div>";
    } else {
        echo "<div class='alert alert-warning'>⚠️ Function result: $result</div>";
    }
}

// Test multiple connections
echo "<h5>Testing Multiple Database Connections:</h5>";

try {
    // Test connection 1
    $conn1 = getConnection();
    if ($conn1) {
        echo "<div class='alert alert-success'>✅ Connection 1: Success</div>";
        
        // Test query on connection 1
        $result1 = $conn1->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'pending'");
        if ($result1) {
            $count1 = $result1->fetch_assoc()['count'];
            echo "<div class='alert alert-info'>📊 Connection 1 found $count1 pending withdrawals</div>";
        }
        
        $conn1->close();
        echo "<div class='alert alert-secondary'>🔒 Connection 1 closed</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Connection 1: Failed</div>";
    }
    
    // Test connection 2 (should work independently)
    $conn2 = getConnection();
    if ($conn2) {
        echo "<div class='alert alert-success'>✅ Connection 2: Success (after closing connection 1)</div>";
        
        // Test query on connection 2
        $result2 = $conn2->query("SELECT COUNT(*) as count FROM users");
        if ($result2) {
            $count2 = $result2->fetch_assoc()['count'];
            echo "<div class='alert alert-info'>📊 Connection 2 found $count2 total users</div>";
        }
        
        $conn2->close();
        echo "<div class='alert alert-secondary'>🔒 Connection 2 closed</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Connection 2: Failed</div>";
    }
    
    // Test connection 3 (should still work)
    $conn3 = getConnection();
    if ($conn3) {
        echo "<div class='alert alert-success'>✅ Connection 3: Success (independent connections working)</div>";
        $conn3->close();
    } else {
        echo "<div class='alert alert-danger'>❌ Connection 3: Failed</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Connection test error: " . $e->getMessage() . "</div>";
}

// Show available pending withdrawals for testing
try {
    $conn = getConnection();
    $result = $conn->query("
        SELECT wr.id, wr.user_id, wr.amount, wr.status, wr.created_at, u.full_name 
        FROM withdrawal_requests wr 
        JOIN users u ON wr.user_id = u.id 
        WHERE wr.status = 'pending' 
        ORDER BY wr.created_at DESC 
        LIMIT 5
    ");
    
    if ($result && $result->num_rows > 0) {
        echo "<h5 class='mt-4'>Test Function with Pending Withdrawals:</h5>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-sm'>";
        echo "<thead><tr><th>ID</th><th>User</th><th>Amount</th><th>Date</th><th>Action</th></tr></thead>";
        echo "<tbody>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>#{$row['id']}</td>";
            echo "<td>{$row['full_name']}</td>";
            echo "<td>₦" . number_format($row['amount'], 2) . "</td>";
            echo "<td>" . date('M j, Y', strtotime($row['created_at'])) . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='request_id' value='{$row['id']}'>";
            echo "<button type='submit' name='test_function' class='btn btn-primary btn-sm'>Test Function</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info mt-4'>No pending withdrawal requests found for testing.</div>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Error fetching test data: " . $e->getMessage() . "</div>";
}

echo "                <div class='text-center mt-4'>
                        <a href='payouts.php' class='btn btn-success'>Test Real Payouts Page</a>
                        <a href='dashboard.php' class='btn btn-secondary'>Dashboard</a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>What This Test Checks:</h6>
                        <ul class='small'>
                            <li>✅ Multiple independent database connections</li>
                            <li>✅ Connection closing and reopening</li>
                            <li>✅ Function isolation (processWithdrawalRequest)</li>
                            <li>✅ No 'mysqli object is already closed' errors</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
