<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/points_system.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

$user_id = $_SESSION['user_id'];
$points_to_sell = filter_input(INPUT_POST, 'points', FILTER_VALIDATE_INT);

if (!$points_to_sell || $points_to_sell <= 0) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid number of points to sell.']);
    exit;
}

// --- Configuration ---
define('NAIRA_PER_POINT_SELL', 5);
$value = $points_to_sell * NAIRA_PER_POINT_SELL;

$conn = getConnection();

try {
    $conn->begin_transaction();

    // 1. Get current points balance and lock the row
    $stmt = $conn->prepare("SELECT total_points FROM user_points WHERE user_id = ? FOR UPDATE");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    $current_points = $result['total_points'] ?? 0;

    if ($current_points < $points_to_sell) {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'Insufficient points balance.']);
        exit;
    }

    // 2. Deduct points from user
    // We use a negative value with the existing awardPoints function for simplicity
    awardPoints($user_id, -$points_to_sell, 'point_sale', "Sold {$points_to_sell} points");

    // 3. Add value to wallet balance
    $update_wallet_stmt = $conn->prepare("UPDATE user_stats SET balance = balance + ? WHERE user_id = ?");
    $update_wallet_stmt->bind_param("di", $value, $user_id);
    $update_wallet_stmt->execute();

    // 4. Log the wallet transaction
    $trans_stmt = $conn->prepare("INSERT INTO transactions (user_id, amount, type, description) VALUES (?, ?, 'point_sale', ?)");
    $description = "Sale of {$points_to_sell} points";
    $trans_stmt->bind_param("ids", $user_id, $value, $description);
    $trans_stmt->execute();

    $conn->commit();

    // 5. Fetch updated balances
    $new_points_balance = $current_points - $points_to_sell;
    
    $balance_stmt = $conn->prepare("SELECT balance FROM user_stats WHERE user_id = ?");
    $balance_stmt->bind_param("i", $user_id);
    $balance_stmt->execute();
    $new_balance = $balance_stmt->get_result()->fetch_assoc()['balance'] ?? 0;

    echo json_encode([
        'success' => true, 
        'message' => 'Successfully sold ' . $points_to_sell . ' points!', 
        'data' => [
            'new_balance' => $new_balance,
            'new_points_balance' => $new_points_balance
        ]
    ]);

} catch (Exception $e) {
    $conn->rollback();
    logError("Sell Points API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request.']);
}

$conn->close();
?>