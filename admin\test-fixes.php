<?php
/**
 * Test script to verify admin fixes
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Admin Fixes Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='test-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-bug me-2'></i>Admin Fixes Test</h1>
                        <p class='text-muted'>Testing all admin page fixes and functionality</p>
                    </div>";

$tests = [];
$errors = [];

// Test 1: Database Connection
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-database me-2'></i>Test 1: Database Connection</h6>";

try {
    $conn = getConnection();
    if ($conn) {
        echo "<p class='mb-0'>✅ Database connection successful</p>";
        $tests['database'] = true;
    } else {
        echo "<p class='mb-0'>❌ Database connection failed</p>";
        $tests['database'] = false;
        $errors[] = "Database connection failed";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $tests['database'] = false;
    $errors[] = $e->getMessage();
}

echo "</div>";

// Test 2: Check Tables Exist
echo "<div class='test-result " . ($tests['database'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-table me-2'></i>Test 2: Database Tables</h6>";

if ($tests['database']) {
    $tables = ['users', 'screen_records', 'mirror_recordings', 'withdrawal_requests'];
    $tablesExist = 0;
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='mb-0'>✅ Table '$table' exists</p>";
            $tablesExist++;
        } else {
            echo "<p class='mb-0'>❌ Table '$table' missing</p>";
            $errors[] = "Table $table missing";
        }
    }
    
    $tests['tables'] = ($tablesExist == count($tables));
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database failed)</p>";
    $tests['tables'] = false;
}

echo "</div>";

// Test 3: Check Video Files
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-video me-2'></i>Test 3: Video Files Directory</h6>";

$recordingsDir = '../uploads/recordings/';
if (is_dir($recordingsDir)) {
    $files = scandir($recordingsDir);
    $videoFiles = array_filter($files, function($file) {
        return preg_match('/\.(mp4|webm|ogg|mov|avi)$/i', $file);
    });
    
    echo "<p class='mb-0'>✅ Directory exists: $recordingsDir</p>";
    echo "<p class='mb-0'>✅ Found " . count($videoFiles) . " video files</p>";
    
    if (!empty($videoFiles)) {
        echo "<p class='mb-0'>📁 Files: " . implode(', ', array_slice($videoFiles, 0, 3));
        if (count($videoFiles) > 3) echo " and " . (count($videoFiles) - 3) . " more...";
        echo "</p>";
    }
    
    $tests['videos'] = true;
} else {
    echo "<p class='mb-0'>❌ Directory not found: $recordingsDir</p>";
    $tests['videos'] = false;
    $errors[] = "Video directory not found";
}

echo "</div>";

// Test 4: Database Records vs Files
echo "<div class='test-result " . ($tests['database'] && $tests['tables'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-sync me-2'></i>Test 4: Database Records vs Files</h6>";

if ($tests['database'] && $tests['tables']) {
    try {
        // Get screen records
        $screenResult = $conn->query("SELECT COUNT(*) as count, COUNT(CASE WHEN file_path IS NOT NULL AND file_path != '' THEN 1 END) as with_files FROM screen_records");
        $screenStats = $screenResult->fetch_assoc();
        
        // Get mirror records  
        $mirrorResult = $conn->query("SELECT COUNT(*) as count, COUNT(CASE WHEN file_path IS NOT NULL AND file_path != '' THEN 1 END) as with_files FROM mirror_recordings");
        $mirrorStats = $mirrorResult->fetch_assoc();
        
        echo "<p class='mb-0'>✅ Screen records: {$screenStats['count']} total, {$screenStats['with_files']} with file paths</p>";
        echo "<p class='mb-0'>✅ Mirror records: {$mirrorStats['count']} total, {$mirrorStats['with_files']} with file paths</p>";
        
        $tests['records'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Query error: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['records'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database/tables failed)</p>";
    $tests['records'] = false;
}

echo "</div>";

// Test 5: Page Syntax Check
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-code me-2'></i>Test 5: PHP Syntax Check</h6>";

$pages = ['users.php', 'payouts.php', 'videos.php'];
$syntaxOk = 0;

foreach ($pages as $page) {
    $output = [];
    $returnVar = 0;
    exec("php -l $page 2>&1", $output, $returnVar);
    
    if ($returnVar === 0) {
        echo "<p class='mb-0'>✅ $page - Syntax OK</p>";
        $syntaxOk++;
    } else {
        echo "<p class='mb-0'>❌ $page - Syntax Error: " . implode(' ', $output) . "</p>";
        $errors[] = "$page syntax error";
    }
}

$tests['syntax'] = ($syntaxOk == count($pages));

echo "</div>";

// Close database connection
if (isset($conn) && $conn) {
    $conn->close();
}

// Summary
$totalTests = count($tests);
$passedTests = count(array_filter($tests));

if (empty($errors)) {
    echo "<div class='alert alert-success'>
            <h5><i class='fas fa-check-circle me-2'></i>All Tests Passed! ($passedTests/$totalTests)</h5>
            <p class='mb-3'>All admin fixes are working correctly.</p>
            <div class='text-center'>
                <a href='users.php' class='btn btn-success btn-lg me-2'>
                    <i class='fas fa-users me-2'></i>Test Users Page
                </a>
                <a href='payouts.php' class='btn btn-warning btn-lg me-2'>
                    <i class='fas fa-money-bill-wave me-2'></i>Test Payouts Page
                </a>
                <a href='videos.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-video me-2'></i>Test Videos Page
                </a>
            </div>
          </div>";
} else {
    echo "<div class='alert alert-warning'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Some Issues Found ($passedTests/$totalTests tests passed)</h5>
            <h6>Issues:</h6>
            <ul class='mb-3'>";
    
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    
    echo "            </ul>
            <div class='text-center'>
                <a href='dashboard.php' class='btn btn-secondary btn-lg'>
                    <i class='fas fa-tachometer-alt me-2'></i>Back to Dashboard
                </a>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            Delete this test file after confirming everything works.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
