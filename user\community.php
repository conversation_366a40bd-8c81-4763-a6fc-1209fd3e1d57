<?php
$page_title = 'Community Hub';
require_once __DIR__ . '/../components/user_header.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$posts = [];

try {
    $conn = getConnection();
    
    // Fetch community posts (you might want to add pagination)
    $stmt = $conn->prepare("
        SELECT p.id, p.content, p.created_at, u.full_name as username, u.profile_picture as profile_pic
        FROM posts p
        JOIN users u ON p.user_id = u.id
        WHERE u.is_active = 1
        ORDER BY p.created_at DESC
        LIMIT 20
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    $posts = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();

    // Handle new post submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['post_content'])) {
        $content = trim($_POST['post_content']);
        if (!empty($content)) {
            $stmt = $conn->prepare("INSERT INTO posts (user_id, content, created_at) VALUES (?, ?, NOW())");
            $stmt->bind_param("is", $user_id, $content);
            if ($stmt->execute()) {
                $stmt->close();
                $conn->close();
                header("Location: community.php"); // Refresh to see the new post
                exit();
            }
            $stmt->close();
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    error_log("Community error: " . $e->getMessage());
}
?>

<div class="row">
    <!-- Left Column: New Post & Feed -->
    <div class="col-lg-8">
        <!-- Create Post Card -->
        <div class="card mb-4">
            <div class="card-body">
                <form action="community.php" method="POST">
                    <div class="d-flex align-items-start">
                        <?php if (!empty($user['profile_picture'])): ?>
                            <img src="../uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="My Avatar" class="rounded-circle me-3" width="48" height="48">
                        <?php else: ?>
                            <img src="../assets/images/default-avatar.png" alt="My Avatar" class="rounded-circle me-3" width="48" height="48">
                        <?php endif; ?>
                        <textarea name="post_content" class="form-control" rows="3" placeholder="Share your thoughts or ask a question..." required></textarea>
                    </div>
                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Post
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Community Feed -->
        <?php if (empty($posts)): ?>
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-comments fa-3x text-secondary mb-3"></i>
                    <h4>The Community is Quiet</h4>
                    <p class="text-secondary">Be the first to start a conversation!</p>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($posts as $post): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex">
                            <?php if (!empty($post['profile_pic'])): ?>
                                <img src="../uploads/profiles/<?php echo htmlspecialchars($post['profile_pic']); ?>" alt="<?php echo htmlspecialchars($post['username'] ?? 'User'); ?>" class="rounded-circle me-3" width="48" height="48">
                            <?php else: ?>
                                <img src="../assets/images/default-avatar.png" alt="<?php echo htmlspecialchars($post['username'] ?? 'User'); ?>" class="rounded-circle me-3" width="48" height="48">
                            <?php endif; ?>
                            <div>
                                <h5 class="mb-0"><?php echo htmlspecialchars($post['username'] ?? 'User'); ?></h5>
                                <small class="text-secondary"><?php echo timeAgo($post['created_at']); ?></small>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-link text-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">Report Post</a></li>
                                <?php if (($post['username'] ?? '') == ($user['full_name'] ?? '')): // Basic ownership check ?>
                                <li><a class="dropdown-item" href="#">Delete Post</a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    <p class="mt-3"><?php echo nl2br(htmlspecialchars($post['content'] ?? '')); ?></p>
                    <hr>
                    <div class="d-flex justify-content-around text-secondary">
                        <a href="#" class="text-decoration-none text-secondary"><i class="far fa-thumbs-up me-2"></i>Like</a>
                        <a href="#" class="text-decoration-none text-secondary"><i class="far fa-comment me-2"></i>Comment</a>
                        <a href="#" class="text-decoration-none text-secondary"><i class="far fa-share-square me-2"></i>Share</a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Right Column: Leaderboard & Links -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="card-title">
                    <i class="fas fa-trophy me-2"></i>Top Reciters
                </h4>
            </div>
            <div class="card-body">
                <!-- Placeholder for leaderboard -->
                <p class="text-secondary">Leaderboard coming soon!</p>
            </div>
        </div>
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">
                    <i class="fas fa-link me-2"></i>Quick Links
                </h4>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="rankings.php" class="btn btn-outline-primary">
                        <i class="fas fa-trophy me-2"></i>Rankings
                    </a>
                    <a href="../streams.php" class="btn btn-outline-primary">
                        <i class="fas fa-stream me-2"></i>Streams
                    </a>
                    <a href="wallet.php" class="btn btn-outline-primary">
                        <i class="fas fa-wallet me-2"></i>Wallet
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --primary-red: #B10020;
    --primary-red-light: #D32F2F;
    --primary-red-dark: #8B0000;
    --white: #FFFFFF;
    --dark-text: #1E1E1E;
    --subtle-gray: #F3F3F3;
    --light-gray: #E8E8E8;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(177, 0, 32, 0.1);
    background: var(--white);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(177, 0, 32, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    color: white;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 1rem 1.5rem;
}

.card-title {
    margin: 0;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    border: none;
    color: white;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-red-dark), var(--primary-red));
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(177, 0, 32, 0.3);
}

.btn-outline-primary {
    color: var(--primary-red);
    border: 2px solid var(--primary-red);
    background: transparent;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: white;
    transform: translateY(-2px);
}

.form-control {
    border-radius: 12px;
    border: 2px solid var(--light-gray);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(177, 0, 32, 0.25);
}

.text-secondary {
    color: var(--dark-text) !important;
}

.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(177, 0, 32, 0.1);
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    color: white;
}

.text-decoration-none {
    transition: all 0.3s ease;
    color: var(--primary-red) !important;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.text-decoration-none:hover {
    background: var(--subtle-gray);
    color: var(--primary-red-dark) !important;
}

hr {
    border-color: var(--light-gray);
    margin: 1rem 0;
}
</style>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>
