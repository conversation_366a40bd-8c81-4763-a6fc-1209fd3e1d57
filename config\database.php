<?php
/**
 * Database Configuration for RECITE App
 * Auto-detects local vs live server and configures accordingly
 */

// Include server-specific configuration
require_once __DIR__ . '/server_config.php';

// Session Configuration (must be set before session_start)
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_lifetime', 86400); // 24 hours
    ini_set('session.gc_maxlifetime', 86400);
}

// getConnection() function is now defined in server_config.php

// executeQuery() function is now defined in server_config.php

// sanitize() function is now defined in server_config.php

/**
 * Generate CSRF token
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 * @param string $token Token to validate
 * @return bool True if valid
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check if user is logged in
 * @return bool True if logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// requireLogin() function is now defined in server_config.php

// getUserById() function is now defined in server_config.php

/**
 * Update user wallet balance
 * @param int $userId User ID
 * @param float $amount Amount to add/subtract
 * @param string $description Transaction description
 * @param string $type Transaction type
 * @return bool Success status
 */
function updateWallet($userId, $amount, $description, $type = 'payment') {
    try {
        $conn = getConnection();
        $conn->autocommit(false);
        
        // Update wallet balance
        executeQuery(
            "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
            'di',
            [$amount, $userId]
        );
        
        // Log transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, amount, description, status) VALUES (?, ?, ?, ?, 'completed')",
            'isds',
            [$userId, $type, $amount, $description]
        );
        
        $conn->commit();
        $conn->autocommit(true);
        
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        error_log("Wallet update error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update user points balance
 * @param int $userId User ID
 * @param int $points Points to add/subtract
 * @param string $description Transaction description
 * @return bool Success status
 */
function updatePoints($userId, $points, $description) {
    try {
        $conn = getConnection();
        $conn->autocommit(false);
        
        // Update points balance
        executeQuery(
            "UPDATE users SET points_balance = points_balance + ? WHERE id = ?",
            'ii',
            [$points, $userId]
        );
        
        // Log transaction
        executeQuery(
            "INSERT INTO transactions (user_id, transaction_type, points, description, status) VALUES (?, 'bonus', ?, ?, 'completed')",
            'iis',
            [$userId, $points, $description]
        );
        
        $conn->commit();
        $conn->autocommit(true);
        
        return true;
    } catch (Exception $e) {
        $conn->rollback();
        $conn->autocommit(true);
        error_log("Points update error: " . $e->getMessage());
        return false;
    }
}

/**
 * Generate unique referral code
 * @param int $userId User ID
 * @return string Referral code
 */
function generateReferralCode($userId) {
    return 'REC' . str_pad($userId, 6, '0', STR_PAD_LEFT) . strtoupper(substr(md5($userId . time()), 0, 4));
}

/**
 * Log admin action
 * @param string $username Admin username
 * @param string $action Action performed
 * @param int $targetUserId Target user ID (optional)
 * @param string $details Additional details
 */
function logAdminAction($username, $action, $targetUserId = null, $details = '') {
    try {
        executeQuery(
            "INSERT INTO admin_logs (admin_username, action, target_user_id, details, ip_address) VALUES (?, ?, ?, ?, ?)",
            'ssiss',
            [$username, $action, $targetUserId, $details, $_SERVER['REMOTE_ADDR']]
        );
    } catch (Exception $e) {
        error_log("Admin log error: " . $e->getMessage());
    }
}

/**
 * Format time ago
 * @param string $datetime DateTime string
 * @return string Formatted time ago
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    
    return date('M j, Y', strtotime($datetime));
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}