<?php
/**
 * Update Point Transactions Enum
 */

require_once 'config/db_config.php';

echo "<h1>Updating Point Transactions Enum</h1>";

try {
    $conn = getConnection();
    
    // Update the transaction_type enum to include purchase and sale
    $sql = "ALTER TABLE point_transactions MODIFY COLUMN transaction_type ENUM(
        'recitation_complete',
        'streak_bonus', 
        'referral',
        'like_received',
        'comment_received',
        'share_received',
        'leaderboard_bonus',
        'admin_adjustment',
        'purchase',
        'sale'
    ) NOT NULL";
    
    if ($conn->query($sql)) {
        echo "✅ point_transactions enum updated successfully<br>";
    } else {
        echo "❌ Error updating enum: " . $conn->error . "<br>";
    }
    
    echo "<br>🎉 Point transactions enum update completed!<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?> 