<?php
/**
 * Production Setup Helper
 * Use this file to configure your production database settings
 */

// IMPORTANT: Update these values for your production server
$production_config = [
    'db_host' => 'localhost',                              // Your database host
    'db_name' => 'your_database_name',                    // Your database name
    'db_user' => 'your_db_username',                      // Your database username
    'db_pass' => 'your_db_password',                      // Your database password
    'base_url' => 'https://universalreciters.name.ng/',  // Your website URL
    'site_url' => 'https://universalreciters.name.ng/',  // Your website URL
];

// Function to update server_config.php with production values
function updateProductionConfig($config) {
    $configFile = __DIR__ . '/server_config.php';
    $content = file_get_contents($configFile);
    
    // Replace placeholder values
    $content = str_replace("define('DB_HOST', 'localhost'); // Your server's DB host", 
                          "define('DB_HOST', '{$config['db_host']}');", $content);
    $content = str_replace("define('DB_NAME', 'your_database_name'); // Your actual database name", 
                          "define('DB_NAME', '{$config['db_name']}');", $content);
    $content = str_replace("define('DB_USER', 'your_db_username');   // Your database username", 
                          "define('DB_USER', '{$config['db_user']}');", $content);
    $content = str_replace("define('DB_PASS', 'your_db_password');   // Your database password", 
                          "define('DB_PASS', '{$config['db_pass']}');", $content);
    $content = str_replace("define('BASE_URL', 'https://yourdomain.com/');", 
                          "define('BASE_URL', '{$config['base_url']}');", $content);
    $content = str_replace("define('SITE_URL', 'https://yourdomain.com/');", 
                          "define('SITE_URL', '{$config['site_url']}');", $content);
    
    return file_put_contents($configFile, $content);
}

// Function to test database connection
function testDatabaseConnection($config) {
    try {
        $conn = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name']);
        
        if ($conn->connect_error) {
            return ['success' => false, 'error' => $conn->connect_error];
        }
        
        $conn->close();
        return ['success' => true, 'message' => 'Database connection successful'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Function to create necessary database tables
function createDatabaseTables($config) {
    try {
        $conn = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name']);
        
        if ($conn->connect_error) {
            throw new Exception('Connection failed: ' . $conn->connect_error);
        }
        
        // Create admins table
        $adminTable = "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        
        if (!$conn->query($adminTable)) {
            throw new Exception('Failed to create admins table: ' . $conn->error);
        }
        
        // Create admin_logs table if it doesn't exist
        $adminLogsTable = "
            CREATE TABLE IF NOT EXISTS admin_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_username VARCHAR(50) NOT NULL,
                action VARCHAR(100) NOT NULL,
                target_user_id INT NULL,
                details TEXT NULL,
                ip_address VARCHAR(45) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ";
        
        if (!$conn->query($adminLogsTable)) {
            throw new Exception('Failed to create admin_logs table: ' . $conn->error);
        }
        
        $conn->close();
        return ['success' => true, 'message' => 'Database tables created successfully'];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// If this file is accessed directly, show setup interface
if (basename($_SERVER['PHP_SELF']) === 'production_setup.php') {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Production Setup - RECITE App</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">RECITE App - Production Setup</h4>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <strong>Important:</strong> This setup should only be run once during initial deployment.
                                Delete this file after setup is complete for security.
                            </div>
                            
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="db_host" class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" 
                                           value="<?php echo $production_config['db_host']; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="db_name" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" 
                                           value="<?php echo $production_config['db_name']; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="db_user" class="form-label">Database Username</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" 
                                           value="<?php echo $production_config['db_user']; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="db_pass" class="form-label">Database Password</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="base_url" class="form-label">Website URL</label>
                                    <input type="url" class="form-control" id="base_url" name="base_url" 
                                           value="<?php echo $production_config['base_url']; ?>" required>
                                </div>
                                
                                <button type="submit" name="setup" class="btn btn-primary">Setup Production Environment</button>
                            </form>
                            
                            <?php
                            if (isset($_POST['setup'])) {
                                $config = [
                                    'db_host' => $_POST['db_host'],
                                    'db_name' => $_POST['db_name'],
                                    'db_user' => $_POST['db_user'],
                                    'db_pass' => $_POST['db_pass'],
                                    'base_url' => $_POST['base_url'],
                                    'site_url' => $_POST['base_url'],
                                ];
                                
                                echo '<div class="mt-4">';
                                
                                // Test database connection
                                $dbTest = testDatabaseConnection($config);
                                if ($dbTest['success']) {
                                    echo '<div class="alert alert-success">✓ Database connection successful</div>';
                                    
                                    // Create tables
                                    $tableResult = createDatabaseTables($config);
                                    if ($tableResult['success']) {
                                        echo '<div class="alert alert-success">✓ Database tables created</div>';
                                        
                                        // Update config file
                                        if (updateProductionConfig($config)) {
                                            echo '<div class="alert alert-success">✓ Configuration updated successfully</div>';
                                            echo '<div class="alert alert-info"><strong>Setup Complete!</strong> You can now access the admin panel at <a href="../admin/login.php">admin/login.php</a></div>';
                                        } else {
                                            echo '<div class="alert alert-danger">✗ Failed to update configuration file</div>';
                                        }
                                    } else {
                                        echo '<div class="alert alert-danger">✗ ' . $tableResult['error'] . '</div>';
                                    }
                                } else {
                                    echo '<div class="alert alert-danger">✗ Database connection failed: ' . $dbTest['error'] . '</div>';
                                }
                                
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
