<?php
require_once 'config/db_config.php';

// Test script to verify withdrawal status updates
header('Content-Type: text/plain');

echo "Testing withdrawal status update...\n\n";

try {
    $conn = getConnection();
    
    // First, let's see current pending withdrawals
    echo "Current pending withdrawals:\n";
    $result = $conn->query("SELECT id, user_id, amount, status FROM withdrawal_requests WHERE status = 'pending' LIMIT 5");
    
    if ($result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            echo "ID: {$row['id']}, Amount: ₦{$row['amount']}, Status: {$row['status']}\n";
        }
    } else {
        echo "No pending withdrawals found.\n";
    }
    
    echo "\nTesting status update...\n";
    
    // Test updating a specific withdrawal (you can change this ID)
    $testId = 1; // Change this to a valid ID from your database
    
    $stmt = $conn->prepare("UPDATE withdrawal_requests SET status = 'completed', processed_at = NOW() WHERE id = ? AND status = 'pending'");
    $stmt->bind_param("i", $testId);
    
    if ($stmt->execute()) {
        $affected = $stmt->affected_rows;
        echo "Update executed. Rows affected: $affected\n";
        
        if ($affected > 0) {
            echo "Status updated successfully!\n";
            
            // Verify the update
            $verify = $conn->prepare("SELECT id, status FROM withdrawal_requests WHERE id = ?");
            $verify->bind_param("i", $testId);
            $verify->execute();
            $verifyResult = $verify->get_result();
            $updated = $verifyResult->fetch_assoc();
            
            echo "New status: {$updated['status']}\n";
        } else {
            echo "No rows updated - withdrawal may not exist or already processed.\n";
        }
    } else {
        echo "Update failed: " . $stmt->error . "\n";
    }
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
?> 