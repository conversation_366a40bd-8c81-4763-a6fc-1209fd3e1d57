/* Modern Mobile App Design System */
:root {
    /* Colors */
    --primary: #15803d;
    --primary-light: #16a34a;
    --primary-dark: #166534;
    --secondary: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Bottom Navigation */
    --bottom-nav-height: 80px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 100%;
    padding: 0 var(--spacing-md);
    margin: 0 auto;
}

@media (min-width: 640px) {
    .container {
        max-width: 640px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}

/* Header */
.app-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
    margin-bottom: var(--spacing-lg);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* Profile Section */
.profile-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: var(--white);
    padding: var(--spacing-2xl) var(--spacing-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid var(--white);
    margin: 0 auto var(--spacing-lg);
    position: relative;
    z-index: 1;
    background: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-3xl);
    color: var(--gray-600);
    font-weight: 600;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.profile-name {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    position: relative;
    z-index: 1;
}

.profile-email {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.2;
    word-break: break-word;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    line-height: 1.2;
    word-break: break-word;
}

/* Action Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-height: 44px;
    min-width: 80px;
    text-align: center;
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
}

.btn-danger {
    background: var(--danger);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    min-height: 36px;
    min-width: 70px;
}

.btn-lg {
    padding: 1rem 1.5rem;
    font-size: 0.95rem;
    min-height: 52px;
    min-width: 100px;
}

.btn-full {
    width: 100%;
    min-width: auto;
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: var(--white);
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(21, 128, 61, 0.1);
}

.form-control:disabled {
    background: var(--gray-100);
    color: var(--gray-500);
    cursor: not-allowed;
}

/* Lists */
.list {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.list-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    transition: background-color 0.2s ease;
    min-height: 72px;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:hover {
    background: var(--gray-50);
}

.list-item-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    background: var(--primary);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
    font-size: 1rem;
}

.list-item-content {
    flex: 1;
    min-width: 0;
}

.list-item-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-item-subtitle {
    font-size: 0.8rem;
    color: var(--gray-600);
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.list-item-action {
    color: var(--gray-400);
    transition: color 0.2s ease;
    flex-shrink: 0;
}

.list-item:hover .list-item-action {
    color: var(--primary);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    min-height: 24px;
    min-width: 60px;
    white-space: nowrap;
    text-align: center;
}

.badge-primary {
    background: var(--primary);
    color: var(--white);
}

.badge-success {
    background: var(--success);
    color: var(--white);
}

.badge-warning {
    background: var(--warning);
    color: var(--white);
}

.badge-danger {
    background: var(--danger);
    color: var(--white);
}

.badge-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* Alerts */
.alert {
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.alert-success {
    background: #ecfdf5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-danger {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-warning {
    background: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
}

.alert-info {
    background: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-sm) 0;
    z-index: 100;
    display: flex;
    justify-content: space-around;
    align-items: center;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
}

.bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--gray-500);
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
    min-width: 60px;
    position: relative;
}

.bottom-nav-item:hover {
    color: var(--primary);
    background: rgba(21, 128, 61, 0.05);
}

.bottom-nav-item.active {
    color: var(--primary);
    background: rgba(21, 128, 61, 0.1);
}

.bottom-nav-item i {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.bottom-nav-item span {
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-align: center;
}

/* Main Content */
.main-content {
    padding-bottom: calc(var(--bottom-nav-height) + var(--spacing-lg));
    min-height: 100vh;
}

/* Responsive Design */
@media (max-width: 640px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .profile-header {
        padding: var(--spacing-xl) var(--spacing-md);
    }
    
    .profile-avatar {
        width: 80px;
        height: 80px;
        font-size: var(--font-size-2xl);
    }
    
    .profile-name {
        font-size: var(--font-size-xl);
    }
    
    .bottom-nav-item {
        min-width: 50px;
        padding: var(--spacing-xs);
    }
    
    .bottom-nav-item i {
        font-size: var(--font-size-base);
    }
    
    .bottom-nav-item span {
        font-size: 0.6rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* Additional utility classes */
.text-muted {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.text-right {
    text-align: right;
}

.d-flex {
    display: flex;
}

.gap-2 {
    gap: var(--spacing-sm);
}

/* Form specific styles */
.form-control[type="file"] {
    padding: 0.5rem;
    border: 2px dashed var(--gray-300);
    background: var(--gray-50);
    cursor: pointer;
}

.form-control[type="file"]:hover {
    border-color: var(--primary);
    background: rgba(21, 128, 61, 0.05);
}

/* Profile avatar in form */
.profile-avatar {
    flex-shrink: 0;
}

/* Button group styles */
.btn + .btn {
    margin-left: var(--spacing-sm);
}

/* Responsive form adjustments */
@media (max-width: 640px) {
    .d-flex.gap-2 {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .btn + .btn {
        margin-left: 0;
    }
} 