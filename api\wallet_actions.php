<?php
require_once __DIR__ . '/../config.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? '';

if ($action === 'withdraw') {
    handle_withdrawal($user_id, $_POST);
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid action.']);
}

function handle_withdrawal($user_id, $post_data) {
    $amount = filter_var($post_data['amount'], FILTER_VALIDATE_FLOAT);

    if (!$amount || $amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid withdrawal amount.']);
        exit;
    }

    $conn = getConnection();

    try {
        $conn->begin_transaction();

        // 1. Get current balance and lock the row
        $stmt = $conn->prepare("SELECT balance FROM user_stats WHERE user_id = ? FOR UPDATE");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $current_balance = $stmt->get_result()->fetch_assoc()['balance'] ?? 0;

        if ($amount > $current_balance) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => 'Insufficient balance.']);
            exit;
        }

        // 2. Deduct from balance
        $new_balance = $current_balance - $amount;
        $update_stmt = $conn->prepare("UPDATE user_stats SET balance = ? WHERE user_id = ?");
        $update_stmt->bind_param("di", $new_balance, $user_id);
        $update_stmt->execute();

        // 3. Create withdrawal record
        $withdrawal_stmt = $conn->prepare("INSERT INTO withdrawals (user_id, amount, status) VALUES (?, ?, 'pending')");
        $withdrawal_stmt->bind_param("id", $user_id, $amount);
        $withdrawal_stmt->execute();

        // 4. Log transaction
        $trans_stmt = $conn->prepare("INSERT INTO transactions (user_id, amount, type, description) VALUES (?, ?, 'withdrawal', ?)");
        $negative_amount = -$amount;
        $description = "Withdrawal request of ₦" . number_format($amount, 2);
        $trans_stmt->bind_param("ids", $user_id, $negative_amount, $description);
        $trans_stmt->execute();

        $conn->commit();

        echo json_encode([
            'success' => true, 
            'message' => 'Withdrawal request submitted successfully!', 
            'data' => [
                'new_balance' => $new_balance
            ]
        ]);

    } catch (Exception $e) {
        $conn->rollback();
        logError("Withdrawal API Error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request.']);
    }

    $conn->close();
}
?>