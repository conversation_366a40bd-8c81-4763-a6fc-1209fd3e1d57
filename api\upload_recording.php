<?php
/**
 * Upload Recording API for Recite! App
 * Handles camera and screen recording uploads
 */

require_once '../config/db_config.php';

header('Content-Type: application/json');

// Check authentication
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$userId = $_SESSION['user_id'];

// Check if recording file was uploaded
if (!isset($_FILES['recording']) || $_FILES['recording']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['success' => false, 'message' => 'No recording file uploaded']);
    exit;
}

$uploadFile = $_FILES['recording'];
$recordingType = $_POST['type'] ?? 'selfie';
$title = $_POST['title'] ?? 'Recording - ' . date('Y-m-d H:i:s');

// Validate file type
$allowedTypes = ['video/webm', 'video/mp4', 'video/avi'];
$fileType = $uploadFile['type'];

if (!in_array($fileType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only video files are allowed.']);
    exit;
}

// Check file size (max 100MB)
$maxSize = 100 * 1024 * 1024; // 100MB
if ($uploadFile['size'] > $maxSize) {
    echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 100MB.']);
    exit;
}

// Create upload directory if it doesn't exist
$uploadDir = '../uploads/screen_records/';
if (!is_dir($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

// Generate unique filename
$fileExtension = pathinfo($uploadFile['name'], PATHINFO_EXTENSION);
$fileName = $recordingType . '_' . $userId . '_' . time() . '.' . $fileExtension;
$filePath = $uploadDir . $fileName;

// Move uploaded file
if (!move_uploaded_file($uploadFile['tmp_name'], $filePath)) {
    echo json_encode(['success' => false, 'message' => 'Failed to save recording file']);
    exit;
}

try {
    // Save to database
    $relativePath = 'uploads/screen_records/' . $fileName;

    // Determine which table to use based on recording type
    if ($recordingType === 'selfie') {
        // Insert into mirror_recordings table for selfie recordings
        $result = executeQuery(
            "INSERT INTO mirror_recordings (user_id, title, file_path, file_size, is_public, created_at)
             VALUES (?, ?, ?, ?, 1, NOW())",
            'isis',
            [$userId, $title, $relativePath, $uploadFile['size']]
        );
    } else {
        // Insert into screen_records table for screen recordings
        $result = executeQuery(
            "INSERT INTO screen_records (user_id, title, file_path, file_size, is_public, created_at)
             VALUES (?, ?, ?, ?, 1, NOW())",
            'isis',
            [$userId, $title, $relativePath, $uploadFile['size']]
        );
    }

    if ($result) {
        $recordingId = getConnection()->insert_id;

        // Also create a stream entry
        executeQuery(
            "INSERT INTO streams (user_id, content_type, text_content, media_path, created_at)
             VALUES (?, 'video', ?, ?, NOW())",
            'iss',
            [$userId, $title, $relativePath]
        );

        echo json_encode([
            'success' => true,
            'message' => 'Recording uploaded successfully',
            'recording_id' => $recordingId,
            'file_path' => $relativePath,
            'type' => $recordingType
        ]);
    } else {
        // Delete the uploaded file if database insert failed
        unlink($filePath);
        echo json_encode(['success' => false, 'message' => 'Failed to save recording to database']);
    }

} catch (Exception $e) {
    // Delete the uploaded file if there was an error
    if (file_exists($filePath)) {
        unlink($filePath);
    }
    
    error_log("Recording upload error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
}
?>
