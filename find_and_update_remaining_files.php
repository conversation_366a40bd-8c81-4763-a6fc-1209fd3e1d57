<?php
/**
 * Find and Update Remaining Database References
 * Scans all PHP files and updates them to use centralized config
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Find & Update Remaining Files - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .update-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .file-list { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 1rem; border-radius: 5px; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='update-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-search me-2'></i>Find & Update Remaining Files</h1>
                        <p class='text-muted'>Scanning for files that still use old database configuration</p>
                    </div>";

$updatedFiles = [];
$scannedFiles = [];

// Function to scan directory recursively
function scanDirectory($dir, $pattern) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $content = file_get_contents($file->getPathname());
            if (preg_match($pattern, $content)) {
                $files[] = $file->getPathname();
            }
        }
    }
    
    return $files;
}

// Patterns to search for
$patterns = [
    "/require_once ['\"]config\/database\.php['\"]/",
    "/require_once ['\"]\.\.\/config\.php['\"]/",
    "/require_once ['\"]config\/server_config\.php['\"]/",
    "/require_once __DIR__ \. ['\"]\/\.\.\/config\/database\.php['\"]/",
];

echo "<div class='alert alert-info'>
        <h5><i class='fas fa-info-circle me-2'></i>Scanning Files</h5>
        <p class='mb-0'>Looking for PHP files that still use old database configuration...</p>
      </div>";

$allFilesToUpdate = [];

foreach ($patterns as $pattern) {
    $files = scanDirectory('.', $pattern);
    $allFilesToUpdate = array_merge($allFilesToUpdate, $files);
}

// Remove duplicates and normalize paths
$allFilesToUpdate = array_unique($allFilesToUpdate);
$allFilesToUpdate = array_map(function($file) {
    return str_replace('\\', '/', $file);
}, $allFilesToUpdate);

echo "<div class='alert alert-warning'>
        <h5><i class='fas fa-exclamation-triangle me-2'></i>Files Found</h5>
        <p>Found " . count($allFilesToUpdate) . " files that need updating:</p>
        <div class='file-list'>
            <ul class='mb-0'>";

foreach ($allFilesToUpdate as $file) {
    echo "<li><code>" . htmlspecialchars($file) . "</code></li>";
}

echo "        </ul>
        </div>
      </div>";

// Update each file
$successCount = 0;
$errorCount = 0;

foreach ($allFilesToUpdate as $file) {
    $relativePath = str_replace('./', '', $file);
    
    echo "<div class='card mb-2'>
            <div class='card-body py-2'>
                <h6 class='mb-1'><i class='fas fa-file-code me-2'></i>" . htmlspecialchars($relativePath) . "</h6>";
    
    if (file_exists($relativePath)) {
        $content = file_get_contents($relativePath);
        $originalContent = $content;
        
        // Apply replacements
        $replacements = [
            "require_once 'config/database.php';" => "require_once 'config/db_config.php';",
            "require_once '../config.php';" => "require_once '../config/db_config.php';",
            "require_once 'config/server_config.php';" => "require_once 'config/db_config.php';",
            "require_once __DIR__ . '/../config/database.php';" => "require_once __DIR__ . '/../config/db_config.php';",
            'require_once "config/database.php";' => 'require_once "config/db_config.php";',
            'require_once "../config.php";' => 'require_once "../config/db_config.php";',
            'require_once "config/server_config.php";' => 'require_once "config/db_config.php";',
            'require_once __DIR__ . "/../config/database.php";' => 'require_once __DIR__ . "/../config/db_config.php";',
        ];
        
        foreach ($replacements as $old => $new) {
            $content = str_replace($old, $new, $content);
        }
        
        if ($content !== $originalContent) {
            if (file_put_contents($relativePath, $content)) {
                echo "<p class='text-success mb-0'><i class='fas fa-check me-1'></i>Updated successfully</p>";
                $updatedFiles[] = $relativePath;
                $successCount++;
            } else {
                echo "<p class='text-danger mb-0'><i class='fas fa-times me-1'></i>Failed to write file</p>";
                $errorCount++;
            }
        } else {
            echo "<p class='text-info mb-0'><i class='fas fa-info me-1'></i>No changes needed</p>";
        }
    } else {
        echo "<p class='text-warning mb-0'><i class='fas fa-exclamation me-1'></i>File not found</p>";
        $errorCount++;
    }
    
    echo "    </div>
          </div>";
}

// Summary
echo "<div class='alert alert-" . ($errorCount > 0 ? 'warning' : 'success') . "'>
        <h5><i class='fas fa-" . ($errorCount > 0 ? 'exclamation-triangle' : 'check-circle') . " me-2'></i>Update Summary</h5>
        <div class='row'>
            <div class='col-md-4'>
                <p class='mb-0'><strong>Files Scanned:</strong> " . count($allFilesToUpdate) . "</p>
            </div>
            <div class='col-md-4'>
                <p class='mb-0'><strong>Successfully Updated:</strong> $successCount</p>
            </div>
            <div class='col-md-4'>
                <p class='mb-0'><strong>Errors:</strong> $errorCount</p>
            </div>
        </div>
      </div>";

if ($successCount > 0) {
    echo "<div class='alert alert-success'>
            <h6><i class='fas fa-check-circle me-2'></i>Successfully Updated Files:</h6>
            <div class='file-list'>
                <ul class='mb-0'>";
    
    foreach ($updatedFiles as $file) {
        echo "<li><code>" . htmlspecialchars($file) . "</code></li>";
    }
    
    echo "            </ul>
            </div>
          </div>";
}

echo "<div class='text-center mt-4'>
        <a href='configure_database.php' class='btn btn-primary btn-lg me-2'>
            <i class='fas fa-database me-2'></i>Configure Database
        </a>
        <a href='login.php' class='btn btn-success btn-lg'>
            <i class='fas fa-sign-in-alt me-2'></i>Test Login
        </a>
      </div>
      
      <div class='mt-4 text-center'>
        <small class='text-muted'>
            <i class='fas fa-info-circle me-1'></i>
            All files now use the centralized database configuration in config/db_config.php
        </small>
      </div>
    </div>
  </div>
</div>
</body>
</html>";
?>
