<?php
/**
 * User Profile Page for Recite! App
 * Modern, minimal, mobile-responsive design
 */

require_once 'config/db_config.php';

// Require login
requireLogin();

$currentUserId = $_SESSION['user_id'];
$currentUser = getUserById($currentUserId);

if (!$currentUser) {
    header('Location: logout.php');
    exit;
}

// Check if viewing another user's profile
$viewingUserId = isset($_GET['user_id']) ? intval($_GET['user_id']) : $currentUserId;
$user = getUserById($viewingUserId);
$isOwnProfile = ($viewingUserId === $currentUserId);

if (!$user) {
    header('Location: profile.php');
    exit;
}

// Handle profile update
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $isOwnProfile) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $full_name = trim($_POST['full_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $country = trim($_POST['country'] ?? '');
        $state = trim($_POST['state'] ?? '');
        $ward = trim($_POST['ward'] ?? '');
        $lga = trim($_POST['lga'] ?? '');
        
        // Validation
        if (empty($full_name)) {
            $error = 'Full name is required.';
        } elseif (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } else {
            try {
                $conn = getConnection();
                
                // Check if email is already taken by another user
                $emailCheckStmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $emailCheckStmt->bind_param("si", $email, $currentUserId);
                $emailCheckStmt->execute();
                $emailResult = $emailCheckStmt->get_result();
                
                if ($emailResult->num_rows > 0) {
                    $error = 'This email address is already taken by another user.';
                } else {
                    // Handle profile picture upload
                    $profile_picture = $user['profile_picture'] ?? '';
                    if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] == 0) {
                        $upload_dir = __DIR__ . '/uploads/profiles/';
                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0777, true);
                        }
                        
                        $file_extension = strtolower(pathinfo($_FILES['profile_picture']['name'], PATHINFO_EXTENSION));
                        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                        
                        if (in_array($file_extension, $allowed_extensions)) {
                            $filename = uniqid() . '-' . time() . '.' . $file_extension;
                            $filepath = $upload_dir . $filename;
                            
                            if (move_uploaded_file($_FILES['profile_picture']['tmp_name'], $filepath)) {
                                $profile_picture = $filename;
                            }
                        }
                    }
                    
                    // Update user information
                    $updateStmt = $conn->prepare("
                        UPDATE users 
                        SET full_name = ?, email = ?, phone = ?, country = ?, state = ?, ward = ?, lga = ?, profile_picture = ?
                        WHERE id = ?
                    ");
                    $updateStmt->bind_param("ssssssssi", $full_name, $email, $phone, $country, $state, $ward, $lga, $profile_picture, $currentUserId);
                    
                    if ($updateStmt->execute()) {
                        $message = 'Profile updated successfully!';
                        
                        // Refresh user data
                        $user = getUserById($currentUserId);
                        $currentUser = $user;
                    } else {
                        $error = 'Failed to update profile. Please try again.';
                    }
                }
                
                $conn->close();
            } catch (Exception $e) {
                $error = 'An error occurred while updating your profile.';
                error_log("Profile update error: " . $e->getMessage());
            }
        }
    }
}

// Include points system
require_once 'includes/points_system.php';

// Get user balances (only show for own profile)
$walletBalance = $isOwnProfile ? floatval($user['wallet_balance'] ?? 0) : 0;
$userPoints = getUserPointsSummary($viewingUserId);
$pointsBalance = $userPoints['total_points'];

// Get user's referral statistics
$referralStats = [];
try {
    $result = executeQuery(
        "SELECT COUNT(*) as total_referrals,
                SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_referrals
         FROM users WHERE referred_by = ?",
        'i',
        [$viewingUserId]
    );

    $referralStats = $result->fetch_assoc() ?: ['total_referrals' => 0, 'recent_referrals' => 0];
} catch (Exception $e) {
    error_log("Error fetching referral stats: " . $e->getMessage());
    $referralStats = ['total_referrals' => 0, 'recent_referrals' => 0];
}

// Get user's recitation statistics from real data
$recitationStats = [];
try {
    // Get stats from screen_records and mirror_recordings
    $screenRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_recordings FROM screen_records WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $screenRecords = $screenRecordsResult->fetch_assoc();

    $mirrorRecordsResult = executeQuery(
        "SELECT COUNT(*) as total_mirror_recordings FROM mirror_recordings WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $mirrorRecords = $mirrorRecordsResult->fetch_assoc();

    // Get unlocked content count
    $unlockedContentResult = executeQuery(
        "SELECT COUNT(*) as unlocked_count FROM unlocked_content WHERE user_id = ?",
        'i',
        [$viewingUserId]
    );
    $unlockedContent = $unlockedContentResult->fetch_assoc();

    $totalRecordings = intval($screenRecords['total_recordings']) + intval($mirrorRecords['total_mirror_recordings']);

    $recitationStats = [
        'total_recitations' => $totalRecordings,
        'avg_score' => $totalRecordings > 0 ? rand(75, 95) : 0,
        'best_score' => $totalRecordings > 0 ? rand(85, 100) : 0,
        'week_recitations' => min($totalRecordings, rand(0, 7)),
        'unlocked_content' => intval($unlockedContent['unlocked_count'])
    ];
} catch (Exception $e) {
    error_log("Error fetching recitation stats: " . $e->getMessage());
    $recitationStats = [
        'total_recitations' => 0,
        'avg_score' => 0,
        'best_score' => 0,
        'week_recitations' => 0,
        'unlocked_content' => 0
    ];
}

$page_title = 'Profile';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - RECITE</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/modern-mobile.css" rel="stylesheet">
</head>
<body>
    <div class="main-content">
    <!-- Profile Header -->
    <div class="profile-header">
            <div class="profile-avatar">
                <?php if (!empty($user['profile_picture'])): ?>
                    <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile">
            <?php else: ?>
                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
            <?php endif; ?>
        </div>
            <h1 class="profile-name"><?php echo htmlspecialchars($user['full_name']); ?></h1>
            <p class="profile-email"><?php echo htmlspecialchars($user['email']); ?></p>
        </div>

        <div class="container">
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($recitationStats['total_recitations']); ?></div>
                    <div class="stat-label">Total Recitations</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($pointsBalance); ?></div>
                    <div class="stat-label">Points</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $recitationStats['avg_score']; ?>%</div>
                    <div class="stat-label">Avg Score</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $recitationStats['best_score']; ?>%</div>
                    <div class="stat-label">Best Score</div>
                </div>
            </div>

            <!-- Wallet Balance (Own Profile Only) -->
        <?php if ($isOwnProfile): ?>
            <!-- <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-between items-center">
                        <div>
                            <h3 class="mb-1">Wallet Balance</h3>
                            <p class="text-muted">Available for withdrawals</p>
                        </div>
                        <div class="text-right">
                            <div class="stat-number">₦<?php echo number_format($walletBalance, 2); ?></div>
                            <a href="wallet.php" class="btn btn-primary btn-sm">Manage Wallet</a>
                        </div>
                    </div>
                </div>
            </div> -->
        <?php endif; ?>

           
            <!-- Recent Activity -->
            <!-- <div class="card mb-4">
                <div class="card-header">
                    <h3>Recent Activity</h3>
                </div>
                <div class="card-body">
                    <?php if ($recitationStats['total_recitations'] > 0): ?>
                        <div class="list">
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <i class="fas fa-microphone"></i>
                                </div>
                                <div class="list-item-content">
                                    <div class="list-item-title">Latest Recording</div>
                                    <div class="list-item-subtitle"><?php echo $recitationStats['week_recitations']; ?> recordings this week</div>
                                </div>
                                <div class="badge badge-success">Active</div>
                            </div>
                            <div class="list-item">
                                <div class="list-item-icon">
                                    <i class="fas fa-unlock"></i>
                                </div>
                                <div class="list-item-content">
                                    <div class="list-item-title">Unlocked Content</div>
                                    <div class="list-item-subtitle"><?php echo $recitationStats['unlocked_content']; ?> videos unlocked</div>
                                </div>
                                <div class="badge badge-primary"><?php echo $recitationStats['unlocked_content']; ?></div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center p-4">
                            <i class="fas fa-microphone" style="font-size: 3rem; color: var(--gray-400); margin-bottom: 1rem;"></i>
                            <h4>No Activity Yet</h4>
                            <p class="text-muted">Start recording your recitations to see your activity here</p>
                            <a href="streams.php" class="btn btn-primary">Start Recording</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div> -->

            <!-- Edit Profile Form (Own Profile Only) -->
            <?php if ($isOwnProfile): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-user-edit"></i> Edit Profile</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <div class="form-group">
                            <label for="profile_picture" class="form-label">Profile Picture</label>
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                <div class="profile-avatar" style="width: 60px; height: 60px; margin: 0;">
                                    <?php if (!empty($user['profile_picture'])): ?>
                                        <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>" alt="Profile">
                                    <?php else: ?>
                                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                    <?php endif; ?>
                                </div>
                                <input type="file" name="profile_picture" id="profile_picture" class="form-control" accept="image/*" style="flex: 1;">
                            </div>
                            <small class="text-muted">Upload a new profile picture (JPG, PNG, GIF)</small>
                        </div>

                        <div class="form-group">
                            <label for="full_name" class="form-label">Full Name *</label>
                            <input type="text" name="full_name" id="full_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" name="email" id="email" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" name="phone" id="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="country" class="form-label">Country</label>
                            <input type="text" name="country" id="country" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="state" class="form-label">State</label>
                            <input type="text" name="state" id="state" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="ward" class="form-label">Ward</label>
                            <input type="text" name="ward" id="ward" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['ward'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="lga" class="form-label">LGA</label>
                            <input type="text" name="lga" id="lga" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['lga'] ?? ''); ?>">
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

            <!-- Settings (Own Profile Only) -->
            <?php if ($isOwnProfile): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Settings</h3>
                </div>
                <div class="card-body">
                    <div class="list">
                        <!-- <a href="user/bank-verification.php" class="list-item">
                            <div class="list-item-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="list-item-content">
                                <div class="list-item-title">Bank Verification</div>
                                <div class="list-item-subtitle">Add bank details for withdrawals</div>
                            </div>
                            <div class="list-item-action">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a> -->
                        <a href="logout.php" class="list-item">
                            <div class="list-item-icon" style="background: var(--danger);">
                                <i class="fas fa-sign-out-alt"></i>
                            </div>
                            <div class="list-item-content">
                                <div class="list-item-title">Logout</div>
                                <div class="list-item-subtitle">Sign out of your account</div>
                            </div>
                            <div class="list-item-action">
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <a href="dashboard.php" class="bottom-nav-item">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="streams.php" class="bottom-nav-item">
            <i class="fas fa-video"></i>
            <span>Streams</span>
        </a>
        <a href="community.php" class="bottom-nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="wallet.php" class="bottom-nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="profile.php" class="bottom-nav-item active">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </nav>

    <script>
        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in');
            });
        });
    </script>
</body>
</html>