<?php
/**
 * Production Server Setup Script
 * Run this on your production server to configure everything properly
 */

echo "🚀 Production Server Setup\n";
echo "==========================\n\n";

// Step 1: Check PHP version
echo "📋 Step 1: Checking PHP version...\n";
echo "PHP Version: " . PHP_VERSION . "\n";
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    echo "⚠️  Warning: PHP version should be 7.4 or higher for best compatibility\n";
} else {
    echo "✅ PHP version is good\n";
}

// Step 2: Check MySQL version
echo "\n📋 Step 2: Checking MySQL version...\n";
require_once 'config/db_config.php';
$conn = getConnection();

if ($conn) {
    $result = $conn->query("SELECT VERSION() as version");
    if ($result) {
        $row = $result->fetch_assoc();
        $mysqlVersion = $row['version'];
        echo "MySQL Version: " . $mysqlVersion . "\n";
        
        if (version_compare($mysqlVersion, '5.7.0', '<')) {
            echo "⚠️  Warning: MySQL version is older than 5.7. Consider upgrading for better performance\n";
        } else {
            echo "✅ MySQL version is good\n";
        }
    }
    $conn->close();
} else {
    echo "❌ Cannot connect to database. Please check your database credentials in config/db_config.php\n";
}

// Step 3: Check required directories
echo "\n📋 Step 3: Checking required directories...\n";
$directories = [
    'uploads',
    'uploads/profiles',
    'uploads/recordings',
    'uploads/mirrors',
    'uploads/screen-recordings',
    'uploads/thumbnails',
    'logs'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir\n";
        } else {
            echo "❌ Failed to create directory: $dir\n";
        }
    } else {
        echo "✅ Directory exists: $dir\n";
    }
}

// Step 4: Check file permissions
echo "\n📋 Step 4: Checking file permissions...\n";
$files = [
    'config/db_config.php',
    'logs/php_errors.log'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $perms = substr(sprintf('%o', $perms), -4);
        echo "✅ File: $file (permissions: $perms)\n";
    } else {
        echo "❌ File missing: $file\n";
    }
}

// Step 5: Test database connection
echo "\n📋 Step 5: Testing database connection...\n";
$conn = getConnection();
if ($conn) {
    echo "✅ Database connection successful\n";
    
    // Test basic queries
    $tables = ['users', 'transactions', 'point_transactions'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "✅ Table exists: $table\n";
        } else {
            echo "❌ Table missing: $table\n";
        }
    }
    $conn->close();
} else {
    echo "❌ Database connection failed\n";
}

// Step 6: Production configuration recommendations
echo "\n📋 Step 6: Production Configuration Recommendations\n";
echo "==================================================\n\n";

echo "🔧 Update your config/db_config.php with production credentials:\n";
echo "   - Change DB_HOST, DB_NAME, DB_USER, DB_PASS\n";
echo "   - Update BASE_URL to your domain\n";
echo "   - Set DEVELOPMENT_MODE to false\n\n";

echo "🔒 Security recommendations:\n";
echo "   - Use strong database passwords\n";
echo "   - Enable HTTPS on your domain\n";
echo "   - Set proper file permissions (755 for directories, 644 for files)\n";
echo "   - Keep PHP and MySQL updated\n\n";

echo "📊 Performance recommendations:\n";
echo "   - Enable PHP OPcache\n";
echo "   - Use MySQL query caching\n";
echo "   - Consider using a CDN for static assets\n";
echo "   - Monitor error logs regularly\n\n";

echo "🎯 Next steps:\n";
echo "   1. Update database credentials in config/db_config.php\n";
echo "   2. Run the forum system setup: php scripts/forum_system/setup_forum_tables.php\n";
echo "   3. Run the video unlock system setup: php scripts/video_unlock_system/setup_video_unlock.php\n";
echo "   4. Test all pages to ensure they work properly\n\n";

echo "✅ Production setup check completed!\n";
?>
