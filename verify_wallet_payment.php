<?php
/**
 * Wallet Payment Verification for Recite! App
 * Verifies Paystack payments and credits user wallet
 */

require_once 'config/database.php';

// Require login
requireLogin();

$error = '';
$success = '';

// Check if reference is provided
if (!isset($_GET['reference']) && !isset($_POST['reference'])) {
    header('Location: wallet.php');
    exit;
}

$reference = sanitize($_GET['reference'] ?? $_POST['reference']);
$userId = $_SESSION['user_id'];

try {
    // DEVELOPMENT MODE: Mock payment verification for testing
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        error_log("DEVELOPMENT MODE: Using mock wallet payment verification for reference: $reference");

        // Extract amount and user ID from reference (format: FUND_USERID_AMOUNT_RANDOM)
        $amount = 0;
        $verifiedUserId = null;
        if (preg_match('/^FUND_(\d+)_([\d\.]+)_/', $reference, $matches)) {
            $verifiedUserId = (int)$matches[1];
            $amount = (float)$matches[2];
        }

        if (!$verifiedUserId || $amount <= 0) {
            throw new Exception("Could not parse user ID or amount from development reference string.");
        }
        
        // Mock successful payment data
        $result = [
            'status' => true,
            'data' => [
                'status' => 'success',
                'amount' => $amount * 100, // store as kobo for consistency
                'metadata' => [
                    'user_id' => $verifiedUserId
                ],
                'reference' => $reference
            ]
        ];
        error_log("Mock wallet payment data: " . json_encode($result));

    } else {
        // PRODUCTION MODE: Verify payment with Paystack using cURL for better reliability
        $paystackUrl = "https://api.paystack.co/transaction/verify/" . $reference;
        $paystackHeaders = [
            "Authorization: Bearer " . PAYSTACK_SECRET_KEY,
            "Cache-Control: no-cache",
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paystackUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $paystackHeaders);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            $curlError = curl_error($ch);
            curl_close($ch);
            throw new Exception("Payment verification failed: " . $curlError);
        }
        curl_close($ch);
        
        $result = json_decode($response, true);
    }
    
    if ($result && isset($result['status']) && $result['status'] === true && isset($result['data']['status']) && $result['data']['status'] === 'success') {
        $paymentData = $result['data'];
        $verifiedUserId = $paymentData['metadata']['user_id'] ?? null;
        $amount = $paymentData['amount'] / 100; // Convert from kobo to naira
        
        // Final security check: Ensure the logged-in user matches the user from metadata
        if ($verifiedUserId != $userId) {
            throw new Exception("Payment metadata mismatch. Security check failed.");
        }
        
        // Check if transaction has already been processed to prevent double-crediting
        $existingTx = executeQuery("SELECT id FROM transactions WHERE reference_id = ?", 's', [$reference]);
        
        if ($existingTx->num_rows > 0) {
            $success = 'Payment has already been credited to your wallet.';
        } else {
            $conn = getConnection();
            $conn->begin_transaction();
            
            try {
                // Credit user's wallet
                executeQuery("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?", 'di', [$amount, $userId]);
                
                // Record the successful transaction
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, amount, description, reference_id, status) VALUES (?, 'deposit', ?, 'Wallet funding via Paystack', ?, 'completed')",
                    'ids',
                    [$userId, $amount, $reference]
                );
                
                $conn->commit();
                $success = "Payment successful! Your wallet has been credited with ₦" . number_format($amount, 2) . ".";
                
            } catch (Exception $e) {
                $conn->rollback();
                throw $e; // Re-throw to be caught by the outer catch block
            }
        }
        
    } else {
        $gatewayMessage = $result['message'] ?? 'No response from gateway.';
        throw new Exception('Payment verification failed. Gateway response: ' . $gatewayMessage);
    }
    
} catch (Exception $e) {
    error_log("Wallet payment verification error for user $userId: " . $e->getMessage());
    $error = 'Payment verification failed: ' . $e->getMessage();
}

// Redirect back to wallet page with a clear message
if (!empty($success)) {
    header('Location: wallet.php?wallet_message=' . urlencode($success) . '&wallet_status=success');
    exit;
} else {
    header('Location: wallet.php?wallet_message=' . urlencode($error) . '&wallet_status=error');
    exit;
}

$page_title = 'Payment Verification';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    
    <!-- Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Inter', sans-serif;
        }

        .verification-container {
            background: white;
            border-radius: 16px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .success-icon {
            color: #28a745;
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .error-icon {
            color: #dc3545;
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .verification-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        .verification-message {
            font-size: 1rem;
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            color: white;
        }
    </style>
</head>
<body>
    <?php if ($error): ?>
        <div class="alert alert-danger text-center" style="margin: 2rem auto; max-width: 500px;">
            <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>
    <div class="verification-container">
        <?php if ($success): ?>
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="verification-title">Payment Successful!</h1>
            <p class="verification-message"><?php echo $success; ?></p>
            
            <script>
                // Auto redirect after 3 seconds
                setTimeout(function() {
                    window.location.href = 'wallet.php';
                }, 3000);
            </script>
        <?php else: ?>
            <div class="error-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            <h1 class="verification-title">Payment Verification Failed</h1>
            <p class="verification-message"><?php echo $error; ?></p>
        <?php endif; ?>
        
        <a href="wallet.php" class="btn btn-primary">
            <i class="fas fa-wallet"></i> Return to Wallet
        </a>
    </div>
</body>
</html>
