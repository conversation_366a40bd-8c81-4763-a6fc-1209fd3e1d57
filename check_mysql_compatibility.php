<?php
/**
 * MySQL Compatibility Checker for Universal Reciters
 * This script checks your MySQL version and suggests the best collation to use
 */

// Force error display
error_reporting(E_ALL);
ini_set('display_errors', 1);

$results = [];
$recommendations = [];

// Database credentials - UPDATE THESE WITH YOUR ACTUAL CREDENTIALS
$db_host = 'localhost';
$db_name = 'your_database_name';  // CHANGE THIS
$db_user = 'your_db_username';    // CHANGE THIS  
$db_pass = 'your_db_password';    // CHANGE THIS

// Test database connection and get MySQL info
try {
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    
    if ($conn->connect_error) {
        throw new Exception('Connection failed: ' . $conn->connect_error);
    }
    
    $results['connection'] = 'SUCCESS';
    
    // Get MySQL version
    $version_result = $conn->query("SELECT VERSION() as version");
    $version_row = $version_result->fetch_assoc();
    $mysql_version = $version_row['version'];
    $results['mysql_version'] = $mysql_version;
    
    // Extract version number for comparison
    preg_match('/^(\d+)\.(\d+)\.(\d+)/', $mysql_version, $version_parts);
    $major = (int)$version_parts[1];
    $minor = (int)$version_parts[2];
    $patch = (int)$version_parts[3];
    
    // Check available collations
    $collations_result = $conn->query("SHOW COLLATION WHERE Charset = 'utf8mb4'");
    $available_collations = [];
    while ($row = $collations_result->fetch_assoc()) {
        $available_collations[] = $row['Collation'];
    }
    $results['available_collations'] = $available_collations;
    
    // Determine best collation based on MySQL version
    if ($major >= 8) {
        // MySQL 8.0+
        if (in_array('utf8mb4_0900_ai_ci', $available_collations)) {
            $recommended_collation = 'utf8mb4_0900_ai_ci';
            $recommendations[] = 'MySQL 8.0+ detected. Using modern utf8mb4_0900_ai_ci collation.';
        } else {
            $recommended_collation = 'utf8mb4_unicode_ci';
            $recommendations[] = 'MySQL 8.0+ detected but utf8mb4_0900_ai_ci not available. Using utf8mb4_unicode_ci.';
        }
    } elseif ($major == 5 && $minor >= 7) {
        // MySQL 5.7+
        if (in_array('utf8mb4_unicode_ci', $available_collations)) {
            $recommended_collation = 'utf8mb4_unicode_ci';
            $recommendations[] = 'MySQL 5.7+ detected. Using utf8mb4_unicode_ci collation.';
        } else {
            $recommended_collation = 'utf8mb4_general_ci';
            $recommendations[] = 'MySQL 5.7+ detected but utf8mb4_unicode_ci not available. Using utf8mb4_general_ci.';
        }
    } else {
        // Older MySQL versions
        $recommended_collation = 'utf8mb4_general_ci';
        $recommendations[] = 'Older MySQL version detected. Using utf8mb4_general_ci for maximum compatibility.';
    }
    
    $results['recommended_collation'] = $recommended_collation;
    
    // Test the recommended collation by creating a test table
    $test_table_sql = "
        CREATE TEMPORARY TABLE test_collation (
            id INT AUTO_INCREMENT PRIMARY KEY,
            test_field VARCHAR(50) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=$recommended_collation
    ";
    
    if ($conn->query($test_table_sql)) {
        $results['collation_test'] = 'SUCCESS';
        $recommendations[] = "Collation $recommended_collation works correctly with your MySQL version.";
    } else {
        $results['collation_test'] = 'FAILED: ' . $conn->error;
        $recommended_collation = 'utf8mb4_general_ci'; // Fallback
        $recommendations[] = "Falling back to utf8mb4_general_ci for maximum compatibility.";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $results['connection'] = 'FAILED: ' . $e->getMessage();
    $recommendations[] = 'Please check your database credentials and ensure MySQL server is running.';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL Compatibility Check - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>MySQL Compatibility Check
                        </h4>
                        <small>Universal Reciters - Database Configuration Helper</small>
                    </div>
                    <div class="card-body">
                        
                        <?php if ($results['connection'] === 'SUCCESS'): ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>Database Connection Successful!</h5>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Database Information:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <strong>MySQL Version:</strong>
                                        <code><?php echo htmlspecialchars($results['mysql_version']); ?></code>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <strong>Connection Status:</strong>
                                        <span class="badge bg-success">Connected</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <strong>Recommended Collation:</strong>
                                        <code><?php echo $results['recommended_collation']; ?></code>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <strong>Collation Test:</strong>
                                        <span class="badge bg-<?php echo $results['collation_test'] === 'SUCCESS' ? 'success' : 'warning'; ?>">
                                            <?php echo $results['collation_test']; ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Recommendations:</h6>
                                <div class="alert alert-info">
                                    <ul class="mb-0">
                                        <?php foreach ($recommendations as $rec): ?>
                                        <li><?php echo htmlspecialchars($rec); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6>Corrected SQL for Admin Table:</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <pre><code>CREATE TABLE IF NOT EXISTS `admins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=<?php echo $results['recommended_collation'] ?? 'utf8mb4_general_ci'; ?>;</code></pre>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="setup_production.php" class="btn btn-primary">
                                <i class="fas fa-cogs me-2"></i>Continue with Setup
                            </a>
                            <a href="database/create_admin_table.sql" class="btn btn-secondary" download>
                                <i class="fas fa-download me-2"></i>Download SQL File
                            </a>
                        </div>
                        
                        <?php else: ?>
                        
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Database Connection Failed</h5>
                            <p><?php echo htmlspecialchars($results['connection']); ?></p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6>Please update the database credentials in this file:</h6>
                            <ul class="mb-0">
                                <li><strong>$db_host:</strong> Your database host (usually 'localhost')</li>
                                <li><strong>$db_name:</strong> Your database name</li>
                                <li><strong>$db_user:</strong> Your database username</li>
                                <li><strong>$db_pass:</strong> Your database password</li>
                            </ul>
                        </div>
                        
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                This tool helps determine the best MySQL collation for your server version.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
