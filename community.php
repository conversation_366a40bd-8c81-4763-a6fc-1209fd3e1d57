<?php
/**
 * Forum/Community Page for Recite! App
 * Complete forum system with posts, replies, likes, and interactions
 */

require_once 'config/db_config.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Community Forum';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize variables
$message = '';
$error = '';

// Handle forum interactions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'create_post') {
        $title = trim($_POST['title'] ?? '');
        $content = trim($_POST['content'] ?? '');
        $category = $_POST['category'] ?? 'general';

        if (!empty($title) && !empty($content)) {
            $conn = getConnection();
            $stmt = $conn->prepare("INSERT INTO forum_posts (user_id, title, content, category, created_at) VALUES (?, ?, ?, ?, NOW())");
            $stmt->bind_param("isss", $userId, $title, $content, $category);
            
            if ($stmt->execute()) {
                $message = 'Post created successfully!';
                } else {
                $error = 'Failed to create post.';
                }
            $stmt->close();
            $conn->close();
            } else {
            $error = 'Please fill in both title and content.';
        }
    } elseif ($action === 'create_reply') {
        $postId = intval($_POST['post_id']);
        $content = trim($_POST['reply_content'] ?? '');

        if ($postId > 0 && !empty($content)) {
            $conn = getConnection();
            $stmt = $conn->prepare("INSERT INTO forum_replies (post_id, user_id, content, created_at) VALUES (?, ?, ?, NOW())");
            $stmt->bind_param("iis", $postId, $userId, $content);
            
            if ($stmt->execute()) {
                // Update post reply count
                $updateStmt = $conn->prepare("UPDATE forum_posts SET reply_count = reply_count + 1 WHERE id = ?");
                $updateStmt->bind_param("i", $postId);
                $updateStmt->execute();
                $updateStmt->close();
                
                $message = 'Reply posted successfully!';
                } else {
                $error = 'Failed to post reply.';
                }
            $stmt->close();
            $conn->close();
            } else {
            $error = 'Please enter a reply.';
        }
    } elseif ($action === 'like_post') {
        $postId = intval($_POST['post_id']);
        
        if ($postId > 0) {
            $conn = getConnection();
            
            // Check if already liked
            $checkStmt = $conn->prepare("SELECT id FROM forum_likes WHERE post_id = ? AND user_id = ?");
            $checkStmt->bind_param("ii", $postId, $userId);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            
            if ($result->num_rows == 0) {
                // Add like
                $likeStmt = $conn->prepare("INSERT INTO forum_likes (post_id, user_id, created_at) VALUES (?, ?, NOW())");
                $likeStmt->bind_param("ii", $postId, $userId);
                $likeStmt->execute();
                $likeStmt->close();
                
                // Update post like count
                $updateStmt = $conn->prepare("UPDATE forum_posts SET like_count = like_count + 1 WHERE id = ?");
                $updateStmt->bind_param("i", $postId);
                $updateStmt->execute();
                $updateStmt->close();
                
                $message = 'Post liked!';
                } else {
                // Remove like
                $unlikeStmt = $conn->prepare("DELETE FROM forum_likes WHERE post_id = ? AND user_id = ?");
                $unlikeStmt->bind_param("ii", $postId, $userId);
                $unlikeStmt->execute();
                $unlikeStmt->close();
                
                // Update post like count
                $updateStmt = $conn->prepare("UPDATE forum_posts SET like_count = like_count - 1 WHERE id = ?");
                $updateStmt->bind_param("i", $postId);
                $updateStmt->execute();
                $updateStmt->close();
                
                $message = 'Post unliked!';
            }
            $checkStmt->close();
            $conn->close();
        }
    }
}

// Get forum data
$conn = getConnection();

// Get filter parameters
$category = $_GET['category'] ?? 'all';
$sort = $_GET['sort'] ?? 'latest';

// Build query based on filters
$whereClause = "";
$orderClause = "ORDER BY p.created_at DESC";

if ($category !== 'all') {
    $whereClause = "WHERE p.category = '$category'";
}

if ($sort === 'popular') {
    $orderClause = "ORDER BY p.like_count DESC, p.reply_count DESC, p.created_at DESC";
} elseif ($sort === 'recent') {
    $orderClause = "ORDER BY p.updated_at DESC, p.created_at DESC";
}

// Get forum posts with user info and stats
$postsQuery = "
    SELECT p.*, u.full_name, u.profile_picture,
           (SELECT COUNT(*) FROM forum_replies WHERE post_id = p.id) as actual_reply_count,
           (SELECT COUNT(*) FROM forum_likes WHERE post_id = p.id) as actual_like_count,
           EXISTS(SELECT 1 FROM forum_likes WHERE post_id = p.id AND user_id = ?) as user_liked
    FROM forum_posts p
    JOIN users u ON p.user_id = u.id
    $whereClause
    $orderClause
    LIMIT 50
";

$stmt = $conn->prepare($postsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$posts = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get categories for filter
$categoriesQuery = "SELECT category, COUNT(*) as count FROM forum_posts GROUP BY category ORDER BY count DESC";
$categoriesResult = $conn->query($categoriesQuery);
$categories = [];
while ($row = $categoriesResult->fetch_assoc()) {
    $categories[] = $row;
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--secondary);
            color: var(--text);
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profile-avatar:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .search-input {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1rem;
            width: 300px;
            color: var(--text);
        }

        .search-input:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
        }

        .welcome-card {
            text-align: center;
        }

        .welcome-card h2 {
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .forum-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .filter-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
            transform: translateY(-2px);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary);
            color: var(--primary);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary);
            color: white;
        }

        .btn-outline-primary.active {
            background: var(--primary);
            color: white;
        }

        .post-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            background: var(--primary);
            color: white;
        }

        .post-meta {
            flex: 1;
        }

        .post-author {
            font-weight: 600;
            color: var(--text);
            margin-bottom: 0.25rem;
        }

        .post-info {
            font-size: 0.9rem;
            color: #666;
        }

        .post-category {
            background: var(--primary);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .post-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text);
        }

        .post-content {
            color: var(--text);
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .post-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            background: var(--secondary);
            color: var(--text);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .action-btn:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-1px);
        }

        .action-btn.liked {
            background: var(--danger);
            color: white;
        }

        .action-btn.liked:hover {
            background: #c82333;
        }

        .reply-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .reply-form {
            display: none;
            margin-bottom: 1rem;
        }

        .reply-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            resize: vertical;
            min-height: 80px;
        }

        .reply-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .replies-list {
            margin-top: 1rem;
        }

        .reply-item {
            background: var(--secondary);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .reply-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .reply-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            background: var(--primary);
            color: white;
            font-size: 0.8rem;
        }

        .reply-author {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .reply-date {
            font-size: 0.8rem;
            color: #666;
            margin-left: auto;
        }

        .reply-content {
            color: var(--text);
            line-height: 1.5;
        }

        .create-post-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
            background: white;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Bottom Navigation Styles */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            padding: 0.5rem 0;
            z-index: 100;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text);
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .bottom-nav-item:hover {
            background: var(--secondary);
            color: var(--primary);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.1);
        }

        .bottom-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .bottom-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
            text-align: center;
        }

        /* Add bottom padding to main content to account for fixed navigation */
            .container {
            padding-bottom: 100px;
        }

        @media (max-width: 768px) {
            .forum-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .search-input {
                width: 100%;
            }

            .post-actions {
                flex-wrap: wrap;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .bottom-nav-item {
                min-width: 50px;
                padding: 0.25rem;
            }

            .bottom-nav-item i {
                font-size: 1rem;
            }

            .bottom-nav-item span {
                font-size: 0.6rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                            <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
                    <div>
                        <h4 style="margin: 0;"><?php echo htmlspecialchars($user['full_name']); ?></h4>
                        <small style="opacity: 0.8;">Member</small>
                </div>
            </div>

            <div class="header-center">
                    <input type="text" class="search-input" placeholder="Search forum posts...">
            </div>

            <div class="header-right">
                    <button class="btn btn-primary" onclick="openCreatePostModal()">
                        <i class="fas fa-plus"></i> New Post
                    </button>
            </div>
        </div>

        <div class="welcome-card">
                <h2><i class="fas fa-comments"></i> Community Forum</h2>
                <p>Connect, discuss, and share with fellow Qur'an reciters</p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Forum Controls -->
        <div class="forum-controls">
            <div class="filter-controls">
                <div>
                    <label style="font-weight: 500; margin-right: 0.5rem;">Category:</label>
                    <select onchange="filterByCategory(this.value)" class="form-select" style="width: auto; display: inline-block;">
                        <option value="all" <?php echo $category === 'all' ? 'selected' : ''; ?>>All Categories</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat['category']); ?>" <?php echo $category === $cat['category'] ? 'selected' : ''; ?>>
                                <?php echo ucfirst(htmlspecialchars($cat['category'])); ?> (<?php echo $cat['count']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                                </div>

                <div style="margin-left: 1rem;">
                    <label style="font-weight: 500; margin-right: 0.5rem;">Sort by:</label>
                    <select onchange="sortPosts(this.value)" class="form-select" style="width: auto; display: inline-block;">
                        <option value="latest" <?php echo $sort === 'latest' ? 'selected' : ''; ?>>Latest</option>
                        <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>Most Popular</option>
                        <option value="recent" <?php echo $sort === 'recent' ? 'selected' : ''; ?>>Recently Active</option>
                    </select>
                                    </div>
                                </div>

            <div>
                <button class="btn btn-primary" onclick="openCreatePostModal()">
                    <i class="fas fa-plus"></i> Create New Post
                                    </button>
                </div>
            </div>

        <!-- Forum Posts -->
        <?php if (empty($posts)): ?>
            <div class="empty-state">
                <i class="fas fa-comments"></i>
                <h3>No Posts Yet</h3>
                <p>Be the first to start a discussion in the community!</p>
                <button class="btn btn-primary" onclick="openCreatePostModal()">
                    <i class="fas fa-plus"></i> Create First Post
                                    </button>
                    </div>
        <?php else: ?>
            <?php foreach ($posts as $post): ?>
                <div class="post-card" data-post-id="<?php echo $post['id']; ?>">
                    <div class="post-header">
                        <div class="post-avatar">
                            <?php if (!empty($post['profile_picture'])): ?>
                                <img src="uploads/profiles/<?php echo htmlspecialchars($post['profile_picture']); ?>"
                                                 alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                        <?php else: ?>
                                <?php echo strtoupper(substr($post['full_name'], 0, 1)); ?>
                                        <?php endif; ?>
                                    </div>
                        <div class="post-meta">
                            <div class="post-author"><?php echo htmlspecialchars($post['full_name']); ?></div>
                            <div class="post-info">
                                <?php echo date('M j, Y \a\t g:i A', strtotime($post['created_at'])); ?>
                                • <?php echo $post['category']; ?>
                                        </div>
                                        </div>
                        <span class="post-category"><?php echo ucfirst(htmlspecialchars($post['category'])); ?></span>
                                    </div>

                    <div class="post-title"><?php echo htmlspecialchars($post['title']); ?></div>
                    <div class="post-content"><?php echo nl2br(htmlspecialchars($post['content'])); ?></div>

                    <div class="post-actions">
                        <button class="action-btn <?php echo $post['user_liked'] ? 'liked' : ''; ?>" 
                                onclick="likePost(<?php echo $post['id']; ?>)">
                            <i class="fas fa-heart"></i>
                            <span id="like-count-<?php echo $post['id']; ?>"><?php echo $post['actual_like_count']; ?></span>
                                        </button>
                        
                        <button class="action-btn" onclick="toggleReplies(<?php echo $post['id']; ?>)">
                            <i class="fas fa-comment"></i>
                            <span id="reply-count-<?php echo $post['id']; ?>"><?php echo $post['actual_reply_count']; ?></span>
                                    </button>

                        <button class="action-btn" onclick="sharePost(<?php echo $post['id']; ?>)">
                            <i class="fas fa-share"></i>
                            Share
                                        </button>
                                </div>

                    <!-- Reply Section -->
                    <div class="reply-section" id="reply-section-<?php echo $post['id']; ?>" style="display: none;">
                        <button class="btn btn-outline-primary" onclick="toggleReplyForm(<?php echo $post['id']; ?>)">
                            <i class="fas fa-reply"></i> Reply to this post
                                            </button>
                        
                        <div class="reply-form" id="reply-form-<?php echo $post['id']; ?>">
                            <textarea id="reply-input-<?php echo $post['id']; ?>" class="reply-input" placeholder="Write your reply..." required></textarea>
                            <div style="margin-top: 0.5rem;">
                                <button type="button" class="btn btn-primary" onclick="submitMainReply(<?php echo $post['id']; ?>)">Post Reply</button>
                                <button type="button" class="btn btn-secondary" onclick="toggleReplyForm(<?php echo $post['id']; ?>)">Cancel</button>
                                        </div>
                                </div>

                        <div class="replies-list" id="replies-list-<?php echo $post['id']; ?>">
                            <!-- Replies will be loaded here -->
                            </div>
                        </div>
                </div>
            <?php endforeach; ?>
                    <?php endif; ?>
                </div>

    <!-- Create Post Modal -->
    <div class="create-post-modal" id="createPostModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">Create New Post</div>
                <button class="modal-close" onclick="closeCreatePostModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form method="POST">
                <input type="hidden" name="action" value="create_post">
                
                <div class="form-group">
                    <label class="form-label">Title</label>
                    <input type="text" name="title" class="form-control" placeholder="Enter post title..." required>
                                </div>

                <div class="form-group">
                    <label class="form-label">Category</label>
                    <select name="category" class="form-select" required>
                        <option value="general">General Discussion</option>
                        <option value="recitation">Recitation Tips</option>
                        <option value="tajweed">Tajweed Rules</option>
                        <option value="memorization">Memorization</option>
                        <option value="questions">Questions & Help</option>
                        <option value="announcements">Announcements</option>
                    </select>
                                </div>

                <div class="form-group">
                    <label class="form-label">Content</label>
                    <textarea name="content" class="form-control" rows="6" placeholder="Write your post content..." required></textarea>
                                </div>

                <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" onclick="closeCreatePostModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Post</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <a href="dashboard.php" class="bottom-nav-item">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="streams.php" class="bottom-nav-item">
            <i class="fas fa-video"></i>
            <span>Streams</span>
        </a>
        
        <a href="community.php" class="bottom-nav-item active">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="wallet.php" class="bottom-nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="profile.php" class="bottom-nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </nav>

    <script>
        // Modal functions
        function openCreatePostModal() {
            document.getElementById('createPostModal').style.display = 'block';
        }

        function closeCreatePostModal() {
            document.getElementById('createPostModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('createPostModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCreatePostModal();
            }
        });

        // Filter and sort functions
        function filterByCategory(category) {
            const url = new URL(window.location);
            url.searchParams.set('category', category);
            window.location.href = url.toString();
        }

        function sortPosts(sort) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sort);
            window.location.href = url.toString();
        }

        // Post interaction functions
        function likePost(postId) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="like_post">
                <input type="hidden" name="post_id" value="${postId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function toggleReplies(postId) {
            const section = document.getElementById(`reply-section-${postId}`);
            const isVisible = section.style.display !== 'none';
            
            if (!isVisible) {
                section.style.display = 'block';
                loadReplies(postId);
            } else {
                section.style.display = 'none';
            }
        }

        function toggleReplyForm(postId) {
            const form = document.getElementById(`reply-form-${postId}`);
            const isVisible = form.style.display !== 'none';
            form.style.display = isVisible ? 'none' : 'block';
        }

        function loadReplies(postId) {
            const repliesList = document.getElementById(`replies-list-${postId}`);
            repliesList.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">Loading replies...</p>';
            
            // Load replies via AJAX
            fetch(`api/get-forum-replies.php?post_id=${postId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayReplies(postId, data.replies);
                    } else {
                        repliesList.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No replies yet. Be the first to reply!</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading replies:', error);
                    repliesList.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">Error loading replies. Please try again.</p>';
                });
        }

        function displayReplies(postId, replies) {
            const repliesList = document.getElementById(`replies-list-${postId}`);
            
            if (replies.length === 0) {
                repliesList.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No replies yet. Be the first to reply!</p>';
                return;
            }
            
            // Organize replies into a tree structure
            const replyTree = buildReplyTree(replies);
            
            let html = '';
            replyTree.forEach(reply => {
                html += renderReplyItem(reply, postId, 0);
            });
            
            repliesList.innerHTML = html;
        }

        function buildReplyTree(replies) {
            const replyMap = {};
            const rootReplies = [];
            
            // Create a map of all replies
            replies.forEach(reply => {
                replyMap[reply.id] = { ...reply, children: [] };
            });
            
            // Build the tree structure
            replies.forEach(reply => {
                if (reply.parent_reply_id) {
                    // This is a nested reply
                    if (replyMap[reply.parent_reply_id]) {
                        replyMap[reply.parent_reply_id].children.push(replyMap[reply.id]);
                    }
                } else {
                    // This is a root reply
                    rootReplies.push(replyMap[reply.id]);
                }
            });
            
            return rootReplies;
        }

        function renderReplyItem(reply, postId, depth = 0) {
            const avatar = reply.user.profile_picture ? 
                `<img src="uploads/profiles/${reply.user.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` :
                reply.user.name.charAt(0).toUpperCase();
            
            const date = new Date(reply.created_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit'
            });
            
            const marginLeft = depth * 20;
            const borderLeft = depth > 0 ? 'border-left: 3px solid var(--primary); padding-left: 1rem;' : '';
            
            let html = `
                <div class="reply-item" style="margin-left: ${marginLeft}px; ${borderLeft}">
                    <div class="reply-header">
                        <div class="reply-avatar">
                            ${avatar}
                        </div>
                        <div class="reply-author">${reply.user.name}</div>
                        <div class="reply-date">${date}</div>
                    </div>
                    <div class="reply-content">${reply.content.replace(/\n/g, '<br>')}</div>
                    <div class="reply-actions" style="margin-top: 0.5rem; display: flex; gap: 0.5rem; align-items: center;">
                        <button class="action-btn ${reply.user_liked ? 'liked' : ''}" 
                                onclick="likeReply(${reply.id}, this)" 
                                style="font-size: 0.8rem; padding: 0.25rem 0.5rem;">
                            <i class="fas fa-heart"></i>
                            <span class="like-count">${reply.like_count || 0}</span>
                        </button>
                        <button class="action-btn" 
                                onclick="toggleReplyToReply(${reply.id}, ${postId})" 
                                style="font-size: 0.8rem; padding: 0.25rem 0.5rem;">
                            <i class="fas fa-reply"></i>
                            Reply
                        </button>
                    </div>
                    <div class="reply-to-reply-form" id="reply-to-reply-form-${reply.id}" style="display: none; margin-top: 0.5rem;">
                        <textarea class="reply-input" id="reply-to-reply-input-${reply.id}" 
                                  placeholder="Write your reply..." style="min-height: 60px; font-size: 0.9rem;"></textarea>
                        <div style="margin-top: 0.5rem; display: flex; gap: 0.5rem;">
                            <button class="btn btn-primary" onclick="submitReplyToReply(${reply.id}, ${postId})" 
                                    style="font-size: 0.8rem; padding: 0.25rem 0.75rem;">Post Reply</button>
                            <button class="btn btn-secondary" onclick="toggleReplyToReply(${reply.id}, ${postId})" 
                                    style="font-size: 0.8rem; padding: 0.25rem 0.75rem;">Cancel</button>
                        </div>
                    </div>
            `;
            
            // Render nested replies
            if (reply.children && reply.children.length > 0) {
                html += '<div class="nested-replies" style="margin-top: 0.5rem;">';
                reply.children.forEach(childReply => {
                    html += renderReplyItem(childReply, postId, depth + 1);
                });
                html += '</div>';
            }
            
            html += '</div>';
            return html;
        }

        function sharePost(postId) {
            // Get post data for sharing
            const postCard = document.querySelector(`[data-post-id="${postId}"]`) || 
                           document.querySelector(`.post-card:has(#reply-section-${postId})`);
            
            if (!postCard) {
                alert('Post not found');
                return;
            }
            
            const title = postCard.querySelector('.post-title').textContent;
            const content = postCard.querySelector('.post-content').textContent.substring(0, 100) + '...';
            const url = window.location.href;
            
            // Create share text
            const shareText = `Check out this post: "${title}"\n\n${content}\n\nRead more at: ${url}`;
            
            // Try to use Web Share API if available
            if (navigator.share) {
                navigator.share({
                    title: title,
                    text: content,
                    url: url
                }).catch(err => {
                    fallbackShare(shareText);
                });
            } else {
                fallbackShare(shareText);
            }
        }

        function fallbackShare(shareText) {
            // Create a temporary textarea to copy the share text
            const textarea = document.createElement('textarea');
            textarea.value = shareText;
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                document.execCommand('copy');
                alert('Post link copied to clipboard! You can now share it.');
            } catch (err) {
                // Fallback: show the text for manual copying
                prompt('Copy this text to share the post:', shareText);
            }
            
            document.body.removeChild(textarea);
        }

        function likeReply(replyId, button) {
            const formData = new FormData();
            formData.append('reply_id', replyId);
            
            fetch('api/like-reply.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button appearance
                    if (data.action === 'liked') {
                        button.classList.add('liked');
                    } else {
                        button.classList.remove('liked');
                    }
                    
                    // Update like count
                    const likeCountSpan = button.querySelector('.like-count');
                    likeCountSpan.textContent = data.like_count;
                    
                    // Show success message
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.error || 'Failed to like reply', 'error');
                }
            })
            .catch(error => {
                console.error('Error liking reply:', error);
                showMessage('Error liking reply. Please try again.', 'error');
            });
        }

        function showMessage(message, type) {
            // Create a temporary message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'}`;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.right = '20px';
            messageDiv.style.zIndex = '9999';
            messageDiv.style.minWidth = '300px';
            messageDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}`;
            
            document.body.appendChild(messageDiv);
            
            // Remove after 3 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        function toggleReplyToReply(replyId, postId) {
            const form = document.getElementById(`reply-to-reply-form-${replyId}`);
            const isVisible = form.style.display !== 'none';
            form.style.display = isVisible ? 'none' : 'block';
            
            if (!isVisible) {
                // Focus on the textarea
                const textarea = document.getElementById(`reply-to-reply-input-${replyId}`);
                textarea.focus();
            }
        }

        function submitMainReply(postId) {
            const textarea = document.getElementById(`reply-input-${postId}`);
            const content = textarea.value.trim();
            
            if (!content) {
                showMessage('Please enter a reply', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('post_id', postId);
            formData.append('content', content);
            
            fetch('api/create-reply.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear the form
                    textarea.value = '';
                    toggleReplyForm(postId);
                    
                    // Reload replies to show the new reply
                    loadReplies(postId);
                    
                    // Update reply count
                    const replyCountSpan = document.getElementById(`reply-count-${postId}`);
                    if (replyCountSpan) {
                        const currentCount = parseInt(replyCountSpan.textContent) || 0;
                        replyCountSpan.textContent = currentCount + 1;
                    }
                    
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.error || 'Failed to post reply', 'error');
                }
            })
            .catch(error => {
                console.error('Error posting reply:', error);
                showMessage('Error posting reply. Please try again.', 'error');
            });
        }

        function submitReplyToReply(parentReplyId, postId) {
            const textarea = document.getElementById(`reply-to-reply-input-${parentReplyId}`);
            const content = textarea.value.trim();
            
            if (!content) {
                showMessage('Please enter a reply', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('post_id', postId);
            formData.append('parent_reply_id', parentReplyId);
            formData.append('content', content);
            
            fetch('api/create-reply.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear the form
                    textarea.value = '';
                    toggleReplyToReply(parentReplyId, postId);
                    
                    // Reload replies to show the new reply
                    loadReplies(postId);
                    
                    // Update reply count
                    const replyCountSpan = document.getElementById(`reply-count-${postId}`);
                    if (replyCountSpan) {
                        const currentCount = parseInt(replyCountSpan.textContent) || 0;
                        replyCountSpan.textContent = currentCount + 1;
                    }
                    
                    showMessage(data.message, 'success');
                } else {
                    showMessage(data.error || 'Failed to post reply', 'error');
                }
            })
            .catch(error => {
                console.error('Error posting reply:', error);
                showMessage('Error posting reply. Please try again.', 'error');
            });
        }

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }, 5000);
    </script>
</body>
</html>
