<?php
/**
 * Paystack Wallet Funding Verification for Recite! App
 * Verifies Paystack payment and credits user wallet
 */

require_once '../config/db_config.php';

header('Content-Type: application/json');

// DEBUG: Log all errors and output for troubleshooting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

ob_start(); // Start output buffering to catch any unexpected output

// Require login
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

$userId = $_SESSION['user_id'];
$input = json_decode(file_get_contents('php://input'), true);
$reference = sanitize($input['reference'] ?? '');

if (empty($reference)) {
    echo json_encode(['success' => false, 'message' => 'Payment reference is required']);
    exit;
}

try {
    // Check if payment already processed
    $existingPayment = executeQuery(
        "SELECT id FROM transactions WHERE reference_id = ? AND transaction_type = 'deposit' AND user_id = ?",
        'si',
        [$reference, $userId]
    );
    
    if ($existingPayment->num_rows > 0) {
        echo json_encode(['success' => false, 'message' => 'Payment already processed']);
        exit;
    }
    
    // In development mode, simulate successful payment
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        // Extract amount from reference (format: FUND_USERID_AMOUNT_RANDOM)
        $parts = explode('_', $reference);
        if (count($parts) >= 3 && $parts[0] === 'FUND') {
            $amount = floatval($parts[2]);
            
            if ($amount > 0) {
                $conn = getConnection();
                $conn->autocommit(false);
                
                try {
                    // Update user wallet balance
                    executeQuery(
                        "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
                        'di',
                        [$amount, $userId]
                    );
                    
                    // Record transaction
                    executeQuery(
                        "INSERT INTO transactions (user_id, transaction_type, amount, description, reference_id, status, created_at) VALUES (?, 'deposit', ?, 'Wallet funding via Paystack', ?, 'completed', NOW())",
                        'ids',
                        [$userId, $amount, $reference]
                    );
                    
                    $conn->commit();
                    $conn->autocommit(true);
                    
                    // Get updated balance
                    $newBalance = getUserWalletBalance($userId);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Wallet funded successfully!',
                        'amount' => $amount,
                        'new_balance' => $newBalance
                    ]);
                    exit;
                    
                } catch (Exception $e) {
                    $conn->rollback();
                    $conn->autocommit(true);
                    throw $e;
                }
            }
        }
    } else {
        // Production Paystack verification
        $paystackSecretKey = PAYSTACK_SECRET_KEY;
        
        // Use file_get_contents instead of cURL if cURL is not available
        if (function_exists('curl_init')) {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://api.paystack.co/transaction/verify/" . $reference,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
                CURLOPT_HTTPHEADER => array(
                    "Authorization: Bearer " . $paystackSecretKey,
                    "Cache-Control: no-cache",
                ),
            ));
            
            $response = curl_exec($curl);
            $err = curl_error($curl);
            curl_close($curl);
            
            if ($err) {
                throw new Exception("Payment verification failed: " . $err);
            }
        } else {
            // Fallback to file_get_contents
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        "Authorization: Bearer " . $paystackSecretKey,
                        "Cache-Control: no-cache"
                    ]
                ]
            ]);
            
            $response = file_get_contents("https://api.paystack.co/transaction/verify/" . $reference, false, $context);
            
            if ($response === false) {
                throw new Exception("Payment verification failed");
            }
        }
        
        $result = json_decode($response, true);
        
        if ($result['status'] && $result['data']['status'] === 'success') {
            $amount = $result['data']['amount'] / 100; // Paystack amounts are in kobo
            $customerEmail = $result['data']['customer']['email'];
            
            // Verify user email matches
            $user = getUserById($userId);
            if ($user['email'] !== $customerEmail) {
                throw new Exception("Payment email doesn't match account email");
            }
            
            $conn = getConnection();
            $conn->autocommit(false);
            
            try {
                // Update user wallet balance
                executeQuery(
                    "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?",
                    'di',
                    [$amount, $userId]
                );
                
                // Record transaction
                executeQuery(
                    "INSERT INTO transactions (user_id, transaction_type, amount, description, reference_id, status, created_at) VALUES (?, 'deposit', ?, 'Wallet funding via Paystack', ?, 'completed', NOW())",
                    'ids',
                    [$userId, $amount, $reference]
                );
                
                $conn->commit();
                $conn->autocommit(true);
                
                // Get updated balance
                $newBalance = getUserWalletBalance($userId);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Wallet funded successfully!',
                    'amount' => $amount,
                    'new_balance' => $newBalance
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                $conn->autocommit(true);
                throw $e;
            }
        } else {
            throw new Exception("Payment verification failed");
        }
    }
    
} catch (Exception $e) {
    error_log("Paystack funding error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getUserWalletBalance($userId) {
    $result = executeQuery(
        "SELECT wallet_balance FROM users WHERE id = ?",
        'i',
        [$userId]
    );
    $user = $result->fetch_assoc();
    return floatval($user['wallet_balance'] ?? 0);
}

$output = ob_get_clean();
if (!empty($output)) {
    echo json_encode([
        'success' => false,
        'message' => 'Unexpected output: ' . $output
    ]);
}
?> 