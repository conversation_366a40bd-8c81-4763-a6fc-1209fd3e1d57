<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard | EduSphere LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --secondary: #10b981;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --accent: #f59e0b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: var(--dark);
        }
        
        .font-heading {
            font-family: 'Plus Jakarta Sans', sans-serif;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
        }
        
        .sidebar {
            transition: all 0.3s ease;
        }
        
        .course-card:hover .course-image {
            transform: scale(1.05);
        }
        
        .progress-ring {
            transition: stroke-dashoffset 0.5s ease;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }
        
        .floating {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="antialiased bg-slate-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-white w-64 border-r border-slate-200 flex-shrink-0 hidden md:block">
            <div class="flex items-center justify-center h-16 px-4 border-b border-slate-200">
                <a href="#" class="flex items-center">
                    <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <span class="font-heading text-xl font-bold text-slate-800">EduSphere</span>
                </a>
            </div>
            
            <div class="p-4">
                <!-- User Profile -->
                <div class="flex items-center mb-6 p-3 rounded-lg bg-slate-50">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <h4 class="font-medium text-slate-800">Sarah Johnson</h4>
                        <p class="text-xs text-slate-500">Student</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-indigo-50 text-indigo-700">
                        <i class="fas fa-home mr-3 text-indigo-600"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-book-open mr-3 text-slate-500"></i>
                        My Courses
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-calendar-alt mr-3 text-slate-500"></i>
                        Calendar
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tasks mr-3 text-slate-500"></i>
                        Assignments
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chart-line mr-3 text-slate-500"></i>
                        Progress
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-comments mr-3 text-slate-500"></i>
                        Discussions
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-cog mr-3 text-slate-500"></i>
                        Settings
                    </a>
                </nav>
                
                <!-- Bottom Links -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-200">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-question-circle mr-3 text-slate-500"></i>
                        Help Center
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-sign-out-alt mr-3 text-slate-500"></i>
                        Sign Out
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Mobile sidebar -->
        <div class="sidebar md:hidden fixed inset-0 z-40 bg-white transform -translate-x-full transition-transform duration-300 ease-in-out" id="mobile-sidebar">
            <div class="flex items-center justify-between h-16 px-4 border-b border-slate-200">
                <a href="#" class="flex items-center">
                    <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                        <i class="fas fa-graduation-cap text-white text-xl"></i>
                    </div>
                    <span class="font-heading text-xl font-bold text-slate-800">EduSphere</span>
                </a>
                <button id="close-sidebar" class="text-slate-500 hover:text-slate-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <div class="p-4 overflow-y-auto h-[calc(100%-4rem)]">
                <!-- User Profile -->
                <div class="flex items-center mb-6 p-3 rounded-lg bg-slate-50">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <h4 class="font-medium text-slate-800">Sarah Johnson</h4>
                        <p class="text-xs text-slate-500">Student</p>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="space-y-1">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-indigo-50 text-indigo-700">
                        <i class="fas fa-home mr-3 text-indigo-600"></i>
                        Dashboard
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-book-open mr-3 text-slate-500"></i>
                        My Courses
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-calendar-alt mr-3 text-slate-500"></i>
                        Calendar
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-tasks mr-3 text-slate-500"></i>
                        Assignments
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-chart-line mr-3 text-slate-500"></i>
                        Progress
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-comments mr-3 text-slate-500"></i>
                        Discussions
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-cog mr-3 text-slate-500"></i>
                        Settings
                    </a>
                </nav>
                
                <!-- Bottom Links -->
                <div class="mt-4 pt-4 border-t border-slate-200">
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-question-circle mr-3 text-slate-500"></i>
                        Help Center
                    </a>
                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-slate-700 hover:bg-slate-100">
                        <i class="fas fa-sign-out-alt mr-3 text-slate-500"></i>
                        Sign Out
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
            <header class="bg-white border-b border-slate-200">
                <div class="flex items-center justify-between h-16 px-4">
                    <div class="flex items-center">
                        <button id="open-sidebar" class="md:hidden text-slate-500 hover:text-slate-700 mr-2">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="font-heading text-xl font-bold text-slate-800">Dashboard</h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100">
                            <i class="fas fa-bell text-xl"></i>
                        </button>
                        <button class="p-1 text-slate-500 hover:text-slate-700 rounded-full hover:bg-slate-100">
                            <i class="fas fa-envelope text-xl"></i>
                        </button>
                        <div class="relative">
                            <button class="flex items-center focus:outline-none" id="user-menu-button">
                                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-8 h-8 rounded-full">
                                <span class="ml-2 text-sm font-medium text-slate-700 hidden md:inline">Sarah J.</span>
                                <i class="fas fa-chevron-down ml-1 text-xs text-slate-500 hidden md:inline"></i>
                            </button>
                            
                            <!-- Dropdown menu -->
                            <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50" id="user-menu">
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                                <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Sign out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto p-4 md:p-6">
                <!-- Welcome Banner -->
                <div class="gradient-bg rounded-xl p-6 text-white mb-6">
                    <div class="flex flex-col md:flex-row items-center justify-between">
                        <div class="mb-4 md:mb-0">
                            <h2 class="font-heading text-2xl font-bold mb-2">Welcome back, Sarah!</h2>
                            <p class="opacity-90">You have 3 courses in progress. Keep up the good work!</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="https://images.unsplash.com/photo-1581726707445-75cbe4efc586?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Learning" class="w-32 h-32 object-contain floating">
                        </div>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-indigo-100 text-indigo-600 mr-4">
                                <i class="fas fa-book-open text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Active Courses</p>
                                <h3 class="text-2xl font-bold text-slate-800">5</h3>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-green-100 text-green-600 mr-4">
                                <i class="fas fa-check-circle text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Completed</p>
                                <h3 class="text-2xl font-bold text-slate-800">12</h3>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-amber-100 text-amber-600 mr-4">
                                <i class="fas fa-tasks text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Pending Tasks</p>
                                <h3 class="text-2xl font-bold text-slate-800">3</h3>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-blue-100 text-blue-600 mr-4">
                                <i class="fas fa-certificate text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-slate-500">Certificates</p>
                                <h3 class="text-2xl font-bold text-slate-800">7</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Courses in Progress -->
                <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm mb-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="font-heading text-xl font-bold text-slate-800">Courses in Progress</h2>
                        <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Course 1 -->
                        <div class="course-card bg-white rounded-lg border border-slate-200 overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-40 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1499750310107-5fef28a66643?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image transition duration-500">
                                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                    <h3 class="text-white font-medium">Data Science Fundamentals</h3>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xs font-medium px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full">In Progress</span>
                                    <div class="text-xs text-slate-500">4/12 Lessons</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: 33%"></div>
                                    </div>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>8h remaining</span>
                                    </div>
                                    <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">Continue</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 2 -->
                        <div class="course-card bg-white rounded-lg border border-slate-200 overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-40 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image transition duration-500">
                                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                    <h3 class="text-white font-medium">Web Development Bootcamp</h3>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xs font-medium px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full">In Progress</span>
                                    <div class="text-xs text-slate-500">7/24 Lessons</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: 29%"></div>
                                    </div>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>15h remaining</span>
                                    </div>
                                    <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">Continue</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Course 3 -->
                        <div class="course-card bg-white rounded-lg border border-slate-200 overflow-hidden hover:shadow-md transition duration-300">
                            <div class="relative h-40 overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1605379399642-870262d3d051?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80" alt="Course" class="w-full h-full object-cover course-image transition duration-500">
                                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                    <h3 class="text-white font-medium">Digital Marketing Mastery</h3>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xs font-medium px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full">In Progress</span>
                                    <div class="text-xs text-slate-500">3/10 Lessons</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: 30%"></div>
                                    </div>
                                </div>
                                
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="far fa-clock mr-1"></i>
                                        <span>5h remaining</span>
                                    </div>
                                    <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">Continue</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Upcoming Deadlines & Recommended Courses -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Upcoming Deadlines -->
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="font-heading text-xl font-bold text-slate-800">Upcoming Deadlines</h2>
                            <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Deadline 1 -->
                            <div class="flex items-start p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0 mt-1">
                                    <div class="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center text-red-600">
                                        <i class="fas fa-exclamation"></i>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h3 class="font-medium text-slate-800">Assignment: Data Analysis Project</h3>
                                    <p class="text-sm text-slate-500">Due tomorrow at 11:59 PM</p>
                                    <p class="text-xs text-slate-400 mt-1">Data Science Fundamentals</p>
                                </div>
                            </div>
                            
                            <!-- Deadline 2 -->
                            <div class="flex items-start p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0 mt-1">
                                    <div class="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center text-amber-600">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h3 class="font-medium text-slate-800">Quiz: React Components</h3>
                                    <p class="text-sm text-slate-500">Due in 3 days</p>
                                    <p class="text-xs text-slate-400 mt-1">Web Development Bootcamp</p>
                                </div>
                            </div>
                            
                            <!-- Deadline 3 -->
                            <div class="flex items-start p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0 mt-1">
                                    <div class="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center text-blue-600">
                                        <i class="fas fa-users"></i>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h3 class="font-medium text-slate-800">Group Project Submission</h3>
                                    <p class="text-sm text-slate-500">Due in 1 week</p>
                                    <p class="text-xs text-slate-400 mt-1">Digital Marketing Mastery</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommended Courses -->
                    <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="font-heading text-xl font-bold text-slate-800">Recommended For You</h2>
                            <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">Browse All</a>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Course 1 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <img src="https://images.unsplash.com/photo-1542626991-cbc4e32524cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Course" class="w-16 h-16 rounded-lg object-cover">
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-medium text-slate-800">Python for Beginners</h3>
                                    <div class="flex items-center text-xs text-slate-500 mb-1">
                                        <i class="fas fa-star text-amber-400 mr-1"></i>
                                        <span>4.8 (1.2k reviews)</span>
                                    </div>
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="fas fa-user-graduate mr-1"></i>
                                        <span>Beginner • 15h</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course 2 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <img src="https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Course" class="w-16 h-16 rounded-lg object-cover">
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-medium text-slate-800">UX/UI Design Principles</h3>
                                    <div class="flex items-center text-xs text-slate-500 mb-1">
                                        <i class="fas fa-star text-amber-400 mr-1"></i>
                                        <span>4.9 (890 reviews)</span>
                                    </div>
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="fas fa-user-graduate mr-1"></i>
                                        <span>Intermediate • 10h</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course 3 -->
                            <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                                <div class="flex-shrink-0">
                                    <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=200&q=80" alt="Course" class="w-16 h-16 rounded-lg object-cover">
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-medium text-slate-800">Business Analytics</h3>
                                    <div class="flex items-center text-xs text-slate-500 mb-1">
                                        <i class="fas fa-star text-amber-400 mr-1"></i>
                                        <span>4.7 (1.5k reviews)</span>
                                    </div>
                                    <div class="flex items-center text-xs text-slate-500">
                                        <i class="fas fa-user-graduate mr-1"></i>
                                        <span>Advanced • 20h</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="bg-white rounded-xl p-6 border border-slate-200 shadow-sm">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="font-heading text-xl font-bold text-slate-800">Recent Activity</h2>
                        <a href="#" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium">View All</a>
                    </div>
                    
                    <div class="space-y-4">
                        <!-- Activity 1 -->
                        <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-slate-700">
                                    <span class="font-medium">You completed</span> "Introduction to Data Visualization" lesson
                                </p>
                                <p class="text-xs text-slate-400 mt-1">2 hours ago • Data Science Fundamentals</p>
                            </div>
                        </div>
                        
                        <!-- Activity 2 -->
                        <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                    <i class="fas fa-comment"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-slate-700">
                                    <span class="font-medium">You posted</span> in "React State Management" discussion
                                </p>
                                <p class="text-xs text-slate-400 mt-1">5 hours ago • Web Development Bootcamp</p>
                            </div>
                        </div>
                        
                        <!-- Activity 3 -->
                        <div class="flex p-3 rounded-lg hover:bg-slate-50 transition">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                                    <i class="fas fa-certificate"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-slate-700">
                                    <span class="font-medium">You earned</span> a certificate for "JavaScript Basics"
                                </p>
                                <p class="text-xs text-slate-400 mt-1">1 day ago • JavaScript Fundamentals</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Mobile sidebar toggle
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const openSidebarBtn = document.getElementById('open-sidebar');
        const closeSidebarBtn = document.getElementById('close-sidebar');
        
        openSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.remove('-translate-x-full');
        });
        
        closeSidebarBtn.addEventListener('click', () => {
            mobileSidebar.classList.add('-translate-x-full');
        });
        
        // User menu toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');
        
        userMenuButton.addEventListener('click', () => {
            userMenu.classList.toggle('hidden');
        });
        
        // Close user menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!userMenuButton.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>