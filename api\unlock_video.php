<?php
/**
 * Video Unlock API Endpoint
 * Handles video unlock transactions with 3 Naira payment split
 * 1 Naira to creator, 2 Naira to admin
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/db_config.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function unlockVideo($userId, $recordingId, $recordingType) {
    $conn = getConnection();
    
    if (!$conn) {
        return ['success' => false, 'message' => 'Database connection failed'];
    }

    try {
        // Start transaction
        $conn->begin_transaction();

        // Get user's wallet balance
        $userStmt = $conn->prepare("SELECT wallet_balance FROM users WHERE id = ?");
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $user = $userResult->fetch_assoc();
        $userStmt->close();

        if (!$user) {
            $conn->rollback();
            return ['success' => false, 'message' => 'User not found'];
        }

        if ($user['wallet_balance'] < 3.00) {
            $conn->rollback();
            return ['success' => false, 'message' => 'Insufficient wallet balance. Required: ₦3.00'];
        }

        // Check if already unlocked
        $unlockStmt = $conn->prepare("SELECT id FROM video_unlocks WHERE user_id = ? AND recording_id = ? AND recording_type = ?");
        $unlockStmt->bind_param("iis", $userId, $recordingId, $recordingType);
        $unlockStmt->execute();
        $unlockResult = $unlockStmt->get_result();
        $unlockStmt->close();

        if ($unlockResult->num_rows > 0) {
            $conn->rollback();
            return ['success' => false, 'message' => 'Video already unlocked'];
        }

        // Get recording details and creator
        $tableName = '';
        switch ($recordingType) {
            case 'video':
                $tableName = 'videos';
                break;
            case 'screen_record':
                $tableName = 'screen_records';
                break;
            case 'mirror_record':
                $tableName = 'mirror_recordings';
                break;
            default:
                $conn->rollback();
                return ['success' => false, 'message' => 'Invalid recording type'];
        }

        $recordingStmt = $conn->prepare("SELECT user_id, unlock_price FROM $tableName WHERE id = ?");
        $recordingStmt->bind_param("i", $recordingId);
        $recordingStmt->execute();
        $recordingResult = $recordingStmt->get_result();
        $recording = $recordingResult->fetch_assoc();
        $recordingStmt->close();

        if (!$recording) {
            $conn->rollback();
            return ['success' => false, 'message' => 'Recording not found'];
        }

        $creatorId = $recording['user_id'];
        $unlockPrice = $recording['unlock_price'];
        $creatorAmount = 1.00; // 1 Naira to creator
        $adminAmount = 2.00;   // 2 Naira to admin

        // Deduct from user's wallet
        $updateUserStmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?");
        $updateUserStmt->bind_param("di", $unlockPrice, $userId);
        $updateUserStmt->execute();
        $updateUserStmt->close();

        // Add to creator's wallet
        $updateCreatorStmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
        $updateCreatorStmt->bind_param("di", $creatorAmount, $creatorId);
        $updateCreatorStmt->execute();
        $updateCreatorStmt->close();

        // Create wallet transaction for user (deduction)
        $userTransactionStmt = $conn->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'video_unlock', ?, ?, 'completed')");
        $userDescription = "Unlocked $recordingType #$recordingId for ₦$unlockPrice";
        $userTransactionStmt->bind_param("ids", $userId, $unlockPrice, $userDescription);
        $userTransactionStmt->execute();
        $userTransactionId = $conn->insert_id;
        $userTransactionStmt->close();

        // Create wallet transaction for creator (earnings)
        $creatorTransactionStmt = $conn->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, status) VALUES (?, 'creator_earnings', ?, ?, 'completed')");
        $creatorDescription = "Earned ₦$creatorAmount from video unlock by user #$userId";
        $creatorTransactionStmt->bind_param("ids", $creatorId, $creatorAmount, $creatorDescription);
        $creatorTransactionStmt->execute();
        $creatorTransactionStmt->close();

        // Create admin earnings record
        $adminEarningsStmt = $conn->prepare("INSERT INTO admin_earnings (transaction_id, user_id, recording_id, recording_type, amount) VALUES (?, ?, ?, ?, ?)");
        $adminEarningsStmt->bind_param("iiisd", $userTransactionId, $userId, $recordingId, $recordingType, $adminAmount);
        $adminEarningsStmt->execute();
        $adminEarningsStmt->close();

        // Create video unlock record
        $unlockRecordStmt = $conn->prepare("INSERT INTO video_unlocks (user_id, recording_id, recording_type, unlock_price, creator_amount, admin_amount, creator_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'completed')");
        $unlockRecordStmt->bind_param("iisdddi", $userId, $recordingId, $recordingType, $unlockPrice, $creatorAmount, $adminAmount, $creatorId);
        $unlockRecordStmt->execute();
        $unlockRecordStmt->close();

        // Update view count
        $viewCountStmt = $conn->prepare("UPDATE $tableName SET view_count = view_count + 1 WHERE id = ?");
        $viewCountStmt->bind_param("i", $recordingId);
        $viewCountStmt->execute();
        $viewCountStmt->close();

        // Commit transaction
        $conn->commit();

        return [
            'success' => true,
            'message' => 'Video unlocked successfully',
            'data' => [
                'unlock_price' => $unlockPrice,
                'creator_amount' => $creatorAmount,
                'admin_amount' => $adminAmount,
                'new_balance' => $user['wallet_balance'] - $unlockPrice
            ]
        ];

    } catch (Exception $e) {
        $conn->rollback();
        return ['success' => false, 'message' => 'Transaction failed: ' . $e->getMessage()];
    } finally {
        $conn->close();
    }
}

function checkVideoUnlockStatus($userId, $recordingId, $recordingType) {
    $conn = getConnection();
    
    if (!$conn) {
        return ['success' => false, 'message' => 'Database connection failed'];
    }

    try {
        $stmt = $conn->prepare("SELECT id, created_at FROM video_unlocks WHERE user_id = ? AND recording_id = ? AND recording_type = ?");
        $stmt->bind_param("iis", $userId, $recordingId, $recordingType);
        $stmt->execute();
        $result = $stmt->get_result();
        $unlock = $result->fetch_assoc();
        $stmt->close();

        return [
            'success' => true,
            'is_unlocked' => $result->num_rows > 0,
            'unlock_data' => $unlock
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Check failed: ' . $e->getMessage()];
    } finally {
        $conn->close();
    }
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['action'])) {
        echo json_encode(['success' => false, 'message' => 'Action required']);
        exit();
    }

    switch ($input['action']) {
        case 'unlock':
            if (!isset($input['user_id']) || !isset($input['recording_id']) || !isset($input['recording_type'])) {
                echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
                exit();
            }
            
            $result = unlockVideo($input['user_id'], $input['recording_id'], $input['recording_type']);
            echo json_encode($result);
            break;

        case 'check_status':
            if (!isset($input['user_id']) || !isset($input['recording_id']) || !isset($input['recording_type'])) {
                echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
                exit();
            }
            
            $result = checkVideoUnlockStatus($input['user_id'], $input['recording_id'], $input['recording_type']);
            echo json_encode($result);
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}
?> 