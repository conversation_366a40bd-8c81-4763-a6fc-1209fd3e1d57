/* Clean Mobile-First Design - Same as Dashboard */
:root {
    --primary: #1a5f3f;
    --primary-light: #2d7a5a;
    --secondary: #f8f9fa;
    --accent: #28a745;
    --text-dark: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-dark);
    line-height: 1.6;
    padding-bottom: 80px;
}

/* Header */
.header {
    background: var(--primary);
    color: white;
    padding: 1rem;
    text-align: center;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

/* Main Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: none;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    background: var(--primary);
    color: white;
    padding: 1rem;
    border-bottom: none;
}

.card-body {
    padding: 1.5rem;
}

/* Wallet Balance */
.balance-card {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    color: white;
    text-align: center;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.balance-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Action Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn-outline-danger {
    border: 2px solid var(--primary);
    color: var(--primary);
    background: transparent;
}

.btn-outline-danger:hover {
    background: var(--primary);
    color: white;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-light);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: var(--accent);
    color: white;
}

/* Points Exchange */
.points-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.exchange-rate {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Transaction History */
.transaction-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.transaction-info small {
    color: var(--text-muted);
}

.transaction-amount {
    font-weight: 600;
}

.transaction-amount.positive {
    color: var(--accent);
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Forms */
.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(26, 95, 63, 0.25);
}

.input-group-text {
    background: var(--secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px 0 0 8px;
}

/* Alerts */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: space-around;
    padding: 0.5rem;
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #666;
    padding: 0.5rem;
    min-width: 60px;
}

.nav-item.active {
    color: #1a5f3f;
}

.nav-item i {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.nav-item span {
    font-size: 0.7rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}
