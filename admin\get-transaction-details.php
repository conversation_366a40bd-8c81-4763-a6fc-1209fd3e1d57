<?php
session_start();
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Transaction ID required']);
    exit();
}

$transactionId = (int)$_GET['id'];

$conn = getConnection();
$sql = "SELECT 
            vu.*,
            u.full_name as user_name,
            u.email as user_email,
            u.phone as user_phone,
            c.full_name as creator_name,
            c.email as creator_email,
            c.phone as creator_phone
        FROM video_unlocks vu
        LEFT JOIN users u ON vu.user_id = u.id
        LEFT JOIN users c ON vu.creator_id = c.id
        WHERE vu.id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $transactionId);
$stmt->execute();
$result = $stmt->get_result();
$transaction = $result->fetch_assoc();
$stmt->close();

if (!$transaction) {
    echo json_encode(['success' => false, 'message' => 'Transaction not found']);
    exit();
}

// Get related wallet transactions
$wallet_sql = "SELECT * FROM wallet_transactions 
               WHERE user_id IN (?, ?) 
               AND transaction_type IN ('video_unlock', 'creator_earnings', 'admin_earnings')
               AND created_at BETWEEN ? AND DATE_ADD(?, INTERVAL 1 DAY)
               ORDER BY created_at DESC";

$stmt = $conn->prepare($wallet_sql);
$stmt->bind_param("iiss", $transaction['user_id'], $transaction['creator_id'], $transaction['created_at'], $transaction['created_at']);
$stmt->execute();
$wallet_transactions = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$stmt->close();

$conn->close();

// Generate HTML for modal
$html = '
<div class="row">
    <div class="col-md-6">
        <h6>Transaction Information</h6>
        <table class="table table-sm">
            <tr><td><strong>Transaction ID:</strong></td><td>#' . $transaction['id'] . '</td></tr>
            <tr><td><strong>Status:</strong></td><td><span class="badge bg-' . ($transaction['status'] === 'completed' ? 'success' : ($transaction['status'] === 'pending' ? 'warning' : 'danger')) . '">' . ucfirst($transaction['status']) . '</span></td></tr>
            <tr><td><strong>Recording Type:</strong></td><td>' . ucfirst(str_replace('_', ' ', $transaction['recording_type'])) . '</td></tr>
            <tr><td><strong>Recording ID:</strong></td><td>' . $transaction['recording_id'] . '</td></tr>
            <tr><td><strong>Unlock Price:</strong></td><td>₦' . number_format($transaction['unlock_price'], 2) . '</td></tr>
            <tr><td><strong>Creator Amount:</strong></td><td>₦' . number_format($transaction['creator_amount'], 2) . '</td></tr>
            <tr><td><strong>Admin Amount:</strong></td><td>₦' . number_format($transaction['admin_amount'], 2) . '</td></tr>
            <tr><td><strong>Created:</strong></td><td>' . date('M j, Y H:i:s', strtotime($transaction['created_at'])) . '</td></tr>
        </table>
    </div>
    <div class="col-md-6">
        <h6>User Information</h6>
        <table class="table table-sm">
            <tr><td><strong>Name:</strong></td><td>' . htmlspecialchars($transaction['user_name']) . '</td></tr>
            <tr><td><strong>Email:</strong></td><td>' . htmlspecialchars($transaction['user_email']) . '</td></tr>
            <tr><td><strong>Phone:</strong></td><td>' . htmlspecialchars($transaction['user_phone']) . '</td></tr>
            <tr><td><strong>User ID:</strong></td><td>' . $transaction['user_id'] . '</td></tr>
        </table>
        
        <h6>Creator Information</h6>
        <table class="table table-sm">
            <tr><td><strong>Name:</strong></td><td>' . htmlspecialchars($transaction['creator_name']) . '</td></tr>
            <tr><td><strong>Email:</strong></td><td>' . htmlspecialchars($transaction['creator_email']) . '</td></tr>
            <tr><td><strong>Phone:</strong></td><td>' . htmlspecialchars($transaction['creator_phone']) . '</td></tr>
            <tr><td><strong>Creator ID:</strong></td><td>' . $transaction['creator_id'] . '</td></tr>
        </table>
    </div>
</div>';

if (!empty($wallet_transactions)) {
    $html .= '
    <div class="row mt-3">
        <div class="col-12">
            <h6>Related Wallet Transactions</h6>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>';
    
    foreach ($wallet_transactions as $wt) {
        $user_name = ($wt['user_id'] == $transaction['user_id']) ? $transaction['user_name'] : $transaction['creator_name'];
        $html .= '
                        <tr>
                            <td><span class="badge bg-info">' . ucfirst(str_replace('_', ' ', $wt['transaction_type'])) . '</span></td>
                            <td>' . htmlspecialchars($user_name) . '</td>
                            <td>₦' . number_format($wt['amount'], 2) . '</td>
                            <td>' . htmlspecialchars($wt['description']) . '</td>
                            <td><span class="badge bg-' . ($wt['status'] === 'completed' ? 'success' : 'warning') . '">' . ucfirst($wt['status']) . '</span></td>
                            <td>' . date('M j, Y H:i', strtotime($wt['created_at'])) . '</td>
                        </tr>';
    }
    
    $html .= '
                    </tbody>
                </table>
            </div>
        </div>
    </div>';
}

echo json_encode(['success' => true, 'html' => $html]);
?> 