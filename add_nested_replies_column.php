<?php
/**
 * Add parent_reply_id column to forum_replies table for nested replies
 */

require_once 'config/db_config.php';

$conn = getConnection();

if (!$conn) {
    die("Database connection failed");
}

echo "Adding nested replies support...\n";

try {
    // Add parent_reply_id column to forum_replies table
    $sql = "ALTER TABLE forum_replies ADD COLUMN parent_reply_id INT NULL DEFAULT NULL AFTER user_id";
    
    if ($conn->query($sql)) {
        echo "✅ Added parent_reply_id column to forum_replies table\n";
    } else {
        echo "ℹ️ parent_reply_id column already exists or error occurred\n";
    }

    // Add index for better performance
    $sql = "ALTER TABLE forum_replies ADD INDEX idx_parent_reply (parent_reply_id)";
    
    if ($conn->query($sql)) {
        echo "✅ Added index for parent_reply_id\n";
    } else {
        echo "ℹ️ Index already exists or error occurred\n";
    }

    echo "\n🎉 Nested replies support added successfully!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 