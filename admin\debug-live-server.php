<?php
/**
 * Debug script for live server issues
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Live Server Debug - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .debug-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='debug-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-server me-2'></i>Live Server Debug</h1>
                        <p class='text-muted'>Diagnosing issues with users and payouts pages</p>
                    </div>";

$tests = [];
$errors = [];

// Test 1: PHP Version and Extensions
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-code me-2'></i>Test 1: PHP Environment</h6>";

echo "<p class='mb-0'>✅ PHP Version: " . PHP_VERSION . "</p>";
echo "<p class='mb-0'>✅ MySQLi Extension: " . (extension_loaded('mysqli') ? 'Available' : 'Missing') . "</p>";
echo "<p class='mb-0'>✅ Session Extension: " . (extension_loaded('session') ? 'Available' : 'Missing') . "</p>";
echo "<p class='mb-0'>✅ Memory Limit: " . ini_get('memory_limit') . "</p>";
echo "<p class='mb-0'>✅ Max Execution Time: " . ini_get('max_execution_time') . "s</p>";

echo "</div>";

// Test 2: Database Connection
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-database me-2'></i>Test 2: Database Connection</h6>";

try {
    $conn = getConnection();
    if ($conn) {
        echo "<p class='mb-0'>✅ Database connection successful</p>";
        echo "<p class='mb-0'>✅ Server Info: " . $conn->server_info . "</p>";
        $tests['database'] = true;
    } else {
        echo "<p class='mb-0'>❌ Database connection failed</p>";
        $tests['database'] = false;
        $errors[] = "Database connection failed";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $tests['database'] = false;
    $errors[] = $e->getMessage();
}

echo "</div>";

// Test 3: Required Tables
echo "<div class='test-result " . ($tests['database'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-table me-2'></i>Test 3: Required Tables</h6>";

if ($tests['database']) {
    $requiredTables = ['users', 'withdrawal_requests', 'admins'];
    $tablesExist = 0;
    
    foreach ($requiredTables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p class='mb-0'>✅ Table '$table' exists</p>";
            $tablesExist++;
        } else {
            echo "<p class='mb-0'>❌ Table '$table' missing</p>";
            $errors[] = "Table $table missing";
        }
    }
    
    $tests['tables'] = ($tablesExist == count($requiredTables));
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database failed)</p>";
    $tests['tables'] = false;
}

echo "</div>";

// Test 4: Data Counts
echo "<div class='test-result " . ($tests['database'] && $tests['tables'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-chart-bar me-2'></i>Test 4: Data Counts</h6>";

if ($tests['database'] && $tests['tables']) {
    try {
        // Count users
        $userCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Users: $userCount records</p>";
        
        // Count withdrawal requests
        $withdrawalCount = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Withdrawal Requests: $withdrawalCount records</p>";
        
        // Count admins
        $adminCount = $conn->query("SELECT COUNT(*) as count FROM admins")->fetch_assoc()['count'];
        echo "<p class='mb-0'>✅ Admins: $adminCount records</p>";
        
        $tests['data'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Data query error: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['data'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database/tables failed)</p>";
    $tests['data'] = false;
}

echo "</div>";

// Test 5: File Permissions
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-lock me-2'></i>Test 5: File Permissions</h6>";

$files = ['users.php', 'payouts.php', '../config/db_config.php'];
$permissionsOk = 0;

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? 'Readable' : 'Not Readable';
        echo "<p class='mb-0'>✅ $file - $readable</p>";
        $permissionsOk++;
    } else {
        echo "<p class='mb-0'>❌ $file - File not found</p>";
        $errors[] = "$file not found";
    }
}

$tests['permissions'] = ($permissionsOk == count($files));

echo "</div>";

// Test 6: Session Status
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-user-shield me-2'></i>Test 6: Session Status</h6>";

echo "<p class='mb-0'>✅ Session Status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "<p class='mb-0'>✅ Admin Logged In: " . (isAdmin() ? 'Yes' : 'No') . "</p>";
echo "<p class='mb-0'>✅ Admin Username: " . ($_SESSION['admin_username'] ?? 'Not Set') . "</p>";

echo "</div>";

// Test 7: Error Logs
echo "<div class='test-result test-warning'>
        <h6><i class='fas fa-exclamation-triangle me-2'></i>Test 7: Recent Errors</h6>";

$errorLogFile = '../logs/php_errors.log';
if (file_exists($errorLogFile)) {
    $errorLines = file($errorLogFile);
    $recentErrors = array_slice($errorLines, -10); // Last 10 lines
    
    if (!empty($recentErrors)) {
        echo "<p class='mb-2'>Recent error log entries:</p>";
        foreach ($recentErrors as $line) {
            echo "<small class='text-muted'>" . htmlspecialchars(trim($line)) . "</small><br>";
        }
    } else {
        echo "<p class='mb-0'>✅ No recent errors in log file</p>";
    }
} else {
    echo "<p class='mb-0'>⚠️ Error log file not found</p>";
}

echo "</div>";

// Close database connection
if (isset($conn) && $conn) {
    $conn->close();
}

// Summary
$totalTests = count($tests);
$passedTests = count(array_filter($tests));

if (empty($errors)) {
    echo "<div class='alert alert-success'>
            <h5><i class='fas fa-check-circle me-2'></i>All Tests Passed! ($passedTests/$totalTests)</h5>
            <p class='mb-3'>The server environment appears to be working correctly.</p>
            <div class='text-center'>
                <a href='users.php' class='btn btn-success btn-lg me-2'>
                    <i class='fas fa-users me-2'></i>Test Users Page
                </a>
                <a href='payouts.php' class='btn btn-warning btn-lg'>
                    <i class='fas fa-money-bill-wave me-2'></i>Test Payouts Page
                </a>
            </div>
          </div>";
} else {
    echo "<div class='alert alert-danger'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Issues Found ($passedTests/$totalTests tests passed)</h5>
            <h6>Issues to fix:</h6>
            <ul class='mb-3'>";
    
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    
    echo "            </ul>
            <div class='text-center'>
                <a href='dashboard.php' class='btn btn-secondary btn-lg'>
                    <i class='fas fa-tachometer-alt me-2'></i>Back to Dashboard
                </a>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            Run this diagnostic whenever you have issues on the live server.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
