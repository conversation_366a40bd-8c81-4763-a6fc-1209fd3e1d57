<?php
/**
 * Production Admin Login Fix for universalreciters.name.ng
 * This script will help diagnose and fix admin login issues in production
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set error log path
$errorLogPath = __DIR__ . '/../logs/';
if (!is_dir($errorLogPath)) {
    mkdir($errorLogPath, 0755, true);
}
ini_set('error_log', $errorLogPath . 'admin_fix.log');

$step = $_GET['step'] ?? 1;
$results = [];
$errors = [];

// Step 1: Check basic configuration
if ($step >= 1) {
    try {
        require_once '../config/db_config.php';
        $results['config_loaded'] = true;
    } catch (Exception $e) {
        $errors[] = "Config loading failed: " . $e->getMessage();
        $results['config_loaded'] = false;
    }
}

// Step 2: Test database connection
if ($step >= 2 && $results['config_loaded']) {
    try {
        $conn = getConnection();
        $results['db_connected'] = true;
        $results['db_info'] = [
            'host' => DB_HOST,
            'database' => DB_NAME,
            'user' => DB_USER
        ];
    } catch (Exception $e) {
        $errors[] = "Database connection failed: " . $e->getMessage();
        $results['db_connected'] = false;
    }
}

// Step 3: Create/verify admin table
if ($step >= 3 && $results['db_connected']) {
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) DEFAULT NULL,
                full_name VARCHAR(100) DEFAULT NULL,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        if ($conn->query($createTableQuery)) {
            $results['admin_table_created'] = true;
            
            // Check if table exists and get count
            $result = $conn->query("SELECT COUNT(*) as count FROM admins");
            $count = $result->fetch_assoc()['count'];
            $results['admin_count'] = $count;
        } else {
            throw new Exception("Failed to create admin table: " . $conn->error);
        }
    } catch (Exception $e) {
        $errors[] = "Admin table creation failed: " . $e->getMessage();
        $results['admin_table_created'] = false;
    }
}

// Step 4: Create default admin user
if ($step >= 4 && $results['admin_table_created']) {
    try {
        $username = 'admin';
        $password = '1@3Usazladan';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $conn->prepare("
            INSERT INTO admins (username, password) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE 
                password = VALUES(password),
                updated_at = CURRENT_TIMESTAMP
        ");
        
        if ($stmt) {
            $stmt->bind_param("ss", $username, $hashedPassword);
            if ($stmt->execute()) {
                $results['admin_created'] = true;
                $results['admin_id'] = $conn->insert_id ?: 1;
            } else {
                throw new Exception("Failed to execute admin creation: " . $stmt->error);
            }
            $stmt->close();
        } else {
            throw new Exception("Failed to prepare admin creation statement: " . $conn->error);
        }
    } catch (Exception $e) {
        $errors[] = "Admin creation failed: " . $e->getMessage();
        $results['admin_created'] = false;
    }
}

// Step 5: Test admin login
if ($step >= 5 && $results['admin_created']) {
    try {
        $username = 'admin';
        $password = '1@3Usazladan';
        
        $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        if ($stmt) {
            $stmt->bind_param("s", $username);
            if ($stmt->execute()) {
                $result = $stmt->get_result();
                if ($admin = $result->fetch_assoc()) {
                    if (password_verify($password, $admin['password'])) {
                        $results['login_test'] = true;
                        $results['admin_data'] = [
                            'id' => $admin['id'],
                            'username' => $admin['username']
                        ];
                    } else {
                        throw new Exception("Password verification failed");
                    }
                } else {
                    throw new Exception("Admin user not found");
                }
            } else {
                throw new Exception("Failed to execute login test: " . $stmt->error);
            }
            $stmt->close();
        } else {
            throw new Exception("Failed to prepare login test statement: " . $conn->error);
        }
    } catch (Exception $e) {
        $errors[] = "Login test failed: " . $e->getMessage();
        $results['login_test'] = false;
    }
}

// Step 6: Test session functionality
if ($step >= 6) {
    try {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $_SESSION['test_session'] = 'working';
        if (isset($_SESSION['test_session']) && $_SESSION['test_session'] === 'working') {
            $results['session_test'] = true;
            unset($_SESSION['test_session']);
        } else {
            throw new Exception("Session not working properly");
        }
    } catch (Exception $e) {
        $errors[] = "Session test failed: " . $e->getMessage();
        $results['session_test'] = false;
    }
}

// Close database connection
if (isset($conn)) {
    $conn->close();
}

$allTestsPassed = empty($errors) && 
                  ($results['config_loaded'] ?? false) && 
                  ($results['db_connected'] ?? false) && 
                  ($results['admin_table_created'] ?? false) && 
                  ($results['admin_created'] ?? false) && 
                  ($results['login_test'] ?? false) && 
                  ($results['session_test'] ?? false);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Admin Fix - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0 0.25rem;
        }
        .step.completed { background-color: #d4edda; color: #155724; }
        .step.current { background-color: #cce5ff; color: #004085; }
        .step.pending { background-color: #f8f9fa; color: #6c757d; }
        .step.error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Production Admin Login Fix - Universal Reciters
                        </h4>
                        <small>Domain: https://universalreciters.name.ng/</small>
                    </div>
                    <div class="card-body">
                        
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step <?php echo ($results['config_loaded'] ?? false) ? 'completed' : (in_array('Config loading failed', array_map(function($e) { return substr($e, 0, 20); }, $errors)) ? 'error' : 'pending'); ?>">
                                <i class="fas fa-cog"></i><br>Config
                            </div>
                            <div class="step <?php echo ($results['db_connected'] ?? false) ? 'completed' : (in_array('Database connection failed', array_map(function($e) { return substr($e, 0, 25); }, $errors)) ? 'error' : 'pending'); ?>">
                                <i class="fas fa-database"></i><br>Database
                            </div>
                            <div class="step <?php echo ($results['admin_table_created'] ?? false) ? 'completed' : 'pending'; ?>">
                                <i class="fas fa-table"></i><br>Table
                            </div>
                            <div class="step <?php echo ($results['admin_created'] ?? false) ? 'completed' : 'pending'; ?>">
                                <i class="fas fa-user-plus"></i><br>Admin User
                            </div>
                            <div class="step <?php echo ($results['login_test'] ?? false) ? 'completed' : 'pending'; ?>">
                                <i class="fas fa-sign-in-alt"></i><br>Login Test
                            </div>
                            <div class="step <?php echo ($results['session_test'] ?? false) ? 'completed' : 'pending'; ?>">
                                <i class="fas fa-cookie-bite"></i><br>Session
                            </div>
                        </div>
                        
                        <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Errors Found:</h5>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($allTestsPassed): ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>All Tests Passed!</h5>
                            <p class="mb-0">Your admin login should now work correctly.</p>
                        </div>
                        
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-key me-2"></i>Admin Login Credentials:</h6>
                                <ul class="mb-0">
                                    <li><strong>Username:</strong> admin</li>
                                    <li><strong>Password:</strong> 1@3Usazladan</li>
                                    <li><strong>Admin ID:</strong> <?php echo $results['admin_data']['id'] ?? 'N/A'; ?></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="login.php" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Go to Admin Login
                            </a>
                        </div>
                        
                        <?php else: ?>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6>Configuration Status:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        Config Loaded
                                        <span class="badge bg-<?php echo ($results['config_loaded'] ?? false) ? 'success' : 'danger'; ?>">
                                            <?php echo ($results['config_loaded'] ?? false) ? 'Yes' : 'No'; ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        Database Connected
                                        <span class="badge bg-<?php echo ($results['db_connected'] ?? false) ? 'success' : 'danger'; ?>">
                                            <?php echo ($results['db_connected'] ?? false) ? 'Yes' : 'No'; ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        Admin Table Created
                                        <span class="badge bg-<?php echo ($results['admin_table_created'] ?? false) ? 'success' : 'warning'; ?>">
                                            <?php echo ($results['admin_table_created'] ?? false) ? 'Yes' : 'No'; ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Database Information:</h6>
                                <?php if (isset($results['db_info'])): ?>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($results['db_info'] as $key => $value): ?>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <?php echo ucfirst($key); ?>
                                        <code><?php echo htmlspecialchars($value); ?></code>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php else: ?>
                                <div class="alert alert-warning">Database information not available</div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Next Steps:</h6>
                                <ol class="mb-0">
                                    <li>Update your database credentials in <code>config/server_config.php</code></li>
                                    <li>Ensure your database server is running and accessible</li>
                                    <li>Check that your database user has CREATE, INSERT, SELECT, UPDATE permissions</li>
                                    <li>Run this script again after fixing database issues</li>
                                </ol>
                            </div>
                        </div>
                        
                        <?php endif; ?>
                        
                        <div class="mt-4 text-center">
                            <a href="?step=6" class="btn btn-primary">
                                <i class="fas fa-redo me-2"></i>Run All Tests Again
                            </a>
                            <a href="diagnostic.php" class="btn btn-secondary">
                                <i class="fas fa-stethoscope me-2"></i>Run Diagnostics
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>Security Note:</strong> Delete this file after fixing the admin login issue.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
