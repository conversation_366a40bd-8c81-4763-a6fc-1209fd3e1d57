<?php
/**
 * Simple Wallet Test
 */

require_once 'config/db_config.php';
require_once 'includes/points_system.php';

echo "<h1>Simple Wallet Test</h1>";

try {
    $conn = getConnection();
    
    // Get a sample user
    $result = $conn->query("SELECT id, full_name, wallet_balance, total_points FROM users LIMIT 1");
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $userId = $user['id'];
        
        echo "Testing with user: " . $user['full_name'] . " (ID: $userId)<br>";
        echo "Initial wallet balance: ₦" . number_format($user['wallet_balance'], 2) . "<br>";
        echo "Initial total points: " . ($user['total_points'] ?? 0) . "<br><br>";
        
        // Test buying points
        $pointsToBuy = 3;
        $pointBuyRate = 70;
        $totalCost = $pointsToBuy * $pointBuyRate;
        
        echo "Testing buy points: $pointsToBuy points for ₦$totalCost<br>";
        
        if ($user['wallet_balance'] >= $totalCost) {
            $conn->begin_transaction();
            
            try {
                // Deduct from wallet
                $newBalance = $user['wallet_balance'] - $totalCost;
                $updateStmt = $conn->prepare("UPDATE users SET wallet_balance = ? WHERE id = ?");
                $updateStmt->bind_param("di", $newBalance, $userId);
                $updateResult = $updateStmt->execute();
                
                if (!$updateResult) {
                    throw new Exception("Failed to update wallet balance: " . $conn->error);
                }
                
                // Award points
                $description = "Test purchase: {$pointsToBuy} points for ₦{$totalCost}";
                $awardSuccess = awardPoints($userId, $pointsToBuy, 'purchase', $description);
                
                if (!$awardSuccess) {
                    throw new Exception("Failed to award points");
                }
                
                // Record transaction
                $transactionStmt = $conn->prepare("
                    INSERT INTO transactions
                    (user_id, transaction_type, amount, points, status, description)
                    VALUES (?, 'point_purchase', ?, ?, 'completed', ?)
                ");
                $transactionDescription = "Test purchase: {$pointsToBuy} points";
                $transactionStmt->bind_param("idis", $userId, $totalCost, $pointsToBuy, $transactionDescription);
                $transactionResult = $transactionStmt->execute();
                
                if (!$transactionResult) {
                    throw new Exception("Failed to record transaction: " . $conn->error);
                }
                
                $conn->commit();
                echo "✅ Buy points test completed successfully!<br>";
                
                // Check final values
                $result = $conn->query("SELECT wallet_balance, total_points FROM users WHERE id = $userId");
                $finalUser = $result->fetch_assoc();
                echo "Final wallet balance: ₦" . number_format($finalUser['wallet_balance'], 2) . "<br>";
                echo "Final total points: " . ($finalUser['total_points'] ?? 0) . "<br>";
                
            } catch (Exception $e) {
                $conn->rollback();
                echo "❌ Buy points test failed: " . $e->getMessage() . "<br>";
            }
        } else {
            echo "❌ Insufficient wallet balance for test<br>";
        }
        
    } else {
        echo "No users found in database<br>";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?> 