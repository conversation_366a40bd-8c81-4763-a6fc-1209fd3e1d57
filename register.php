<?php
// Configuration needs to be loaded first
require_once 'config/db_config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

/**
 * User Registration Page for Recite! App
 * Handles user registration and Paystack payment integration
 */

require_once 'config/db_config.php';

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Sanitize and validate input
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        $password = $_POST['password'] ?? '';
        $ward = sanitize($_POST['ward'] ?? '');
        $lga = sanitize($_POST['lga'] ?? '');
        $state = sanitize($_POST['state'] ?? '');
        $referralCode = sanitize($_POST['referral_code'] ?? '');
        
        // Validation
        if (empty($fullName) || empty($email) || empty($password) || empty($ward) || empty($lga) || empty($state)) {
            $error = 'All fields are required.';
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long.';
        } else {
            try {
                // Check if email already exists
                $result = executeQuery(
                    "SELECT id FROM users WHERE email = ?",
                    's',
                    [$email]
                );
                
                if ($result->num_rows > 0) {
                    $error = 'Email already registered. Please use a different email.';
                } else {
                    // Hash password
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert user (inactive until payment)
                    $insertResult = executeQuery(
                        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
                        'ssssss',
                        [$fullName, $email, $passwordHash, $ward, $lga, $state]
                    );
                    
                    if ($insertResult && $insertResult->success) {
                        // Get the user ID from the insert result
                        $userId = $insertResult->insert_id;
                        
                        // Generate referral code
                        $userReferralCode = generateReferralCode(8);
                        executeQuery(
                            "UPDATE users SET referral_code = ? WHERE id = ?",
                            'si',
                            [$userReferralCode, $userId]
                        );
                        
                        // Handle referral if provided
                        if (!empty($referralCode)) {
                            $referrerResult = executeQuery(
                                "SELECT id FROM users WHERE referral_code = ? AND is_active = 1",
                                's',
                                [$referralCode]
                            );
                            
                            if ($referrerResult->num_rows > 0) {
                                $referrer = $referrerResult->fetch_assoc();
                                executeQuery(
                                    "UPDATE users SET referred_by = ? WHERE id = ?",
                                    'ii',
                                    [$referrer['id'], $userId]
                                );
                            }
                        }
                        
                        // Store user ID in session for payment verification
                        $_SESSION['pending_user_id'] = $userId;
                        $_SESSION['user_email'] = $email;
                        
                        $success = 'Registration successful! Please complete the payment to activate your account.';
                    }
                }
            } catch (Exception $e) {
                error_log("Registration error: " . $e->getMessage());
                $error = 'Registration failed. Please try again.<br><small>' . htmlspecialchars($e->getMessage()) . '</small>';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo APP_NAME; ?></title>

    <!-- Fresh Design System -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/recite-design-system.css" rel="stylesheet">
    <script src="https://js.paystack.co/v1/inline.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            margin: 0;
        }
        .auth-container {
            background: var(--white);
            border-radius: 1.5rem;
            box-shadow: 0 4px 24px rgba(0,0,0,0.07);
            overflow: hidden;
            max-width: 480px;
            width: 100%;
            padding: 2rem 1.5rem;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .auth-logo {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }
        .auth-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }
        .auth-subtitle {
            font-size: 1.1rem;
            color: var(--gray-600);
            margin-bottom: 2rem;
        }
        .form-group {
            margin-bottom: 1.2rem;
        }
        .form-label {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.3rem;
            display: block;
        }
        .form-input, .form-select {
            width: 100%;
            padding: 0.6rem 1rem;
            border-radius: 0.7rem;
            border: 1px solid var(--gray-200);
            font-size: 1rem;
            margin-top: 0.2rem;
        }
        .btn, .btn-primary {
            padding: 0.6rem 1.3rem !important;
            font-size: 1rem !important;
            border-radius: 1.2rem !important;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            justify-content: center;
        }
        .alert {
            padding: 0.8rem 1rem;
            border-radius: 0.7rem;
            margin-bottom: 1.2rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.7rem;
        }
        .alert-error {
            background: #ffeaea;
            color: #b91c1c;
        }
        .alert-success {
            background: #eaffea;
            color: #15803d;
        }
        .text-center { text-align: center; }
        .text-primary { color: var(--primary); }
        .font-medium { font-weight: 600; }
        .mt-8 { margin-top: 2rem; }
        .mt-4 { margin-top: 1rem; }
        @media (max-width: 600px) {
            .auth-container { padding: 1.2rem 0.5rem; }
            .auth-title { font-size: 1.2rem; }
            .auth-logo { font-size: 2rem; }
            .btn, .btn-primary { font-size: 0.95rem !important; padding: 0.5rem 1rem !important; }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-user-plus"></i>
            </div>
            <h1 class="auth-title">Join RECITE</h1>
            <p class="auth-subtitle">
                Create your account and start your recitation journey
            </p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo $success; ?>
            </div>

            <!-- Payment Section -->
            <div id="paymentSection" class="text-center mt-8 p-6 bg-gray-50 rounded-lg">
                <div class="text-3xl font-bold text-primary mb-4">₦1,000</div>
                <p class="text-gray-600 mb-6">
                    You are being redirected to the payment page...
                </p>
                <button onclick="payWithPaystack()" class="btn btn-primary w-full">
                    <i class="fas fa-credit-card"></i>
                    Click here to pay if you are not redirected automatically.
                </button>
                <p class="text-sm text-gray-500 mt-4">
                    <i class="fas fa-shield-alt"></i>
                    Secure payment powered by Paystack
                </p>
            </div>
        <?php else: ?>
                            
        <form method="POST" action="" id="registerForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <div class="form-group">
                <label for="full_name" class="form-label">
                    <i class="fas fa-user"></i> Full Name
                </label>
                <input type="text" class="form-input" id="full_name" name="full_name"
                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                       placeholder="Enter your full name" required>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i> Email Address
                </label>
                <input type="email" class="form-input" id="email" name="email"
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                       placeholder="Enter your email address" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password" class="form-input" id="password" name="password"
                       placeholder="Create a secure password (min. 6 characters)" minlength="6" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="ward" class="form-label">
                        <i class="fas fa-map-marker-alt"></i> Ward
                    </label>
                    <input type="text" class="form-input" id="ward" name="ward"
                           value="<?php echo htmlspecialchars($_POST['ward'] ?? ''); ?>"
                           placeholder="Your ward" required>
                </div>

                <div class="form-group">
                    <label for="lga" class="form-label">
                        <i class="fas fa-map"></i> LGA
                    </label>
                    <input type="text" class="form-input" id="lga" name="lga"
                           value="<?php echo htmlspecialchars($_POST['lga'] ?? ''); ?>"
                           placeholder="Your LGA" required>
                </div>
            </div>

            <div class="form-group">
                <label for="state" class="form-label">
                    <i class="fas fa-map-marker-alt"></i> State
                </label>
                <select class="form-select" id="state" name="state" required>
                    <option value="">Select your state</option>
                    <option value="Abia">Abia</option>
                    <option value="Adamawa">Adamawa</option>
                    <option value="Akwa Ibom">Akwa Ibom</option>
                    <option value="Anambra">Anambra</option>
                    <option value="Bauchi">Bauchi</option>
                    <option value="Bayelsa">Bayelsa</option>
                    <option value="Benue">Benue</option>
                    <option value="Borno">Borno</option>
                    <option value="Cross River">Cross River</option>
                    <option value="Delta">Delta</option>
                    <option value="Ebonyi">Ebonyi</option>
                    <option value="Edo">Edo</option>
                    <option value="Ekiti">Ekiti</option>
                    <option value="Enugu">Enugu</option>
                    <option value="FCT">FCT</option>
                    <option value="Gombe">Gombe</option>
                    <option value="Imo">Imo</option>
                    <option value="Jigawa">Jigawa</option>
                    <option value="Kaduna">Kaduna</option>
                    <option value="Kano">Kano</option>
                    <option value="Katsina">Katsina</option>
                    <option value="Kebbi">Kebbi</option>
                    <option value="Kogi">Kogi</option>
                    <option value="Kwara">Kwara</option>
                    <option value="Lagos">Lagos</option>
                    <option value="Nasarawa">Nasarawa</option>
                    <option value="Niger">Niger</option>
                    <option value="Ogun">Ogun</option>
                    <option value="Ondo">Ondo</option>
                    <option value="Osun">Osun</option>
                    <option value="Oyo">Oyo</option>
                    <option value="Plateau">Plateau</option>
                    <option value="Rivers">Rivers</option>
                    <option value="Sokoto">Sokoto</option>
                    <option value="Taraba">Taraba</option>
                    <option value="Yobe">Yobe</option>
                    <option value="Zamfara">Zamfara</option>
                </select>
            </div>

            <div class="form-group">
                <label for="referral_code" class="form-label">
                    <i class="fas fa-gift"></i> Referral Code (Optional)
                </label>
                <input type="text" class="form-input" id="referral_code" name="referral_code"
                       value="<?php echo htmlspecialchars($_GET['ref'] ?? ''); ?>"
                       placeholder="Enter referral code if you have one">
            </div>

            <button type="submit" class="btn btn-primary w-full">
                <i class="fas fa-user-plus"></i>
                Create My Account
            </button>
        </form>
        <?php endif; ?>

        <div class="text-center mt-8">
            <p class="text-gray-600">
                Already have an account?
                <a href="login.php" class="text-primary font-medium">Sign in here</a>
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Form enhancement
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner loading"></i> Creating Account...';
            submitBtn.disabled = true;

            // Re-enable button if form submission fails
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 10000);
        });

        // Auto-focus first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('full_name').focus();
            
            // If registration was successful, trigger payment automatically
            <?php if ($success): ?>
            // A small delay to allow the user to see the success message
            setTimeout(function() {
                payWithPaystack();
            }, 1500);
            <?php endif; ?>
        });
        
        // Paystack payment function
        function payWithPaystack() {
            <?php if (isset($_SESSION['pending_user_id'])): ?>
            var handler = PaystackPop.setup({
                key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                email: '<?php echo $_SESSION['user_email']; ?>',
                amount: 100000, // ₦1000 in kobo
                currency: 'NGN',
                ref: 'REG_<?php echo $_SESSION['pending_user_id']; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
                metadata: {
                    user_id: <?php echo $_SESSION['pending_user_id']; ?>,
                    purpose: 'registration'
                },
                callback: function(response) {
                    // Payment successful, verify on server
                    window.location.href = 'verify_payment.php?reference=' + response.reference;
                },
                onClose: function() {
                    alert('Payment was not completed. Please try again.');
                }
            });
            
            handler.openIframe();
            <?php endif; ?>
        }
    </script>
</body>
</html>