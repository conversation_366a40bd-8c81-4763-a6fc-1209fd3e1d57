<?php
$page_title = 'Manage Payouts';
require_once __DIR__ . '/../components/admin_header.php';

// Function to process withdrawal requests
if (!function_exists('processWithdrawalRequest')) {
function processWithdrawalRequest($requestId, $action, $adminNotes) {
    try {
        $processingConn = getConnection();
        if (!$processingConn) {
            throw new Exception("Database connection failed");
        }

        // Get withdrawal request details
        $requestStmt = $processingConn->prepare("
            SELECT wr.*, u.full_name, u.email, u.wallet_balance
            FROM withdrawal_requests wr 
            JOIN users u ON wr.user_id = u.id 
            WHERE wr.id = ? AND wr.status = 'pending'
        ");
        $requestStmt->bind_param("i", $requestId);
        $requestStmt->execute();
        $result = $requestStmt->get_result();
        $request = $result->fetch_assoc();
        $requestStmt->close();

        if ($request) {
            $newStatus = $action === 'approve' ? 'approved' : 'rejected';
            $adminUsername = $_SESSION['admin_username'] ?? 'admin';
            
            // Start transaction
            $processingConn->begin_transaction();
            
            try {
                // Update withdrawal request
                $combinedNotes = "Processed by: $adminUsername\n" . $adminNotes;
                $updateStmt = $processingConn->prepare("
                    UPDATE withdrawal_requests 
                    SET status = ?, admin_notes = ?, processed_at = NOW() 
                    WHERE id = ? AND status = 'pending'
                ");
                $updateStmt->bind_param("ssi", $newStatus, $combinedNotes, $requestId);
                
                if (!$updateStmt->execute()) {
                    throw new Exception("Failed to update withdrawal status");
                }
                
                $affectedRows = $updateStmt->affected_rows;
                $updateStmt->close();
                
                if ($affectedRows === 0) {
                    throw new Exception("No rows updated - request may have already been processed");
                }
                
                // If rejected, refund the amount
                if ($action === 'reject') {
                    $refundStmt = $processingConn->prepare("
                        UPDATE users 
                        SET wallet_balance = wallet_balance + ? 
                        WHERE id = ?
                    ");
                    $refundStmt->bind_param("di", $request['amount'], $request['user_id']);
                    
                    if (!$refundStmt->execute()) {
                        throw new Exception("Failed to refund amount");
                    }
                    $refundStmt->close();
                }
                
                // Commit transaction
                $processingConn->commit();
                $processingConn->close();
                
                return "Withdrawal request #{$requestId} has been {$action}ed successfully.";
                
            } catch (Exception $e) {
                $processingConn->rollback();
                $processingConn->close();
                throw $e;
            }
        } else {
            $processingConn->close();
            return 'Withdrawal request not found or already processed.';
        }
        
    } catch (Exception $e) {
        logError("Withdrawal processing error: " . $e->getMessage());
        return 'An error occurred while processing the request: ' . $e->getMessage();
    }
}
} // End function_exists check

// Initialize variables
$message = '';
$messageType = '';

// Handle withdrawal request actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $requestId = intval($_POST['request_id'] ?? 0);
    $adminNotes = sanitize($_POST['admin_notes'] ?? '');
    
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } elseif ($requestId <= 0) {
        $message = 'Invalid request ID.';
        $messageType = 'danger';
    } elseif (!in_array($action, ['approve', 'reject'])) {
        $message = 'Invalid action specified.';
        $messageType = 'danger';
    } else {
        // Process the withdrawal request
        $message = processWithdrawalRequest($requestId, $action, $adminNotes);
        $messageType = strpos($message, 'successfully') !== false ? 'success' : 'danger';
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'pending';

// Fetch withdrawals based on status
$withdrawals = [];
$summary = [
    'total_requests' => 0,
    'pending_requests' => 0,
    'approved_requests' => 0,
    'rejected_requests' => 0,
    'pending_amount' => 0,
    'approved_amount' => 0
];

try {
    // Get a fresh connection for fetching data
    $conn = getConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Fetch withdrawals
    if ($status === 'all') {
        $query = "
            SELECT w.*, u.full_name as username, u.email
            FROM withdrawal_requests w
            JOIN users u ON w.user_id = u.id
            ORDER BY w.created_at DESC
        ";
        $result = $conn->query($query);
        $withdrawals = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    } else {
        $query = "
            SELECT w.*, u.full_name as username, u.email
            FROM withdrawal_requests w
            JOIN users u ON w.user_id = u.id
            WHERE w.status = ?
            ORDER BY w.created_at DESC
        ";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('s', $status);
        $stmt->execute();
        $result = $stmt->get_result();
        $withdrawals = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
    }

    // Get summary statistics
    $summaryQuery = "
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
            SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
            SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
        FROM withdrawal_requests
    ";
    $summaryResult = $conn->query($summaryQuery);
    $summary = $summaryResult ? $summaryResult->fetch_assoc() : $summary;

    $conn->close();

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
    logError("Payouts page error: " . $e->getMessage());
}
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Debug Information for Live Server -->
<?php if (isset($error) && !empty($error)): ?>
<div class="alert alert-info">
    <h6><i class="fas fa-info-circle me-2"></i>Debug Information:</h6>
    <small>
        Total Withdrawals: <?php echo count($withdrawals ?? []); ?><br>
        Status Filter: <?php echo htmlspecialchars($status); ?><br>
        <a href="debug-live-server.php" class="btn btn-sm btn-primary mt-2">
            <i class="fas fa-bug me-1"></i>Run Full Diagnostic
        </a>
    </small>
</div>
<?php endif; ?>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Requests</h6>
                        <h3><?php echo number_format($summary['total_requests']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending</h6>
                        <h3><?php echo number_format($summary['pending_requests']); ?></h3>
                        <small>₦<?php echo number_format($summary['pending_amount'], 2); ?></small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Approved</h6>
                        <h3><?php echo number_format($summary['approved_requests']); ?></h3>
                        <small>₦<?php echo number_format($summary['approved_amount'], 2); ?></small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Rejected</h6>
                        <h3><?php echo number_format($summary['rejected_requests']); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter Tabs -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs">
            <li class="nav-item">
                <a class="nav-link <?php echo $status === 'pending' ? 'active' : ''; ?>" href="?status=pending">
                    <i class="fas fa-clock me-1"></i>Pending
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $status === 'approved' ? 'active' : ''; ?>" href="?status=approved">
                    <i class="fas fa-check me-1"></i>Approved
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $status === 'rejected' ? 'active' : ''; ?>" href="?status=rejected">
                    <i class="fas fa-times me-1"></i>Rejected
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $status === 'all' ? 'active' : ''; ?>" href="?status=all">
                    <i class="fas fa-list me-1"></i>All
                </a>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <?php if (empty($withdrawals)): ?>
        <div class="text-center py-4">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No withdrawal requests found</h5>
            <p class="text-muted">There are no <?php echo $status === 'all' ? '' : $status; ?> withdrawal requests at the moment.</p>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Request ID</th>
                        <th>User</th>
                        <th>Amount</th>
                        <th>Bank Details</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($withdrawals as $withdrawal): ?>
                    <tr>
                        <td>
                            #<?php echo $withdrawal['id']; ?>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo htmlspecialchars($withdrawal['username']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($withdrawal['email']); ?></small>
                            </div>
                        </td>
                        <td>
                            <strong>₦<?php echo number_format($withdrawal['amount'], 2); ?></strong>
                        </td>
                        <td>
                            <?php if (isset($withdrawal['bank_name']) && $withdrawal['bank_name']): ?>
                            <small>
                                <strong><?php echo htmlspecialchars($withdrawal['bank_name']); ?></strong><br>
                                <?php echo htmlspecialchars($withdrawal['account_number']); ?><br>
                                <?php echo htmlspecialchars($withdrawal['account_holder_name']); ?>
                            </small>
                            <?php else: ?>
                            <span class="text-muted">Contact user for details</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?php 
                                echo $withdrawal['status'] === 'pending' ? 'warning' : 
                                    ($withdrawal['status'] === 'approved' ? 'success' : 'danger'); 
                            ?>">
                                <?php echo ucfirst($withdrawal['status']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo date('M j, Y g:i A', strtotime($withdrawal['created_at'])); ?>
                            <?php if (isset($withdrawal['processed_at']) && $withdrawal['processed_at']): ?>
                            <br><small class="text-muted">Processed: <?php echo date('M j, Y', strtotime($withdrawal['processed_at'])); ?></small>
                            <?php endif; ?>
                        </td>
                        <td class="text-end">
                            <?php if ($withdrawal['status'] === 'pending'): ?>
                            <button class="btn btn-sm btn-success me-1" onclick="processRequest(<?php echo $withdrawal['id']; ?>, 'approve')">
                                <i class="fas fa-check"></i> Approve
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="processRequest(<?php echo $withdrawal['id']; ?>, 'reject')">
                                <i class="fas fa-times"></i> Reject
                            </button>
                            <?php else: ?>
                            <small class="text-muted">
                                <?php if (isset($withdrawal['admin_notes']) && $withdrawal['admin_notes']): ?>
                                Notes: <?php echo htmlspecialchars($withdrawal['admin_notes']); ?>
                                <?php else: ?>
                                Processed by system
                                <?php endif; ?>
                            </small>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Process Request Modal -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Process Withdrawal Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="request_id" id="modalRequestId">
                    <input type="hidden" name="action" id="modalAction">
                    
                    <p id="modalMessage"></p>
                    
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea name="admin_notes" id="admin_notes" class="form-control" rows="3" 
                                  placeholder="Add any notes about this decision..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="modalSubmitBtn">Process Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function processRequest(requestId, action) {
    document.getElementById('modalRequestId').value = requestId;
    document.getElementById('modalAction').value = action;
    
    const modal = new bootstrap.Modal(document.getElementById('processModal'));
    const title = document.getElementById('modalTitle');
    const message = document.getElementById('modalMessage');
    const submitBtn = document.getElementById('modalSubmitBtn');
    
    if (action === 'approve') {
        title.textContent = 'Approve Withdrawal Request';
        message.textContent = 'This will approve the withdrawal request and mark it as processed.';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Approve Request';
    } else {
        title.textContent = 'Reject Withdrawal Request';
        message.textContent = 'This will reject the withdrawal request and refund the amount to the user\'s wallet.';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-2"></i>Reject Request';
    }
    
    modal.show();
}
</script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
