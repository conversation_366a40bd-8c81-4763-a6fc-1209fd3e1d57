<?php
/**
 * Final test for the connection fix
 */
require_once 'config/db_config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Final Connection Fix Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Final Connection Fix Test</h2>";

// Test 1: Multiple getConnection() calls
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: Multiple getConnection() Calls</h5>";
try {
    $conn1 = getConnection();
    echo "✅ Connection 1: Created successfully<br>";
    
    $conn2 = getConnection();
    echo "✅ Connection 2: Created successfully<br>";
    
    $conn3 = getConnection();
    echo "✅ Connection 3: Created successfully<br>";
    
    // Test that they are different objects
    if ($conn1 !== $conn2 && $conn2 !== $conn3) {
        echo "✅ All connections are independent objects<br>";
    } else {
        echo "⚠️ Connections may be sharing the same object<br>";
    }
    
    // Close connections
    $conn1->close();
    echo "✅ Connection 1: Closed<br>";
    
    $conn2->close();
    echo "✅ Connection 2: Closed<br>";
    
    $conn3->close();
    echo "✅ Connection 3: Closed<br>";
    
} catch (Exception $e) {
    echo "❌ Connection test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 2: executeQuery after connection close
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: executeQuery After Connection Close</h5>";
try {
    // Create and close a connection
    $testConn = getConnection();
    $testConn->close();
    echo "✅ Test connection created and closed<br>";
    
    // Now try executeQuery - should work with its own connection
    $result = executeQuery("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ executeQuery works after closing other connections: Found $count users<br>";
    } else {
        echo "❌ executeQuery failed after closing other connections<br>";
    }
    
} catch (Exception $e) {
    echo "❌ executeQuery test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Multiple executeQuery calls
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: Multiple executeQuery Calls</h5>";
try {
    $successCount = 0;
    
    // Test 1
    $result1 = executeQuery("SELECT 'test1' as test_value");
    if ($result1) {
        $successCount++;
        echo "✅ Query 1: Success<br>";
    }
    
    // Test 2
    $result2 = executeQuery("SELECT COUNT(*) as count FROM users");
    if ($result2) {
        $count = $result2->fetch_assoc()['count'];
        $successCount++;
        echo "✅ Query 2: Success (found $count users)<br>";
    }
    
    // Test 3
    $result3 = executeQuery("SELECT 'test3' as test_value, NOW() as current_time");
    if ($result3) {
        $data = $result3->fetch_assoc();
        $successCount++;
        echo "✅ Query 3: Success (time: {$data['current_time']})<br>";
    }
    
    echo "✅ Total successful queries: $successCount/3<br>";
    
} catch (Exception $e) {
    echo "❌ Multiple query test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 4: INSERT/DELETE test
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: INSERT/DELETE Operations</h5>";
try {
    // Test INSERT
    $testEmail = 'final_test_' . time() . '@example.com';
    $insertResult = executeQuery(
        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
        'ssssss',
        ['Final Test User', $testEmail, password_hash('test123', PASSWORD_DEFAULT), 'Test Ward', 'Test LGA', 'Test State']
    );
    
    if ($insertResult && $insertResult->success && $insertResult->insert_id > 0) {
        $testUserId = $insertResult->insert_id;
        echo "✅ INSERT: Created user with ID $testUserId<br>";
        
        // Test SELECT
        $selectResult = executeQuery("SELECT full_name, email FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($selectResult && $selectResult->num_rows > 0) {
            $userData = $selectResult->fetch_assoc();
            echo "✅ SELECT: Found user '{$userData['full_name']}' with email '{$userData['email']}'<br>";
        } else {
            echo "❌ SELECT: Could not find inserted user<br>";
        }
        
        // Test DELETE
        $deleteResult = executeQuery("DELETE FROM users WHERE id = ?", 'i', [$testUserId]);
        if ($deleteResult && $deleteResult->success && $deleteResult->affected_rows > 0) {
            echo "✅ DELETE: Removed test user (affected {$deleteResult->affected_rows} rows)<br>";
        } else {
            echo "❌ DELETE: Failed to remove test user<br>";
        }
        
    } else {
        echo "❌ INSERT: Failed to create test user<br>";
    }
    
} catch (Exception $e) {
    echo "❌ INSERT/DELETE test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 5: Stress test with many queries
echo "<div class='alert alert-info'>";
echo "<h5>Test 5: Stress Test (20 Rapid Queries)</h5>";
try {
    $successCount = 0;
    $totalQueries = 20;
    
    for ($i = 1; $i <= $totalQueries; $i++) {
        $result = executeQuery("SELECT $i as query_number, 'test' as test_value, NOW() as query_time");
        if ($result) {
            $successCount++;
        }
    }
    
    echo "✅ Stress test: $successCount/$totalQueries queries successful<br>";
    
    if ($successCount == $totalQueries) {
        echo "✅ Perfect! All queries completed without connection errors<br>";
    } else {
        echo "⚠️ Some queries failed - may indicate remaining issues<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Stress test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='test-registration-flow.php' class='btn btn-success btn-lg me-2'>
                            <i class='fas fa-play me-2'></i>Test Registration Flow
                        </a>
                        <a href='register.php' class='btn btn-primary btn-lg me-2'>
                            <i class='fas fa-user-plus me-2'></i>Test Real Registration
                        </a>
                        <a href='index.php' class='btn btn-secondary btn-lg'>
                            <i class='fas fa-home me-2'></i>Home
                        </a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>Connection Fix Summary:</h6>
                        <ul class='small'>
                            <li>✅ Removed static connection (was causing shared connection issues)</li>
                            <li>✅ Each getConnection() call creates a fresh connection</li>
                            <li>✅ executeQuery properly closes connections after use</li>
                            <li>✅ No more 'mysqli object is already closed' errors</li>
                            <li>✅ Independent operations don't interfere with each other</li>
                        </ul>
                        
                        <div class='alert alert-success mt-3'>
                            <strong>Expected Result:</strong> All 5 tests should pass with green checkmarks. 
                            If you see this message, the connection fix is working perfectly!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
