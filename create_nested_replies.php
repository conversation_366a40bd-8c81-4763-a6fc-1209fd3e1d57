<?php
/**
 * Create Sample Nested Replies for Testing
 */

require_once 'config/db_config.php';

$conn = getConnection();

if (!$conn) {
    die("Database connection failed");
}

// Sample nested replies
$nestedReplies = [
    [
        'post_id' => 1,
        'user_id' => 1,
        'parent_reply_id' => 1, // Reply to the first reply
        'content' => 'I completely agree! This community is exactly what I needed for my Qur\'an recitation journey.'
    ],
    [
        'post_id' => 1,
        'user_id' => 1,
        'parent_reply_id' => 1, // Another reply to the first reply
        'content' => 'The support here is amazing. Everyone is so helpful and encouraging.'
    ],
    [
        'post_id' => 2,
        'user_id' => 1,
        'parent_reply_id' => 3, // Reply to the third reply
        'content' => 'I\'ve been practicing with a teacher for 6 months now and the improvement is incredible!'
    ],
    [
        'post_id' => 2,
        'user_id' => 1,
        'parent_reply_id' => 4, // Reply to the fourth reply
        'content' => 'Recording yourself is a game-changer! I discovered so many mistakes I was making.'
    ],
    [
        'post_id' => 3,
        'user_id' => 1,
        'parent_reply_id' => 5, // Reply to the fifth reply
        'content' => 'I struggled with ghunnah for months. This explanation really helped clarify things!'
    ],
    [
        'post_id' => 5,
        'user_id' => 1,
        'parent_reply_id' => 7, // Reply to the seventh reply
        'content' => 'Early morning practice is the best! The mind is so clear and focused.'
    ]
];

echo "Creating sample nested replies...\n";

try {
    $stmt = $conn->prepare("INSERT INTO forum_replies (post_id, user_id, parent_reply_id, content, created_at) VALUES (?, ?, ?, ?, NOW())");
    
    foreach ($nestedReplies as $reply) {
        $stmt->bind_param("iiis", $reply['post_id'], $reply['user_id'], $reply['parent_reply_id'], $reply['content']);
        
        if ($stmt->execute()) {
            echo "✅ Created nested reply for post " . $reply['post_id'] . " (parent: " . $reply['parent_reply_id'] . ")\n";
        } else {
            echo "❌ Failed to create nested reply for post " . $reply['post_id'] . "\n";
        }
    }
    
    $stmt->close();
    
    echo "\n🎉 Sample nested replies created successfully!\n";
    echo "You can now test the nested reply functionality in the community forum.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 