<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up | EduSphere LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4f46e5;
            --primary-light: #6366f1;
            --primary-dark: #4338ca;
            --secondary: #10b981;
            --dark: #1e293b;
            --light: #f8fafc;
            --gray: #64748b;
            --accent: #f59e0b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: var(--dark);
            background-color: #ffffff;
        }
        
        .font-heading {
            font-family: 'Plus Jakarta Sans', sans-serif;
        }
        
        .gradient-text {
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
        }
    </style>
</head>
<body class="antialiased bg-slate-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="container mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <a href="#" class="flex items-center">
                        <div class="w-10 h-10 rounded-lg gradient-bg flex items-center justify-center mr-3">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <span class="font-heading text-2xl font-bold text-slate-800">EduSphere</span>
                    </a>
                    <div>
                        <span class="text-slate-600">Already have an account?</span>
                        <a href="login.html" class="ml-2 text-indigo-600 font-medium hover:text-indigo-700">Sign in</a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow flex items-center justify-center py-12">
            <div class="w-full max-w-md px-6">
                <div class="bg-white rounded-xl shadow-sm p-8 border border-slate-100">
                    <div class="text-center mb-8">
                        <h1 class="font-heading text-3xl font-bold text-slate-800 mb-2">Get started</h1>
                        <p class="text-slate-600">Create your account to begin your learning journey</p>
                    </div>
                    
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="first-name" class="block text-sm font-medium text-slate-700 mb-1">First name</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-slate-400"></i>
                                    </div>
                                    <input type="text" id="first-name" name="first-name" required 
                                        class="pl-10 block w-full border border-slate-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        placeholder="John">
                                </div>
                            </div>
                            
                            <div>
                                <label for="last-name" class="block text-sm font-medium text-slate-700 mb-1">Last name</label>
                                <input type="text" id="last-name" name="last-name" required 
                                    class="block w-full border border-slate-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="Doe">
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-slate-700 mb-1">Email address</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-slate-400"></i>
                                </div>
                                <input type="email" id="email" name="email" required 
                                    class="pl-10 block w-full border border-slate-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="<EMAIL>">
                            </div>
                        </div>
                        
                        <div>
                            <label for="password" class="block text-sm font-medium text-slate-700 mb-1">Password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-slate-400"></i>
                                </div>
                                <input type="password" id="password" name="password" required 
                                    class="pl-10 block w-full border border-slate-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="••••••••">
                            </div>
                            <p class="mt-1 text-xs text-slate-500">Must be at least 8 characters</p>
                        </div>
                        
                        <div>
                            <label for="confirm-password" class="block text-sm font-medium text-slate-700 mb-1">Confirm password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-lock text-slate-400"></i>
                                </div>
                                <input type="password" id="confirm-password" name="confirm-password" required 
                                    class="pl-10 block w-full border border-slate-300 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="••••••••">
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <input id="terms" name="terms" type="checkbox" 
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                            <label for="terms" class="ml-2 block text-sm text-slate-700">
                                I agree to the <a href="#" class="text-indigo-600 hover:text-indigo-500">Terms of Service</a> and <a href="#" class="text-indigo-600 hover:text-indigo-500">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button type="submit" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Create account
                        </button>
                    </form>
                    
                    <div class="mt-6">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-slate-300"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-slate-500">
                                    Or sign up with
                                </span>
                            </div>
                        </div>
                        
                        <div class="mt-6 grid grid-cols-3 gap-3">
                            <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-slate-300 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-700 hover:bg-slate-50">
                                <i class="fab fa-google text-red-500"></i>
                            </a>
                            <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-slate-300 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-700 hover:bg-slate-50">
                                <i class="fab fa-facebook-f text-blue-600"></i>
                            </a>
                            <a href="#" class="w-full inline-flex justify-center py-2 px-4 border border-slate-300 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-700 hover:bg-slate-50">
                                <i class="fab fa-microsoft text-blue-500"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white py-6 border-t border-slate-200">
            <div class="container mx-auto px-4">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-sm text-slate-500 mb-4 md:mb-0">
                        &copy; 2023 EduSphere. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="#" class="text-sm text-slate-500 hover:text-slate-700">Privacy Policy</a>
                        <a href="#" class="text-sm text-slate-500 hover:text-slate-700">Terms of Service</a>
                        <a href="#" class="text-sm text-slate-500 hover:text-slate-700">Help Center</a>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>