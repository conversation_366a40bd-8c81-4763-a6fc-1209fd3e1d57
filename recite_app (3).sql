-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Aug 01, 2025 at 09:52 PM
-- Server version: 8.0.30
-- PHP Version: 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `recite_app`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `email`, `full_name`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$1JWnjvVou30OaIsL8FZBRO6Mfu1iC2fMWVXlILW6JHfV.a9ZS01Ai', '<EMAIL>', 'System Administrator', 1, '2025-07-10 02:02:55', '2025-08-01 09:47:42');

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int NOT NULL,
  `admin_username` varchar(100) DEFAULT NULL,
  `action` varchar(255) DEFAULT NULL,
  `target_user_id` int DEFAULT NULL,
  `details` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `call_signals`
--

CREATE TABLE `call_signals` (
  `id` int NOT NULL,
  `caller_id` int NOT NULL,
  `callee_id` int NOT NULL,
  `signal_type` enum('offer','answer','candidate') NOT NULL,
  `payload` text NOT NULL,
  `is_processed` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `comments`
--

CREATE TABLE `comments` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `screen_record_id` int DEFAULT NULL,
  `mirror_recording_id` int DEFAULT NULL,
  `stream_id` int DEFAULT NULL,
  `comment_text` text NOT NULL,
  `is_reported` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `content`
--

CREATE TABLE `content` (
  `id` int NOT NULL,
  `surah_number` int NOT NULL,
  `surah_name` varchar(255) NOT NULL,
  `youtube_id` varchar(50) NOT NULL,
  `arabic_text` text NOT NULL,
  `translation` text,
  `unlock_price` decimal(10,2) DEFAULT '30.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `content`
--

INSERT INTO `content` (`id`, `surah_number`, `surah_name`, `youtube_id`, `arabic_text`, `translation`, `unlock_price`, `created_at`, `updated_at`) VALUES
(1, 1, 'Al-Fatiha', 'ZYaZ6Odbx_Y', 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ الرَّحْمَٰنِ الرَّحِيمِ مَالِكِ يَوْمِ الدِّينِ إِيَّاكَ نَعْبُدُ وَإِيَّاكَ نَسْتَعِينُ اهْدِنَا الصِّرَاطَ الْمُسْتَقِيمَ صِرَاطَ الَّذِينَ أَنْعَمْتَ عَلَيْهِمْ غَيْرِ الْمَغْضُوبِ عَلَيْهِمْ وَلَا الضَّالِّينَ', '', '30.00', '2025-07-05 15:36:35', '2025-07-11 10:26:55'),
(2, 2, 'Al-Baqarah (Excerpt)', 'pZ12_E5R3qc', 'الم ذَٰلِكَ الْكِتَابُ لَا رَيْبَ فِيهِ هُدًى لِّلْمُتَّقِينَ الَّذِينَ يُؤْمِنُونَ بِالْغَيْبِ وَيُقِيمُونَ الصَّلَاةَ وَمِمَّا رَزَقْنَاهُمْ يُنفِقُونَ', 'Alif, Lam, Meem. This is the Book about which there is no doubt, a guidance for those conscious of Allah. Who believe in the unseen, establish prayer, and spend out of what We have provided for them.', '30.00', '2025-07-05 15:36:35', '2025-07-10 07:09:53'),
(3, 112, 'Al-Ikhlas', 'kppJBF4dPQY', 'قُلْ هُوَ اللَّهُ أَحَدٌ اللَّهُ الصَّمَدُ لَمْ يَلِدْ وَلَمْ يُولَدْ وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ', NULL, '30.00', '2025-07-05 15:36:35', '2025-07-05 15:36:35');

-- --------------------------------------------------------

--
-- Table structure for table `content_library`
--

CREATE TABLE `content_library` (
  `id` int NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `arabic_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content_type` enum('surah','verse','chapter') COLLATE utf8mb4_unicode_ci DEFAULT 'surah',
  `surah_number` int DEFAULT NULL,
  `verse_number` int DEFAULT NULL,
  `arabic_text` text COLLATE utf8mb4_unicode_ci,
  `transliteration` text COLLATE utf8mb4_unicode_ci,
  `translation` text COLLATE utf8mb4_unicode_ci,
  `audio_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `difficulty_level` enum('beginner','intermediate','advanced') COLLATE utf8mb4_unicode_ci DEFAULT 'beginner',
  `points_required` int DEFAULT '0',
  `is_free` tinyint(1) DEFAULT '1',
  `order_index` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `content_library`
--

INSERT INTO `content_library` (`id`, `title`, `arabic_title`, `content_type`, `surah_number`, `verse_number`, `arabic_text`, `transliteration`, `translation`, `audio_url`, `video_url`, `difficulty_level`, `points_required`, `is_free`, `order_index`, `created_at`, `updated_at`) VALUES
(1, 'Al-Fatiha', 'الفاتحة', 'surah', 1, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 0, 1, 1, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(2, 'Al-Baqarah', 'البقرة', 'surah', 2, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 50, 0, 2, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(3, 'Al-Imran', 'آل عمران', 'surah', 3, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 40, 0, 3, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(4, 'An-Nisa', 'النساء', 'surah', 4, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 60, 0, 4, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(5, 'Al-Maidah', 'المائدة', 'surah', 5, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 30, 0, 5, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(6, 'Al-Anam', 'الأنعام', 'surah', 6, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 35, 0, 6, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(7, 'Al-Araf', 'الأعراف', 'surah', 7, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 40, 0, 7, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(8, 'Al-Anfal', 'الأنفال', 'surah', 8, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 25, 0, 8, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(9, 'At-Tawbah', 'التوبة', 'surah', 9, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 30, 0, 9, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(10, 'Yunus', 'يونس', 'surah', 10, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 25, 0, 10, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(11, 'Hud', 'هود', 'surah', 11, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 30, 0, 11, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(12, 'Yusuf', 'يوسف', 'surah', 12, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 35, 0, 12, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(13, 'Ar-Rad', 'الرعد', 'surah', 13, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 20, 0, 13, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(14, 'Ibrahim', 'إبراهيم', 'surah', 14, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 20, 0, 14, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(15, 'Al-Hijr', 'الحجر', 'surah', 15, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 15, 0, 15, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(16, 'An-Nahl', 'النحل', 'surah', 16, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 25, 0, 16, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(17, 'Al-Isra', 'الإسراء', 'surah', 17, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 30, 0, 17, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(18, 'Al-Kahf', 'الكهف', 'surah', 18, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 35, 0, 18, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(19, 'Maryam', 'مريم', 'surah', 19, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 20, 0, 19, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(20, 'Ta-Ha', 'طه', 'surah', 20, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 25, 0, 20, '2025-07-11 12:37:33', '2025-07-11 12:37:33'),
(21, 'Al-Fatiha', 'الفاتحة', 'surah', 1, NULL, NULL, NULL, NULL, NULL, NULL, 'beginner', 0, 1, 1, '2025-07-11 12:38:39', '2025-07-11 12:38:39'),
(22, 'Al-Baqarah', 'البقرة', 'surah', 2, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 50, 0, 2, '2025-07-11 12:38:39', '2025-07-11 12:38:39'),
(23, 'Al-Imran', 'آل عمران', 'surah', 3, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 40, 0, 3, '2025-07-11 12:38:39', '2025-07-11 12:38:39'),
(24, 'An-Nisa', 'النساء', 'surah', 4, NULL, NULL, NULL, NULL, NULL, NULL, 'advanced', 60, 0, 4, '2025-07-11 12:38:39', '2025-07-11 12:38:39'),
(25, 'Al-Maidah', 'المائدة', 'surah', 5, NULL, NULL, NULL, NULL, NULL, NULL, 'intermediate', 30, 0, 5, '2025-07-11 12:38:39', '2025-07-11 12:38:39');

-- --------------------------------------------------------

--
-- Table structure for table `leaderboards`
--

CREATE TABLE `leaderboards` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `leaderboard_type` enum('ward','lgea','state','country') NOT NULL,
  `location_name` varchar(255) NOT NULL,
  `rank_position` int NOT NULL,
  `total_points` int NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `bonus_awarded` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `likes`
--

CREATE TABLE `likes` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `screen_record_id` int DEFAULT NULL,
  `mirror_recording_id` int DEFAULT NULL,
  `stream_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `mirror_recordings`
--

CREATE TABLE `mirror_recordings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title` varchar(200) DEFAULT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint DEFAULT NULL,
  `duration` int DEFAULT NULL,
  `views_count` int DEFAULT '0',
  `likes_count` int DEFAULT '0',
  `comments_count` int DEFAULT '0',
  `shares_count` int DEFAULT '0',
  `earnings` decimal(10,2) DEFAULT '0.00',
  `is_public` tinyint(1) DEFAULT '1',
  `is_reported` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `point_maturity_view`
-- (See below for the actual view)
--
CREATE TABLE `point_maturity_view` (
`id` int
,`user_id` int
,`points` int
,`transaction_type` enum('recitation_complete','streak_bonus','referral','like_received','comment_received','share_received','leaderboard_bonus','admin_adjustment')
,`description` text
,`reference_id` int
,`reference_type` varchar(50)
,`created_at` timestamp
,`maturity_date` date
,`is_matured` tinyint(1)
,`is_currently_matured` int
,`sell_rate_ngn` int
);

-- --------------------------------------------------------

--
-- Table structure for table `point_transactions`
--

CREATE TABLE `point_transactions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `points` int NOT NULL,
  `transaction_type` enum('recitation_complete','streak_bonus','referral','like_received','comment_received','share_received','leaderboard_bonus','admin_adjustment') NOT NULL,
  `description` text,
  `reference_id` int DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `maturity_date` date DEFAULT NULL,
  `is_matured` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `point_transactions`
--

INSERT INTO `point_transactions` (`id`, `user_id`, `points`, `transaction_type`, `description`, `reference_id`, `reference_type`, `created_at`, `maturity_date`, `is_matured`) VALUES
(1, 10, 2, 'recitation_complete', 'Recitation completed - Duration: 30s, Accuracy: 85%', 9, 'screen_record', '2025-07-11 10:31:58', '2025-08-10', 0),
(2, 10, 1, 'like_received', 'Like received on recording', 1, 'screen_record', '2025-07-11 12:07:16', '2025-08-10', 0),
(3, 10, 1, 'comment_received', 'Comment received on recording', 1, 'screen_record', '2025-07-11 12:07:16', '2025-08-10', 0),
(4, 10, 1, 'share_received', 'Share received on recording', 1, 'screen_record', '2025-07-11 12:07:16', '2025-08-10', 0),
(5, 10, 1, 'like_received', 'Like received on recording', 1, 'screen_record', '2025-07-11 12:07:57', '2025-08-10', 0),
(6, 10, 1, 'like_received', 'Like received on recording', 10, 'screen_record', '2025-07-11 12:08:22', '2025-08-10', 0),
(7, 10, 1, 'like_received', 'Like received on recording', 9, 'screen_record', '2025-07-11 12:08:29', '2025-08-10', 0),
(8, 10, 1, 'comment_received', 'Comment received on recording', 10, 'screen_record', '2025-07-11 12:36:41', '2025-08-10', 0),
(9, 10, 1, 'like_received', 'Like received on recording', 1, 'screen_record', '2025-07-11 13:15:27', '2025-08-10', 0),
(10, 11, 1, 'like_received', 'Like received on recording', 11, 'screen_record', '2025-07-17 16:01:23', '2025-08-16', 0),
(11, 11, 1, 'like_received', 'Like received on recording', 17, 'screen_record', '2025-07-17 16:01:31', '2025-08-16', 0),
(12, 11, 1, 'like_received', 'Like received on recording', 16, 'screen_record', '2025-07-17 17:02:01', '2025-08-16', 0),
(13, 11, 1, 'like_received', 'Like received on recording', 15, 'screen_record', '2025-07-17 17:18:37', '2025-08-16', 0),
(14, 10, 1, 'like_received', 'Like received on recording', 19, 'screen_record', '2025-07-17 17:40:01', '2025-08-16', 0),
(16, 10, 1, 'like_received', 'Like received on recording', 18, 'screen_record', '2025-07-19 22:00:57', '2025-08-18', 0);

-- --------------------------------------------------------

--
-- Table structure for table `rankings`
--

CREATE TABLE `rankings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `ward_rank` int DEFAULT NULL,
  `lga_rank` int DEFAULT NULL,
  `state_rank` int DEFAULT NULL,
  `country_rank` int DEFAULT NULL,
  `total_score` int DEFAULT '0',
  `total_recitations` int DEFAULT '0',
  `streak_count` int DEFAULT '0',
  `last_recitation_date` date DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `recitations`
--

CREATE TABLE `recitations` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_id` int NOT NULL,
  `score` int DEFAULT '100',
  `final_score` int DEFAULT NULL,
  `words_correct` int DEFAULT '0',
  `words_total` int DEFAULT '0',
  `accuracy_percentage` decimal(5,2) DEFAULT NULL,
  `duration_seconds` int DEFAULT NULL,
  `screen_recording_path` varchar(255) DEFAULT NULL,
  `is_completed` tinyint(1) DEFAULT '0',
  `points_awarded` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `recitation_evaluations`
--

CREATE TABLE `recitation_evaluations` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `recording_id` int NOT NULL,
  `recording_type` enum('screen_record','mirror_record') NOT NULL,
  `accuracy_score` int DEFAULT '0',
  `duration_seconds` int DEFAULT '0',
  `points_earned` int DEFAULT '0',
  `evaluation_status` enum('pending','completed','failed') DEFAULT 'pending',
  `evaluation_data` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `recitation_evaluations`
--

INSERT INTO `recitation_evaluations` (`id`, `user_id`, `recording_id`, `recording_type`, `accuracy_score`, `duration_seconds`, `points_earned`, `evaluation_status`, `evaluation_data`, `created_at`) VALUES
(1, 10, 9, 'screen_record', 85, 30, 2, 'completed', NULL, '2025-07-11 10:31:58');

-- --------------------------------------------------------

--
-- Table structure for table `referrals`
--

CREATE TABLE `referrals` (
  `id` int NOT NULL,
  `referrer_id` int NOT NULL,
  `referred_id` int NOT NULL,
  `points_awarded` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reports`
--

CREATE TABLE `reports` (
  `id` int NOT NULL,
  `reporter_id` int NOT NULL,
  `stream_id` int NOT NULL,
  `reason` text,
  `status` enum('pending','reviewed','resolved') DEFAULT 'pending',
  `admin_notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `screen_records`
--

CREATE TABLE `screen_records` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `recitation_id` int DEFAULT NULL,
  `title` varchar(200) DEFAULT NULL,
  `description` text,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint DEFAULT NULL,
  `duration` int DEFAULT NULL,
  `views_count` int DEFAULT '0',
  `likes_count` int DEFAULT '0',
  `comments_count` int DEFAULT '0',
  `shares_count` int DEFAULT '0',
  `earnings` decimal(10,2) DEFAULT '0.00',
  `is_public` tinyint(1) DEFAULT '1',
  `is_reported` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `screen_records`
--

INSERT INTO `screen_records` (`id`, `user_id`, `recitation_id`, `title`, `description`, `file_path`, `file_size`, `duration`, `views_count`, `likes_count`, `comments_count`, `shares_count`, `earnings`, `is_public`, `is_reported`, `created_at`) VALUES
(30, 10, NULL, 'Recitation Recording - Aug 1, 2025 10:46', 'User recording from dashboard', 'uploads/recordings/recording_10_1754045218.mp4', 2501376, NULL, 0, 0, 0, 0, '0.00', 1, 0, '2025-08-01 10:46:58');

-- --------------------------------------------------------

--
-- Table structure for table `streams`
--

CREATE TABLE `streams` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_type` enum('text','image','video','recitation') DEFAULT 'text',
  `text_content` text,
  `media_path` varchar(255) DEFAULT NULL,
  `total_earnings` decimal(10,2) DEFAULT '0.00',
  `like_count` int DEFAULT '0',
  `comment_count` int DEFAULT '0',
  `share_count` int DEFAULT '0',
  `is_reported` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stream_interactions`
--

CREATE TABLE `stream_interactions` (
  `id` int NOT NULL,
  `stream_id` int NOT NULL,
  `user_id` int NOT NULL,
  `interaction_type` enum('like','comment','share') NOT NULL,
  `comment_text` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `transaction_type` enum('payment','withdrawal','deposit','point_sale','point_purchase','video_unlock','video_earnings') NOT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `points` int DEFAULT '0',
  `description` text,
  `reference_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`id`, `user_id`, `transaction_type`, `amount`, `points`, `description`, `reference_id`, `status`, `created_at`) VALUES
(1, 2, 'payment', '1000.00', 0, 'Registration fee', 'REG_2_916624300', 'completed', '2025-07-05 15:47:29'),
(2, 3, 'payment', '1000.00', 0, 'Registration fee', 'REG_3_128767457', 'completed', '2025-07-06 08:25:46'),
(3, 4, 'payment', '1000.00', 0, 'Registration fee', 'REG_4_295288365', 'completed', '2025-07-06 12:42:57'),
(4, 5, 'payment', '1000.00', 0, 'Registration fee', 'REG_5_126111453', 'completed', '2025-07-10 00:44:04'),
(5, 10, 'payment', '1000.00', 0, 'Registration fee', 'REG_10_388113033', 'completed', '2025-07-10 19:27:13'),
(6, 11, 'payment', '1000.00', 0, 'Registration fee', 'REG_11_840412718', 'completed', '2025-07-11 10:59:29'),
(8, 11, 'withdrawal', '500.00', 0, 'Withdrawal to Herman Black - 2222222', NULL, 'completed', '2025-07-19 22:02:21'),
(9, 25, 'payment', '1000.00', 0, 'Registration fee', 'REG_25_692429925', 'completed', '2025-07-25 08:38:14'),
(10, 25, 'deposit', '1000.00', 0, 'Wallet funding via Paystack', 'FUND_25_1000_88851353', 'completed', '2025-07-25 09:16:07'),
(11, 25, 'withdrawal', '500.00', 0, 'Withdrawal to Herman Black - 2222222', NULL, 'completed', '2025-07-25 09:17:03'),
(12, 25, 'withdrawal', '500.00', 0, 'Withdrawal to Opay - 2222222', NULL, 'completed', '2025-07-25 09:20:27'),
(13, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-25 10:55:59'),
(14, 10, 'point_sale', '100.00', 2, 'Sold 2 points', NULL, 'completed', '2025-07-25 10:56:04'),
(15, 10, 'point_purchase', '420.00', 6, 'Purchased 6 points', NULL, 'completed', '2025-07-25 10:56:19'),
(16, 10, 'point_sale', '500.00', 10, 'Sold 10 points', NULL, 'completed', '2025-07-25 10:57:04'),
(18, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 04:31:03'),
(19, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 04:31:08'),
(20, 10, 'point_purchase', '70.00', 1, 'Purchased 1 points', NULL, 'completed', '2025-07-30 04:31:15'),
(21, 10, 'point_purchase', '70.00', 1, 'Purchased 1 points', NULL, 'completed', '2025-07-30 04:31:30'),
(22, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 04:31:39'),
(23, 10, 'point_sale', '500.00', 10, 'Sold 10 points', NULL, 'completed', '2025-07-30 04:31:52'),
(24, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 05:00:50'),
(25, 10, 'point_sale', '500.00', 10, 'Sold 10 points', NULL, 'completed', '2025-07-30 05:01:06'),
(26, 10, 'point_sale', '600.00', 12, 'Sold 12 points', NULL, 'completed', '2025-07-30 05:01:16'),
(27, 10, 'point_sale', '400.00', 8, 'Sold 8 points', NULL, 'completed', '2025-07-30 05:01:27'),
(28, 10, 'point_sale', '100.00', 2, 'Sold 2 points', NULL, 'completed', '2025-07-30 07:33:10'),
(29, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 07:33:41'),
(30, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 07:33:59'),
(31, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 07:34:07'),
(32, 10, 'point_sale', '50.00', 1, 'Sold 1 points', NULL, 'completed', '2025-07-30 10:01:54'),
(33, 10, 'point_purchase', '70.00', 1, 'Purchased 1 points', NULL, 'completed', '2025-07-30 10:02:11'),
(34, 10, 'point_sale', '600.00', 12, 'Sold 12 points', NULL, 'completed', '2025-07-30 10:02:30'),
(35, 10, 'point_sale', '600.00', 12, 'Sold 12 points', NULL, 'completed', '2025-07-30 10:02:37'),
(36, 10, 'point_sale', '600.00', 12, 'Sold 12 points', NULL, 'completed', '2025-07-30 10:02:43'),
(37, 10, 'video_unlock', '3.00', 0, 'Unlocked video for ₦3.00', NULL, 'completed', '2025-08-01 09:42:27'),
(38, 11, 'video_earnings', '1.00', 0, 'Earned ₦1.00 from video unlock', NULL, 'completed', '2025-08-01 09:42:27');

-- --------------------------------------------------------

--
-- Table structure for table `unlocked_content`
--

CREATE TABLE `unlocked_content` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_id` int NOT NULL,
  `unlocked_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `unlocked_content`
--

INSERT INTO `unlocked_content` (`id`, `user_id`, `content_id`, `unlocked_at`) VALUES
(17, 5, 1, '2025-07-10 19:21:22'),
(18, 5, 2, '2025-07-10 19:21:25'),
(20, 5, 3, '2025-07-10 19:21:29'),
(22, 10, 3, '2025-07-10 20:08:07'),
(23, 10, 2, '2025-07-10 20:17:51'),
(24, 10, 1, '2025-07-10 20:18:16'),
(25, 11, 1, '2025-07-11 18:54:08'),
(26, 11, 2, '2025-07-11 22:23:36'),
(27, 11, 3, '2025-07-20 13:29:14');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `ward` varchar(100) DEFAULT NULL,
  `lga` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Nigeria',
  `profile_picture` varchar(255) DEFAULT NULL,
  `wallet_balance` decimal(10,2) DEFAULT '0.00',
  `points_balance` int DEFAULT '0',
  `referral_code` varchar(20) DEFAULT NULL,
  `referred_by` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '0',
  `is_blocked` tinyint(1) DEFAULT '0',
  `payment_verified` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `total_points` int DEFAULT '0',
  `phone` varchar(255) DEFAULT NULL,
  `lgea` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `email`, `password_hash`, `ward`, `lga`, `state`, `country`, `profile_picture`, `wallet_balance`, `points_balance`, `referral_code`, `referred_by`, `is_active`, `is_blocked`, `payment_verified`, `created_at`, `updated_at`, `total_points`, `phone`, `lgea`) VALUES
(2, 'Wesley Schroeder', '<EMAIL>', '$2y$10$G2/ltOYv5injGlFlIyB/C..Uod.OECLcfsjVWz5VDFImUa0.lGZxK', 'Aute cumque sit ani', 'A ut labore aperiam', 'Dolorum sint incidid', 'Nigeria', NULL, '0.00', 0, 'REC0000025982', NULL, 1, 0, 1, '2025-07-05 15:47:17', '2025-07-22 00:01:55', 0, NULL, NULL),
(3, 'Sage Sanford', '<EMAIL>', '$2y$10$/VY6ZtINsiabhobHaDZY0uPJg6hUii/ujwWDzBFiioEH3TTxxRO3e', 'Dolores mollitia nih', 'Molestiae fugiat ut', 'Mollit vel consectet', 'Nigeria', NULL, '0.00', 0, 'REC0000036141', NULL, 1, 0, 1, '2025-07-06 08:25:35', '2025-07-06 08:31:23', 0, NULL, NULL),
(4, 'Samuel Camacho', '<EMAIL>', '$2y$10$YRmxDSB2E2mPtYQND.mnHuDYVnqKuCRRjFNcenZ6AuOls4UQPvFVu', 'Ad mollit autem ipsu', 'Rerum possimus prae', 'Rem iste sit iste cu', 'Nigeria', NULL, '0.00', 0, 'REC0000045B81', NULL, 1, 0, 1, '2025-07-06 12:42:46', '2025-07-06 12:42:57', 0, NULL, NULL),
(5, 'Isaac Green', '<EMAIL>', '$2y$10$h2.FsbszsTqWmpa5/mGQAuyUA/7xvxA146MuwXiG5dEPb1lFbsEcu', 'Eu dolor velit alias', 'Ex et sit et dolorem', 'Aut delectus eiusmo', 'Nigeria', NULL, '880.00', 500, 'REC0000058279', NULL, 1, 0, 1, '2025-07-10 00:43:49', '2025-07-10 19:21:29', 0, NULL, NULL),
(6, 'John Doe', '<EMAIL>', '$2y$10$jxoJLd4sPvIwL3Nmzh0FZeY8sYAsFtyT0U81R3ownVx5RDlW8osR.', NULL, NULL, NULL, 'Nigeria', NULL, '750.50', 300, NULL, NULL, 0, 0, 1, '2025-07-10 04:45:37', '2025-07-10 04:45:37', 0, NULL, NULL),
(7, 'Jane Smith', '<EMAIL>', '$2y$10$qoShIICIGi1k58Tf1RDdWej.aGwGRby3H9Lywy8br7Arpjn8qMcsi', NULL, NULL, NULL, 'Nigeria', NULL, '1200.00', 800, NULL, NULL, 1, 0, 1, '2025-07-10 04:45:37', '2025-07-24 20:15:14', 0, NULL, NULL),
(8, 'Ahmed Hassan', '<EMAIL>', '$2y$10$lnvyPkfy4zsPJnvbGukl2uklWlqUj1cV4tejsDubQM4HsQuFMqSK6', NULL, NULL, NULL, 'Nigeria', NULL, '500.25', 150, NULL, NULL, 1, 0, 1, '2025-07-10 04:45:37', '2025-07-24 20:15:29', 0, NULL, NULL),
(9, 'Daryl Mays', '<EMAIL>', '$2y$10$V349Ygl3jHnsgOvq8Merb.l6gIGGe85sENbpNpFZagzhFmWuhiGk2', 'Aperiam aut voluptat', 'Excepturi esse aut f', 'Enim sunt eos aut i', 'Nigeria', NULL, '0.00', 0, 'REC000009344D', NULL, 0, 0, 0, '2025-07-10 19:23:42', '2025-07-10 19:23:42', 0, NULL, NULL),
(10, 'Keiko Dalton', '<EMAIL>', '$2y$10$RfKgf1g1VXYcoDFIxRBaQOdNiRFjg2/tSuP0YZe3zqFYkrCcGVDui', 'Sunt a est omnis q', 'Quis Nam doloribus n', 'Quo ipsa non volupt', 'Nigeria', '6883b242a7acc-pup_claymation_4.jpeg', '4127.00', 0, 'REC0000105D0D', NULL, 1, 0, 1, '2025-07-10 19:27:04', '2025-08-01 09:42:27', 12, '07026591354', 'Dignissimos facilis cillum autem et'),
(11, 'Freya Jennings', '<EMAIL>', '$2y$10$9RuGemnFv7M3PsyssOk8Te3xdigxhFq.UPt8K5TCO826mx2Wf12eq', 'Voluptatibus ipsam v', 'Provident sit volu', 'Non aspernatur moles', 'Nigeria', NULL, '501.00', 100, 'REC0000114F25', NULL, 1, 0, 1, '2025-07-11 10:59:19', '2025-08-01 09:42:27', 4, NULL, NULL),
(12, 'sulaiman Ahmad muazu', '<EMAIL>', '$2y$10$VaDieZR9PtGFmWiA3cJ92OIOo4ZsYdkPIpK4/51wTF.9DmZ7vStMK', 'KAWRBAI A', 'ZARIA', 'Kaduna', 'Nigeria', NULL, '1000.00', 0, 'REC00001267D5', NULL, 1, 0, 1, '2025-07-11 14:42:28', '2025-07-25 08:27:49', 0, NULL, NULL),
(14, 'Abubakar Abudaullahi', '<EMAIL>', '$2y$10$RlzAqCDNHVcGlyS.i6XKbe0SnuMuZ137dRPfwjYyLpSXgaaWIAJzS', 'kawarbai a', 'zaria', 'kaduna', 'Nigeria', NULL, '2000000.00', 10, NULL, NULL, 0, 0, 0, '2025-07-20 13:24:44', '2025-07-20 13:38:40', 0, NULL, NULL),
(16, 'Shea Boone', '<EMAIL>', '$2y$10$56LMfEPlZRB4cED0ufHzWuL4Sp7rDkSU6KIFJxk6TNS2B8C.B98MW', 'Rerum qui cupiditate', 'Proident dicta magn', 'Dolore esse iste bla', 'Nigeria', NULL, '0.00', 0, NULL, NULL, 0, 0, 0, '2025-07-20 14:08:36', '2025-07-20 14:08:36', 0, NULL, NULL),
(17, 'Joshua Douglas', '<EMAIL>', '$2y$10$pA/krzj3U9hbvrMZJLs8zeQlqfaeUxvOjW5A5XmyILM5eNyDh7e8.', 'Magnam sit culpa ex', 'Ab sed ipsum et tot', 'Quaerat explicabo N', 'Nigeria', NULL, '0.00', 0, NULL, NULL, 0, 0, 0, '2025-07-20 14:10:11', '2025-07-20 14:10:11', 0, NULL, NULL),
(18, 'Kirby Blanchard', '<EMAIL>', '$2y$10$8SgGjtfeo3uC3cS0gC/fy.hcPwwSf0O5FFvdW1.NzIs0t9rHxZlhm', 'Omnis illo ut iste d', 'Pariatur Itaque vel', 'Ea quasi aut minus s', 'Nigeria', NULL, '0.00', 0, NULL, NULL, 0, 0, 0, '2025-07-20 14:23:45', '2025-07-24 18:50:43', 0, NULL, NULL),
(20, 'Kylee Skinner', '<EMAIL>', '$2y$10$W9.w7L.j8mgV7jxF7iwNDuNt00nkcso9wOjB2hH.fH2T0..m3clby', 'Reprehenderit alias', NULL, 'Ad et id perferendis', 'Nigeria', NULL, '97.00', 0, 'JR3MC12P', NULL, 1, 0, 1, '2025-07-24 18:44:40', '2025-07-24 18:44:40', 0, NULL, NULL),
(21, 'Karen Newton', '<EMAIL>', '$2y$10$CwpSOMeH4XqWvq0odXKi0eSOJp1EKU7rx9vMlccCu487XQP3Wj1kG', 'In iusto adipisci so', NULL, 'Cupiditate sed aut c', 'Nigeria', NULL, '96.00', 0, 'K76C4TOO', NULL, 1, 0, 1, '2025-07-24 18:44:54', '2025-07-24 18:47:21', 0, NULL, NULL),
(22, 'Hilda Salas', '<EMAIL>', '$2y$10$8FguG4eiy4GgDOBO3pRRd.KAl4r0DjsmSJ.E5D7pL8V/TueEgEewG', 'Nemo esse qui ad do', 'Officia perspiciatis', 'Imo', 'Nigeria', NULL, '0.00', 0, NULL, NULL, 0, 0, 0, '2025-07-25 08:31:28', '2025-07-25 08:31:28', 0, NULL, NULL),
(23, 'Richard Blanchard', '<EMAIL>', '$2y$10$XE4f10CdtR6B2M7vIEogmub6ZFYZ2912ESSI8WF81rNfaaY4XEU52', 'Pariatur Sit volup', 'Nisi saepe obcaecati', 'Sokoto', 'Nigeria', NULL, '0.00', 0, NULL, NULL, 0, 0, 0, '2025-07-25 08:33:06', '2025-07-25 08:33:06', 0, NULL, NULL),
(24, 'Chantale William', '<EMAIL>', '$2y$10$cR3/mzyHVolMq/9AlnFmqOoJWwImAb4ALtFeiwZDmAzq1AymmvkJG', 'Aspernatur quisquam', 'Sint totam aut qui', 'Katsina', 'Nigeria', NULL, '0.00', 0, 'AGJWR38L', NULL, 0, 0, 0, '2025-07-25 08:34:01', '2025-07-25 08:34:01', 0, NULL, NULL),
(25, 'Avye Pennington', '<EMAIL>', '$2y$10$ckgoC5s58QjFlGw2MQzH6ej23zL4VWhUYCtOKJEoAhcR3e3efCHxW', 'Alias provident pro', 'Nisi aut mollitia cu', 'Zamfara', 'Nigeria', NULL, '0.00', 0, 'H0DXQY57', NULL, 1, 0, 1, '2025-07-25 08:38:04', '2025-08-01 13:32:52', 0, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `user_interactions`
--

CREATE TABLE `user_interactions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `target_user_id` int NOT NULL,
  `recording_id` int NOT NULL,
  `recording_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'screen_record',
  `interaction_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `comment_text` text COLLATE utf8mb4_unicode_ci,
  `points_awarded` int DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_interactions`
--

INSERT INTO `user_interactions` (`id`, `user_id`, `target_user_id`, `recording_id`, `recording_type`, `interaction_type`, `comment_text`, `points_awarded`, `created_at`, `updated_at`) VALUES
(9, 1, 10, 1, '0', 'like', '', 1, '2025-07-11 12:07:57', '2025-07-11 12:07:57'),
(10, 11, 10, 10, '0', 'like', '', 1, '2025-07-11 12:08:22', '2025-07-11 12:08:22'),
(12, 11, 10, 9, '0', 'like', '', 1, '2025-07-11 12:08:29', '2025-07-11 12:08:29'),
(14, 11, 10, 10, '0', 'comment', 'dfd', 1, '2025-07-11 12:36:41', '2025-07-11 12:36:41'),
(15, 11, 10, 1, '0', 'like', '', 1, '2025-07-11 13:15:27', '2025-07-11 13:15:27'),
(16, 11, 11, 15, '0', 'like', '', 1, '2025-07-11 19:43:59', '2025-07-11 19:43:59'),
(17, 10, 11, 11, '0', 'like', '', 1, '2025-07-17 16:01:23', '2025-07-17 16:01:23'),
(18, 10, 11, 17, '0', 'like', '', 1, '2025-07-17 16:01:31', '2025-07-17 16:01:31'),
(19, 10, 10, 18, '0', 'like', '', 1, '2025-07-17 16:01:35', '2025-07-17 16:01:35'),
(21, 10, 10, 18, '0', 'comment', 'sdfc', 1, '2025-07-17 16:54:05', '2025-07-17 16:54:05'),
(23, 10, 11, 16, '0', 'like', '', 1, '2025-07-17 17:02:01', '2025-07-17 17:02:01'),
(25, 10, 10, 19, '0', 'like', '', 1, '2025-07-17 17:18:05', '2025-07-17 17:18:05'),
(27, 10, 11, 15, '0', 'like', '', 1, '2025-07-17 17:18:37', '2025-07-17 17:18:37'),
(28, 13, 10, 19, '0', 'like', '', 1, '2025-07-17 17:40:01', '2025-07-17 17:40:01'),
(31, 10, 13, 20, '0', 'like', '', 1, '2025-07-17 18:03:43', '2025-07-17 18:03:43'),
(33, 10, 10, 23, '0', 'like', '', 1, '2025-07-18 10:24:02', '2025-07-18 10:24:02'),
(34, 11, 10, 18, '0', 'like', '', 1, '2025-07-19 22:00:57', '2025-07-19 22:00:57'),
(35, 11, 11, 16, '0', 'like', '', 1, '2025-07-20 17:27:54', '2025-07-20 17:27:54'),
(37, 10, 10, 28, '0', 'share', '', 1, '2025-07-30 07:37:02', '2025-07-30 07:37:02'),
(39, 10, 10, 28, '0', 'like', '', 1, '2025-07-30 07:37:13', '2025-07-30 07:37:13');

-- --------------------------------------------------------

--
-- Table structure for table `user_points`
--

CREATE TABLE `user_points` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `total_points` int DEFAULT '0',
  `current_streak` int DEFAULT '0',
  `best_streak` int DEFAULT '0',
  `last_activity_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `user_points`
--

INSERT INTO `user_points` (`id`, `user_id`, `total_points`, `current_streak`, `best_streak`, `last_activity_date`, `created_at`, `updated_at`) VALUES
(2, 2, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(3, 3, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(4, 4, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(5, 5, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(6, 6, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(7, 7, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(8, 8, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(9, 9, 0, 0, 0, NULL, '2025-07-11 10:04:15', '2025-07-11 10:04:15'),
(10, 10, 12, 1, 1, '2025-07-11', '2025-07-11 10:04:15', '2025-07-19 22:00:57'),
(25, 11, 4, 0, 0, NULL, '2025-07-17 16:01:23', '2025-07-17 17:18:37');

-- --------------------------------------------------------

--
-- Table structure for table `user_stats`
--

CREATE TABLE `user_stats` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `total_recitations` int DEFAULT '0',
  `total_score` int DEFAULT '0',
  `average_score` decimal(5,2) DEFAULT '0.00',
  `best_score` int DEFAULT '0',
  `current_streak` int DEFAULT '0',
  `longest_streak` int DEFAULT '0',
  `total_study_time` int DEFAULT '0',
  `total_videos_watched` int DEFAULT '0',
  `total_content_unlocked` int DEFAULT '0',
  `last_activity_date` date DEFAULT NULL,
  `level_reached` int DEFAULT '1',
  `badges_earned` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `user_stats`
--

INSERT INTO `user_stats` (`id`, `user_id`, `total_recitations`, `total_score`, `average_score`, `best_score`, `current_streak`, `longest_streak`, `total_study_time`, `total_videos_watched`, `total_content_unlocked`, `last_activity_date`, `level_reached`, `badges_earned`, `created_at`, `updated_at`) VALUES
(4, 2, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-05', 1, 0, '2025-07-05 15:47:32', '2025-07-05 15:47:32'),
(5, 3, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-06', 1, 0, '2025-07-06 08:25:50', '2025-07-06 08:25:50'),
(6, 4, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-06', 1, 0, '2025-07-06 12:43:01', '2025-07-06 12:43:01'),
(7, 5, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-10', 1, 0, '2025-07-10 00:44:07', '2025-07-10 00:44:07'),
(8, 10, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-10', 1, 0, '2025-07-10 19:27:16', '2025-07-10 19:27:16'),
(9, 11, 0, 0, '0.00', 0, 0, 0, 0, 0, 0, '2025-07-11', 1, 0, '2025-07-11 11:00:48', '2025-07-11 11:00:48');

-- --------------------------------------------------------

--
-- Table structure for table `user_unlocked_content`
--

CREATE TABLE `user_unlocked_content` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_id` int NOT NULL,
  `content_type` enum('surah','verse','chapter') COLLATE utf8mb4_unicode_ci DEFAULT 'surah',
  `unlocked_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `unlock_method` enum('free','points','purchase','reward') COLLATE utf8mb4_unicode_ci DEFAULT 'free',
  `points_spent` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_unlocked_content`
--

INSERT INTO `user_unlocked_content` (`id`, `user_id`, `content_id`, `content_type`, `unlocked_at`, `unlock_method`, `points_spent`) VALUES
(2, 2, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(3, 3, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(4, 4, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(5, 5, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(6, 6, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(7, 7, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(8, 8, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(9, 9, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(10, 10, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(11, 11, 1, 'surah', '2025-07-11 12:37:33', 'free', 0),
(12, 10, 2, 'surah', '2025-07-17 17:09:39', 'purchase', 50),
(13, 10, 23, 'surah', '2025-07-17 17:09:50', 'purchase', 40),
(14, 10, 25, 'surah', '2025-07-17 17:45:38', 'purchase', 30);

-- --------------------------------------------------------

--
-- Table structure for table `videos`
--

CREATE TABLE `videos` (
  `id` int NOT NULL,
  `title` varchar(200) NOT NULL,
  `youtube_url` varchar(500) NOT NULL,
  `youtube_id` varchar(50) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `reciter` varchar(100) DEFAULT NULL,
  `description` text,
  `transcript` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `videos`
--

INSERT INTO `videos` (`id`, `title`, `youtube_url`, `youtube_id`, `category`, `reciter`, `description`, `transcript`, `is_active`, `created_by`, `created_at`) VALUES
(1, 'Surah Al-Fatiha - Beautiful Recitation', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Abdul Rahman', 'Beautiful recitation of the opening chapter of the Quran', NULL, 1, NULL, '2025-07-06 12:21:02'),
(2, 'Surah Al-Ikhlas - Perfect Pronunciation', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Muhammad', 'Learn perfect pronunciation of Surah Al-Ikhlas', NULL, 1, NULL, '2025-07-06 12:21:02'),
(3, 'Surah Al-Falaq - Protection Prayer', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Ahmad', 'Seeking protection through Quranic recitation', NULL, 1, NULL, '2025-07-06 12:21:02'),
(4, 'Surah An-Nas - Final Chapter', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Short Surahs', 'Sheikh Ibrahim', 'The final chapter of the Holy Quran', NULL, 1, NULL, '2025-07-06 12:21:02'),
(5, 'Surah Al-Kahf - Friday Special', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Long Surahs', 'Sheikh Yusuf', 'Special Friday recitation of Surah Al-Kahf', NULL, 1, NULL, '2025-07-06 12:21:02'),
(6, 'Surah Yasin - Heart of Quran', 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ', 'Medium Surahs', 'Sheikh Hassan', 'The heart of the Quran - Surah Yasin', NULL, 1, NULL, '2025-07-06 12:21:02'),
(7, 'Aut provident iure', 'https://www.youtube.com/watch?v=rRqC_Vn_5G8&amp;ab_channel=JulianGoldieSEO', 'rRqC_Vn_5G8', 'Magnam qui anim dist', 'Cillum dolore volupt', 'Proident quasi aspe', 'Animi quis dolores', 1, 1, '2025-07-10 20:39:42'),
(8, 'Nulla sequi minima n', 'https://www.youtube.com/watch?v=rRqC_Vn_5G8&amp;ab_channel=JulianGoldieSEO', 'rRqC_Vn_5G8', 'Voluptate autem aut', 'Est illum eos sit', 'Voluptatem occaecat', 'Non qui itaque esse', 1, 1, '2025-07-10 20:42:05');

-- --------------------------------------------------------

--
-- Table structure for table `video_unlocks`
--

CREATE TABLE `video_unlocks` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `recording_id` int NOT NULL,
  `recording_type` enum('screen_record','mirror_record') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'screen_record',
  `unlock_price` decimal(10,2) NOT NULL DEFAULT '3.00',
  `creator_amount` decimal(10,2) NOT NULL DEFAULT '1.00',
  `admin_amount` decimal(10,2) NOT NULL DEFAULT '2.00',
  `creator_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `views`
--

CREATE TABLE `views` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `content_type` enum('screen_record','mirror_recording','stream','video') NOT NULL,
  `content_id` int NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `wallet_transactions`
--

CREATE TABLE `wallet_transactions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `transaction_type` enum('funding','withdrawal','points_purchase','points_sale') COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_account` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bank_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `withdrawals`
--

CREATE TABLE `withdrawals` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `points_converted` int NOT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `account_number` varchar(20) DEFAULT NULL,
  `account_name` varchar(255) DEFAULT NULL,
  `status` enum('pending','approved','completed','rejected') DEFAULT 'pending',
  `maturity_date` date DEFAULT NULL,
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `withdrawal_requests`
--

CREATE TABLE `withdrawal_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `points_converted` int NOT NULL,
  `bank_name` varchar(100) DEFAULT NULL,
  `account_number` varchar(20) DEFAULT NULL,
  `account_holder_name` varchar(100) DEFAULT NULL,
  `status` enum('pending','processing','completed','rejected') DEFAULT 'pending',
  `admin_notes` text,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `withdrawal_requests`
--

INSERT INTO `withdrawal_requests` (`id`, `user_id`, `amount`, `points_converted`, `bank_name`, `account_number`, `account_holder_name`, `status`, `admin_notes`, `processed_at`, `created_at`) VALUES
(1, 25, '500.00', 0, 'Herman Black', '2222222', 'Avye Pennington', 'completed', 'Processed by: admin\n', '2025-07-25 09:25:12', '2025-07-25 09:17:03'),
(2, 25, '500.00', 0, 'Opay', '2222222', 'Avye Pennington', 'completed', 'Processed by: admin\nqwerty', '2025-07-25 09:24:32', '2025-07-25 09:20:27'),
(3, 10, '500.00', 0, 'Moniepoint', '2222222', 'Keiko Dalton', 'completed', 'Processed by: admin\nr3t4twry', '2025-07-25 10:57:42', '2025-07-25 10:57:20');

-- --------------------------------------------------------

--
-- Structure for view `point_maturity_view`
--
DROP TABLE IF EXISTS `point_maturity_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `point_maturity_view`  AS SELECT `pt`.`id` AS `id`, `pt`.`user_id` AS `user_id`, `pt`.`points` AS `points`, `pt`.`transaction_type` AS `transaction_type`, `pt`.`description` AS `description`, `pt`.`reference_id` AS `reference_id`, `pt`.`reference_type` AS `reference_type`, `pt`.`created_at` AS `created_at`, `pt`.`maturity_date` AS `maturity_date`, `pt`.`is_matured` AS `is_matured`, (case when ((`pt`.`is_matured` = 1) or (`pt`.`maturity_date` <= curdate())) then 1 else 0 end) AS `is_currently_matured`, (case when ((`pt`.`is_matured` = 1) or (`pt`.`maturity_date` <= curdate())) then 100 else 50 end) AS `sell_rate_ngn` FROM `point_transactions` AS `pt``pt`  ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `call_signals`
--
ALTER TABLE `call_signals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `caller_id` (`caller_id`),
  ADD KEY `callee_id` (`callee_id`);

--
-- Indexes for table `comments`
--
ALTER TABLE `comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_screen_record` (`screen_record_id`),
  ADD KEY `idx_mirror_recording` (`mirror_recording_id`),
  ADD KEY `idx_stream` (`stream_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `content`
--
ALTER TABLE `content`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `content_library`
--
ALTER TABLE `content_library`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_content_type` (`content_type`),
  ADD KEY `idx_surah_number` (`surah_number`),
  ADD KEY `idx_difficulty` (`difficulty_level`),
  ADD KEY `idx_free_content` (`is_free`),
  ADD KEY `idx_order` (`order_index`);

--
-- Indexes for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_leaderboard` (`leaderboard_type`,`location_name`,`period_start`,`period_end`),
  ADD KEY `idx_user_period` (`user_id`,`period_start`,`period_end`);

--
-- Indexes for table `likes`
--
ALTER TABLE `likes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_screen` (`user_id`,`screen_record_id`),
  ADD UNIQUE KEY `unique_user_mirror` (`user_id`,`mirror_recording_id`),
  ADD UNIQUE KEY `unique_user_stream` (`user_id`,`stream_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_screen_record` (`screen_record_id`),
  ADD KEY `idx_mirror_recording` (`mirror_recording_id`),
  ADD KEY `idx_stream` (`stream_id`);

--
-- Indexes for table `mirror_recordings`
--
ALTER TABLE `mirror_recordings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_public` (`is_public`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_type` (`user_id`,`transaction_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `rankings`
--
ALTER TABLE `rankings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_rank` (`user_id`),
  ADD KEY `idx_rankings_user` (`user_id`);

--
-- Indexes for table `recitations`
--
ALTER TABLE `recitations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `content_id` (`content_id`),
  ADD KEY `idx_recitations_user` (`user_id`);

--
-- Indexes for table `recitation_evaluations`
--
ALTER TABLE `recitation_evaluations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_status` (`user_id`,`evaluation_status`),
  ADD KEY `idx_recording` (`recording_id`,`recording_type`);

--
-- Indexes for table `referrals`
--
ALTER TABLE `referrals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `referrer_id` (`referrer_id`),
  ADD KEY `referred_id` (`referred_id`);

--
-- Indexes for table `reports`
--
ALTER TABLE `reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `reporter_id` (`reporter_id`),
  ADD KEY `stream_id` (`stream_id`);

--
-- Indexes for table `screen_records`
--
ALTER TABLE `screen_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_public` (`is_public`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `streams`
--
ALTER TABLE `streams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_streams_user` (`user_id`);

--
-- Indexes for table `stream_interactions`
--
ALTER TABLE `stream_interactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_like` (`stream_id`,`user_id`,`interaction_type`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_transactions_user` (`user_id`);

--
-- Indexes for table `unlocked_content`
--
ALTER TABLE `unlocked_content`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_unlock` (`user_id`,`content_id`),
  ADD KEY `content_id` (`content_id`),
  ADD KEY `idx_unlocked_content_user` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `referral_code` (`referral_code`),
  ADD KEY `referred_by` (`referred_by`),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_referral_code` (`referral_code`);

--
-- Indexes for table `user_interactions`
--
ALTER TABLE `user_interactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_interaction` (`user_id`,`recording_id`,`recording_type`,`interaction_type`),
  ADD KEY `idx_recording` (`recording_id`,`recording_type`),
  ADD KEY `idx_target_user` (`target_user_id`),
  ADD KEY `idx_interaction_type` (`interaction_type`);

--
-- Indexes for table `user_points`
--
ALTER TABLE `user_points`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user` (`user_id`);

--
-- Indexes for table `user_stats`
--
ALTER TABLE `user_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_stats` (`user_id`);

--
-- Indexes for table `user_unlocked_content`
--
ALTER TABLE `user_unlocked_content`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_unlock` (`user_id`,`content_id`,`content_type`),
  ADD KEY `idx_user_content` (`user_id`,`content_type`);

--
-- Indexes for table `videos`
--
ALTER TABLE `videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `video_unlocks`
--
ALTER TABLE `video_unlocks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_unlock` (`user_id`,`recording_id`,`recording_type`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_recording` (`recording_id`,`recording_type`),
  ADD KEY `idx_creator_id` (`creator_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `views`
--
ALTER TABLE `views`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_content` (`content_type`,`content_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_transaction_type` (`transaction_type`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `withdrawals`
--
ALTER TABLE `withdrawals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `call_signals`
--
ALTER TABLE `call_signals`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `comments`
--
ALTER TABLE `comments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `content`
--
ALTER TABLE `content`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `content_library`
--
ALTER TABLE `content_library`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `leaderboards`
--
ALTER TABLE `leaderboards`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `likes`
--
ALTER TABLE `likes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `mirror_recordings`
--
ALTER TABLE `mirror_recordings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `point_transactions`
--
ALTER TABLE `point_transactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `rankings`
--
ALTER TABLE `rankings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `recitations`
--
ALTER TABLE `recitations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `recitation_evaluations`
--
ALTER TABLE `recitation_evaluations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `referrals`
--
ALTER TABLE `referrals`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reports`
--
ALTER TABLE `reports`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `screen_records`
--
ALTER TABLE `screen_records`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `streams`
--
ALTER TABLE `streams`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `stream_interactions`
--
ALTER TABLE `stream_interactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `unlocked_content`
--
ALTER TABLE `unlocked_content`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `user_interactions`
--
ALTER TABLE `user_interactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `user_points`
--
ALTER TABLE `user_points`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `user_stats`
--
ALTER TABLE `user_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_unlocked_content`
--
ALTER TABLE `user_unlocked_content`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `videos`
--
ALTER TABLE `videos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `video_unlocks`
--
ALTER TABLE `video_unlocks`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `views`
--
ALTER TABLE `views`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `withdrawals`
--
ALTER TABLE `withdrawals`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `call_signals`
--
ALTER TABLE `call_signals`
  ADD CONSTRAINT `call_signals_ibfk_1` FOREIGN KEY (`caller_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `call_signals_ibfk_2` FOREIGN KEY (`callee_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD CONSTRAINT `leaderboards_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `point_transactions`
--
ALTER TABLE `point_transactions`
  ADD CONSTRAINT `point_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `rankings`
--
ALTER TABLE `rankings`
  ADD CONSTRAINT `rankings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `recitations`
--
ALTER TABLE `recitations`
  ADD CONSTRAINT `recitations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `recitations_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `recitation_evaluations`
--
ALTER TABLE `recitation_evaluations`
  ADD CONSTRAINT `recitation_evaluations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `referrals`
--
ALTER TABLE `referrals`
  ADD CONSTRAINT `referrals_ibfk_1` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `referrals_ibfk_2` FOREIGN KEY (`referred_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `reports`
--
ALTER TABLE `reports`
  ADD CONSTRAINT `reports_ibfk_1` FOREIGN KEY (`reporter_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reports_ibfk_2` FOREIGN KEY (`stream_id`) REFERENCES `streams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `streams`
--
ALTER TABLE `streams`
  ADD CONSTRAINT `streams_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `stream_interactions`
--
ALTER TABLE `stream_interactions`
  ADD CONSTRAINT `stream_interactions_ibfk_1` FOREIGN KEY (`stream_id`) REFERENCES `streams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `stream_interactions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `unlocked_content`
--
ALTER TABLE `unlocked_content`
  ADD CONSTRAINT `unlocked_content_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `unlocked_content_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`referred_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `user_points`
--
ALTER TABLE `user_points`
  ADD CONSTRAINT `user_points_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_stats`
--
ALTER TABLE `user_stats`
  ADD CONSTRAINT `user_stats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_unlocked_content`
--
ALTER TABLE `user_unlocked_content`
  ADD CONSTRAINT `user_unlocked_content_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `video_unlocks`
--
ALTER TABLE `video_unlocks`
  ADD CONSTRAINT `video_unlocks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `video_unlocks_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `wallet_transactions`
--
ALTER TABLE `wallet_transactions`
  ADD CONSTRAINT `wallet_transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `withdrawals`
--
ALTER TABLE `withdrawals`
  ADD CONSTRAINT `withdrawals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
