<?php
/**
 * Server Configuration Detector
 * Automatically detects if running on local or live server
 */

// Detect if we're on localhost or live server
$isLocalhost = (
    $_SERVER['HTTP_HOST'] === 'localhost' || 
    $_SERVER['HTTP_HOST'] === '127.0.0.1' || 
    strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
    strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0
);

if ($isLocalhost) {
    // LOCAL DEVELOPMENT SETTINGS
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    
    // Local Database Configuration
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'recite_app');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_CHARSET', 'utf8mb4');
    
    // Local URLs
    define('BASE_URL', 'http://localhost/RECITE_appbac/RECITE_app/');
    define('SITE_URL', 'http://localhost/RECITE_appbac/RECITE_app/');
    
    // Development mode
    define('DEVELOPMENT_MODE', true);
    
} else {
    // LIVE SERVER SETTINGS - universalreciters.name.ng
    error_reporting(E_ALL);
    ini_set('display_errors', 1); // Show errors temporarily to debug white page issue
    ini_set('log_errors', 1);     // Log errors to file

    // Set custom error log location
    $errorLogPath = __DIR__ . '/../logs/';
    if (!is_dir($errorLogPath)) {
        mkdir($errorLogPath, 0755, true);
    }
    ini_set('error_log', $errorLogPath . 'php_errors.log');

    // Live Database Configuration - MUST BE UPDATED FOR YOUR SERVER
    // These are placeholder values - UPDATE THEM WITH YOUR ACTUAL DATABASE CREDENTIALS
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'your_database_name'); // CHANGE THIS to your actual database name
    define('DB_USER', 'your_db_username');   // CHANGE THIS to your actual database username
    define('DB_PASS', 'your_db_password');   // CHANGE THIS to your actual database password
    define('DB_CHARSET', 'utf8mb4');

    // Live URLs - Updated for universalreciters.name.ng
    define('BASE_URL', 'https://universalreciters.name.ng/');
    define('SITE_URL', 'https://universalreciters.name.ng/');

    // Production mode - set to true temporarily for debugging
    define('DEVELOPMENT_MODE', true); // Change to false after fixing database issues
}

// Common settings for both environments
// DB_CHARSET is defined in the local/live sections above

// Paystack Configuration - LIVE KEYS
define('PAYSTACK_PUBLIC_KEY', 'pk_live_c20feccf2bcd5ce811dfef01c926b30c2ccd9ccd');
define('PAYSTACK_SECRET_KEY', '************************************************');

// Application Configuration
define('APP_NAME', 'RECITE');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('REGISTRATION_FEE', 1000);
define('ADMIN_PASSWORD', '1@3Usazladan');
define('DELETE_PASSWORD', '1!3usazladan');

// Create uploads directory if it doesn't exist
$uploadDir = __DIR__ . '/../uploads/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
    mkdir($uploadDir . 'profiles/', 0755, true);
    mkdir($uploadDir . 'recordings/', 0755, true);
    mkdir($uploadDir . 'screen-recordings/', 0755, true);
    mkdir($uploadDir . 'mirrors/', 0755, true);
    mkdir($uploadDir . 'posts/', 0755, true);
    mkdir($uploadDir . 'thumbnails/', 0755, true);
}

/**
 * Enhanced Database Connection with Error Handling
 */
function getConnection() {
    static $connection = null;

    if ($connection === null) {
        // Check if database credentials are still placeholder values
        if (DB_NAME === 'your_database_name' || DB_USER === 'your_db_username' || DB_PASS === 'your_db_password') {
            $errorMsg = "Database not configured! Please update your database credentials in config/server_config.php";
            error_log($errorMsg);
            throw new Exception($errorMsg);
        }

        try {
            $connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

            if ($connection->connect_error) {
                $errorMsg = "Database connection failed: " . $connection->connect_error;
                error_log($errorMsg);
                throw new Exception($errorMsg);
            }

            $connection->set_charset(DB_CHARSET);

        } catch (Exception $e) {
            $errorMsg = "Database error: " . $e->getMessage();
            error_log($errorMsg);
            throw $e; // Re-throw the exception to be handled by the calling code
        }
    }

    return $connection;
}

/**
 * Safe Query Execution
 */
function executeQuery($query, $types = '', $params = []) {
    $conn = getConnection();
    
    try {
        if (empty($params)) {
            $result = $conn->query($query);
        } else {
            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            
            if (!empty($types) && !empty($params)) {
                $stmt->bind_param($types, ...$params);
            }
            
            $result = $stmt->execute();
            if (!$result) {
                throw new Exception("Execute failed: " . $stmt->error);
            }
            
            $result = $stmt->get_result();
        }
        
        return $result;
        
    } catch (Exception $e) {
        if (DEVELOPMENT_MODE) {
            die("Query error: " . $e->getMessage() . "\nQuery: " . $query);
        } else {
            error_log("Query error: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
}

/**
 * Session Management
 */
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Authentication Helper
 */
function requireLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit;
    }
}

/**
 * Get User by ID with Error Handling
 */
function getUserById($userId) {
    try {
        $result = executeQuery(
            "SELECT * FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log("getUserById error: " . $e->getMessage());
        return null;
    }
}

/**
 * Sanitize Input
 */
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Debug Function (only works in development)
 */
function debug($data, $label = 'DEBUG') {
    if (DEVELOPMENT_MODE) {
        echo "<pre><strong>$label:</strong>\n";
        print_r($data);
        echo "</pre>";
    }
}
