<?php
// Load centralized database configuration
require_once __DIR__ . '/config/db_config.php';

// HTTP Request function with cURL fallback
function makeHttpRequest($url, $headers = [], $method = 'GET', $data = null) {
    // Try cURL first
    if (function_exists('curl_init')) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 30
        ));
        
        if ($method === 'POST' && $data) {
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        
        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);
        
        if (!$error) {
            return $response;
        }
    }
    
    // Fallback to file_get_contents
    $context_options = array(
        'http' => array(
            'method' => $method,
            'header' => implode("\r\n", $headers),
            'timeout' => 30,
            'ignore_errors' => true
        )
    );
    
    if ($method === 'POST' && $data) {
        $context_options['http']['content'] = $data;
    }
    
    $context = stream_context_create($context_options);
    $response = file_get_contents($url, false, $context);
    
    return $response;
}

// Paystack payment verification function
function verifyPaystackPayment($reference) {
    $url = "https://api.paystack.co/transaction/verify/" . $reference;
    $headers = [
        "Authorization: Bearer " . PAYSTACK_SECRET_KEY,
        "Cache-Control: no-cache",
    ];
    
    $response = makeHttpRequest($url, $headers);
    
    if ($response) {
        return json_decode($response, true);
    }
    
    return false;
}

// Admin functions are now defined in config/db_config.php

function processReferral($referralCode, $newUserId) {
    $conn = getConnection();
    
    // Find referrer
    $stmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ? AND is_active = 1");
    $stmt->bind_param("s", $referralCode);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($referrer = $result->fetch_assoc()) {
        $referrerId = $referrer['id'];
        
        // Update referred user
        $stmt = $conn->prepare("UPDATE users SET referred_by = ? WHERE id = ?");
        $stmt->bind_param("ii", $referrerId, $newUserId);
        $stmt->execute();
        
        // Add referral record
        $stmt = $conn->prepare("INSERT INTO referrals (referrer_id, referred_id, status) VALUES (?, ?, 'completed')");
        $stmt->bind_param("ii", $referrerId, $newUserId);
        $stmt->execute();
        
        // Award points to referrer
        updatePoints($referrerId, 1, 'Referral bonus');
        
        $stmt->close();
    }
    
    $conn->close();
}

// Ranking Functions
function updateUserRanking($userId) {
    $conn = getConnection();
    
    // Get user location
    $user = getUserById($userId);
    if (!$user) return;
    
    // Calculate user's total score
    $stmt = $conn->prepare("SELECT AVG(accuracy_percentage) as avg_accuracy, COUNT(*) as total_recitations FROM recitations WHERE user_id = ? AND is_completed = 1");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $stats = $result->fetch_assoc();
    $totalScore = $stats['avg_accuracy'] * $stats['total_recitations'];
    
    // Update rankings table
    $stmt = $conn->prepare("INSERT INTO rankings (user_id, total_score) VALUES (?, ?) ON DUPLICATE KEY UPDATE total_score = ?");
    $stmt->bind_param("idi", $userId, $totalScore, $totalScore);
    $stmt->execute();
    
    // Calculate ward rank
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as rank 
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.ward = ? AND u.state = ? AND r.total_score > ? AND u.is_active = 1
    ");
    $stmt->bind_param("ssd", $user['ward'], $user['state'], $totalScore);
    $stmt->execute();
    $result = $stmt->get_result();
    $wardRank = $result->fetch_assoc()['rank'];
    
    // Calculate state rank
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as rank 
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.state = ? AND r.total_score > ? AND u.is_active = 1
    ");
    $stmt->bind_param("sd", $user['state'], $totalScore);
    $stmt->execute();
    $result = $stmt->get_result();
    $stateRank = $result->fetch_assoc()['rank'];
    
    // Calculate country rank
    $stmt = $conn->prepare("
        SELECT COUNT(*) + 1 as rank 
        FROM rankings r 
        JOIN users u ON r.user_id = u.id 
        WHERE u.country = ? AND r.total_score > ? AND u.is_active = 1
    ");
    $stmt->bind_param("sd", $user['country'], $totalScore);
    $stmt->execute();
    $result = $stmt->get_result();
    $countryRank = $result->fetch_assoc()['rank'];
    
    // Update all ranks
    $stmt = $conn->prepare("UPDATE rankings SET ward_rank = ?, state_rank = ?, country_rank = ? WHERE user_id = ?");
    $stmt->bind_param("iiii", $wardRank, $stateRank, $countryRank, $userId);
    $stmt->execute();
    
    $stmt->close();
    $conn->close();
}

// File upload function is now in config/db_config.php

// Utility functions are now in config/db_config.php

// Error handling function is now in config/db_config.php

// Check streak and award bonus
function checkAndAwardStreakBonus($userId) {
    $conn = getConnection();
    
    // Check if user has completed 100 recitations in last 72 hours
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM recitations 
        WHERE user_id = ? 
        AND is_completed = 1 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
    ");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    
    if ($count >= 100) {
        // Check if bonus already awarded for this streak
        $stmt = $conn->prepare("
            SELECT id FROM streaks 
            WHERE user_id = ? 
            AND streak_type = 'challenge' 
            AND bonus_awarded = 1 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 72 HOUR)
        ");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            // Award 100-point bonus
            updatePoints($userId, 100, '100 recitations in 72 hours streak bonus');
            
            // Record streak
            $stmt = $conn->prepare("
                INSERT INTO streaks (user_id, streak_type, start_date, recitations_count, target_count, bonus_awarded, bonus_points) 
                VALUES (?, 'challenge', DATE_SUB(NOW(), INTERVAL 72 HOUR), ?, 100, 1, 100)
            ");
            $stmt->bind_param("ii", $userId, $count);
            $stmt->execute();
        }
    }
    
    $stmt->close();
    $conn->close();
}