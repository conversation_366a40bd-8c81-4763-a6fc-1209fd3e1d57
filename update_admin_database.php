<?php
/**
 * Admin Database Update Script
 * Updates the admin table structure and ensures admin user exists
 * Universal Reciters - https://universalreciters.name.ng/
 */

// Load centralized database configuration
require_once 'config/db_config.php';

// Force error display for this script
error_reporting(E_ALL);
ini_set('display_errors', 1);

$results = [];
$errors = [];

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Admin Database Update - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .update-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .step { padding: 1rem; margin: 0.5rem 0; border-radius: 8px; }
        .step.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .step.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .step.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='update-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-database me-2'></i>Admin Database Update</h1>
                        <p class='text-muted'>Updating admin table structure and user</p>
                    </div>";

try {
    // Step 1: Test database connection
    echo "<div class='step info'>
            <h5><i class='fas fa-plug me-2'></i>Step 1: Testing Database Connection</h5>";
    
    $conn = getConnection();
    echo "<p class='mb-0'>✅ Database connection successful</p>
          </div>";
    $results['connection'] = true;

    // Step 2: Check if admins table exists
    echo "<div class='step info'>
            <h5><i class='fas fa-table me-2'></i>Step 2: Checking Admin Table</h5>";
    
    $tableCheck = $conn->query("SHOW TABLES LIKE 'admins'");
    if ($tableCheck->num_rows > 0) {
        echo "<p>✅ Admin table exists</p>";
        $results['table_exists'] = true;
        
        // Check table structure
        $structure = $conn->query("DESCRIBE admins");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        echo "<p>📋 Current columns: " . implode(', ', $columns) . "</p>";
    } else {
        echo "<p>⚠️ Admin table does not exist - will create it</p>";
        $results['table_exists'] = false;
    }
    echo "</div>";

    // Step 3: Create or update admin table
    echo "<div class='step info'>
            <h5><i class='fas fa-wrench me-2'></i>Step 3: Creating/Updating Admin Table</h5>";
    
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `admins` (
            `id` int NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `password` varchar(255) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `full_name` varchar(100) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT '1',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            KEY `idx_username` (`username`),
            KEY `idx_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($createTableSQL)) {
        echo "<p>✅ Admin table created/updated successfully</p>";
        $results['table_created'] = true;
    } else {
        throw new Exception("Failed to create admin table: " . $conn->error);
    }
    echo "</div>";

    // Step 4: Add missing columns if needed
    echo "<div class='step info'>
            <h5><i class='fas fa-plus me-2'></i>Step 4: Adding Missing Columns</h5>";
    
    $alterQueries = [
        "ALTER TABLE `admins` ADD COLUMN IF NOT EXISTS `email` varchar(100) DEFAULT NULL AFTER `password`",
        "ALTER TABLE `admins` ADD COLUMN IF NOT EXISTS `full_name` varchar(100) DEFAULT NULL AFTER `email`",
        "ALTER TABLE `admins` ADD COLUMN IF NOT EXISTS `is_active` tinyint(1) DEFAULT '1' AFTER `full_name`",
        "ALTER TABLE `admins` ADD COLUMN IF NOT EXISTS `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
    ];
    
    foreach ($alterQueries as $query) {
        $conn->query($query); // Ignore errors for columns that already exist
    }
    echo "<p>✅ Table structure updated</p>";
    echo "</div>";

    // Step 5: Create/Update admin user
    echo "<div class='step info'>
            <h5><i class='fas fa-user-shield me-2'></i>Step 5: Creating/Updating Admin User</h5>";
    
    $username = 'admin';
    $password = '1@3Usazladan';
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $email = '<EMAIL>';
    $fullName = 'System Administrator';
    
    // Check if admin user exists
    $checkAdmin = $conn->prepare("SELECT id, username FROM admins WHERE username = ?");
    $checkAdmin->bind_param("s", $username);
    $checkAdmin->execute();
    $adminResult = $checkAdmin->get_result();
    
    if ($adminResult->num_rows > 0) {
        // Update existing admin
        $updateAdmin = $conn->prepare("
            UPDATE admins 
            SET password = ?, email = ?, full_name = ?, is_active = 1, updated_at = CURRENT_TIMESTAMP 
            WHERE username = ?
        ");
        $updateAdmin->bind_param("ssss", $hashedPassword, $email, $fullName, $username);
        
        if ($updateAdmin->execute()) {
            echo "<p>✅ Admin user updated successfully</p>";
            $results['admin_updated'] = true;
        } else {
            throw new Exception("Failed to update admin user: " . $updateAdmin->error);
        }
        $updateAdmin->close();
    } else {
        // Create new admin
        $insertAdmin = $conn->prepare("
            INSERT INTO admins (username, password, email, full_name, is_active) 
            VALUES (?, ?, ?, ?, 1)
        ");
        $insertAdmin->bind_param("ssss", $username, $hashedPassword, $email, $fullName);
        
        if ($insertAdmin->execute()) {
            echo "<p>✅ Admin user created successfully</p>";
            $results['admin_created'] = true;
        } else {
            throw new Exception("Failed to create admin user: " . $insertAdmin->error);
        }
        $insertAdmin->close();
    }
    $checkAdmin->close();
    echo "</div>";

    // Step 6: Verify admin login
    echo "<div class='step info'>
            <h5><i class='fas fa-check-circle me-2'></i>Step 6: Verifying Admin Login</h5>";
    
    $verifyAdmin = $conn->prepare("SELECT id, username, password, email, full_name, is_active FROM admins WHERE username = ?");
    $verifyAdmin->bind_param("s", $username);
    $verifyAdmin->execute();
    $verifyResult = $verifyAdmin->get_result();
    
    if ($admin = $verifyResult->fetch_assoc()) {
        if (password_verify($password, $admin['password'])) {
            echo "<p>✅ Admin login verification successful</p>";
            echo "<div class='alert alert-info mt-3'>
                    <h6>Admin Details:</h6>
                    <ul class='mb-0'>
                        <li><strong>ID:</strong> {$admin['id']}</li>
                        <li><strong>Username:</strong> {$admin['username']}</li>
                        <li><strong>Email:</strong> {$admin['email']}</li>
                        <li><strong>Full Name:</strong> {$admin['full_name']}</li>
                        <li><strong>Status:</strong> " . ($admin['is_active'] ? 'Active' : 'Inactive') . "</li>
                    </ul>
                  </div>";
            $results['login_verified'] = true;
        } else {
            throw new Exception("Password verification failed");
        }
    } else {
        throw new Exception("Admin user not found after creation");
    }
    $verifyAdmin->close();
    echo "</div>";

    // Step 7: Create admin_logs table
    echo "<div class='step info'>
            <h5><i class='fas fa-list me-2'></i>Step 7: Creating Admin Logs Table</h5>";
    
    $createLogsSQL = "
        CREATE TABLE IF NOT EXISTS `admin_logs` (
            `id` int NOT NULL AUTO_INCREMENT,
            `admin_username` varchar(100) DEFAULT NULL,
            `action` varchar(255) DEFAULT NULL,
            `target_user_id` int DEFAULT NULL,
            `details` text,
            `ip_address` varchar(45) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_admin_username` (`admin_username`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    if ($conn->query($createLogsSQL)) {
        echo "<p>✅ Admin logs table created successfully</p>";
        $results['logs_table_created'] = true;
    } else {
        throw new Exception("Failed to create admin logs table: " . $conn->error);
    }
    echo "</div>";

    $conn->close();

    // Success summary
    echo "<div class='step success'>
            <h5><i class='fas fa-trophy me-2'></i>Update Complete!</h5>
            <p class='mb-3'>All admin database updates completed successfully!</p>
            <div class='row'>
                <div class='col-md-6'>
                    <h6>Admin Login Credentials:</h6>
                    <ul>
                        <li><strong>Username:</strong> admin</li>
                        <li><strong>Password:</strong> 1@3Usazladan</li>
                        <li><strong>URL:</strong> <a href='admin/login.php'>admin/login.php</a></li>
                    </ul>
                </div>
                <div class='col-md-6'>
                    <h6>What Was Updated:</h6>
                    <ul>
                        <li>✅ Admin table structure</li>
                        <li>✅ Admin user account</li>
                        <li>✅ Admin logs table</li>
                        <li>✅ Password encryption</li>
                    </ul>
                </div>
            </div>
            <div class='text-center mt-3'>
                <a href='admin/login.php' class='btn btn-success btn-lg'>
                    <i class='fas fa-sign-in-alt me-2'></i>Try Admin Login Now
                </a>
            </div>
          </div>";

} catch (Exception $e) {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Error Occurred</h5>
            <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
            <div class='alert alert-warning mt-3'>
                <h6>Troubleshooting Steps:</h6>
                <ol class='mb-0'>
                    <li>Check your database credentials in <code>config/db_config.php</code></li>
                    <li>Ensure your database exists and is accessible</li>
                    <li>Verify database user has CREATE, INSERT, UPDATE, SELECT permissions</li>
                    <li>Try using <a href='configure_database.php'>configure_database.php</a> to update credentials</li>
                </ol>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            Delete this file after successful admin setup for security.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
