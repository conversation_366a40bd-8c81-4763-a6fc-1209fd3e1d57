<?php
/**
 * API Endpoint to get forum replies for a specific post
 */

require_once '../config/db_config.php';

// Set headers for JSON response
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$userId = $_SESSION['user_id'];

// Get post ID from request
$postId = intval($_GET['post_id'] ?? 0);

if ($postId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid post ID']);
    exit;
}

try {
    $conn = getConnection();
    
    // Get replies for the post with user information and like counts
    $query = "
        SELECT r.*, u.full_name, u.profile_picture,
               (SELECT COUNT(*) FROM forum_likes WHERE reply_id = r.id) as like_count,
               EXISTS(SELECT 1 FROM forum_likes WHERE reply_id = r.id AND user_id = ?) as user_liked
        FROM forum_replies r
        JOIN users u ON r.user_id = u.id
        WHERE r.post_id = ?
        ORDER BY r.created_at ASC
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $userId, $postId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $replies = [];
    while ($row = $result->fetch_assoc()) {
        $replies[] = [
            'id' => $row['id'],
            'content' => $row['content'],
            'created_at' => $row['created_at'],
            'like_count' => $row['like_count'],
            'user' => [
                'id' => $row['user_id'],
                'name' => $row['full_name'],
                'profile_picture' => $row['profile_picture']
            ],
            'user_liked' => (bool)$row['user_liked']
        ];
    }
    
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'success' => true,
        'replies' => $replies
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?> 