<?php
$page_title = 'Withdrawal Requests';
require_once __DIR__ . '/../components/admin_header.php';

$message = '';
$messageType = '';

// Handle withdrawal request actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'danger';
    } else {
        $action = $_POST['action'] ?? '';
        $requestId = intval($_POST['request_id'] ?? 0);
        $adminNotes = sanitize($_POST['admin_notes'] ?? '');
        
        if ($requestId > 0 && in_array($action, ['approve', 'reject'])) {
            try {
                $conn = getConnection();
                
                // Get withdrawal request details
                $requestStmt = $conn->prepare("
                    SELECT wr.*, u.username, u.email, u.wallet_balance 
                    FROM withdrawal_requests wr 
                    JOIN users u ON wr.user_id = u.id 
                    WHERE wr.id = ? AND wr.status = 'pending'
                ");
                $requestStmt->bind_param("i", $requestId);
                $requestStmt->execute();
                $request = $requestStmt->get_result()->fetch_assoc();
                
                if ($request) {
                    $newStatus = $action === 'approve' ? 'approved' : 'rejected';
                    
                    // Update withdrawal request
                    $updateStmt = $conn->prepare("
                        UPDATE withdrawal_requests 
                        SET status = ?, admin_notes = ?, processed_at = NOW(), processed_by = ? 
                        WHERE id = ?
                    ");
                    $adminUsername = $_SESSION['admin_username'] ?? 'admin';
                    $updateStmt->bind_param("sssi", $newStatus, $adminNotes, $adminUsername, $requestId);
                    
                    if ($updateStmt->execute()) {
                        // If rejected, refund the amount to user's wallet
                        if ($action === 'reject') {
                            $refundStmt = $conn->prepare("
                                UPDATE users 
                                SET wallet_balance = wallet_balance + ? 
                                WHERE id = ?
                            ");
                            $refundStmt->bind_param("di", $request['amount'], $request['user_id']);
                            $refundStmt->execute();
                            
                            // Create wallet transaction for refund
                            $transactionStmt = $conn->prepare("
                                INSERT INTO wallet_transactions (user_id, transaction_type, amount, description, status, created_at) 
                                VALUES (?, 'credit', ?, 'Withdrawal request rejected - refund', 'completed', NOW())
                            ");
                            $transactionStmt->bind_param("id", $request['user_id'], $request['amount']);
                            $transactionStmt->execute();
                            $transactionStmt->close();
                            $refundStmt->close();
                        }
                        
                        // Log admin action
                        if (function_exists('logAdminAction')) {
                            logAdminAction($adminUsername, 'WITHDRAWAL_' . strtoupper($action), $request['user_id'], 
                                "Withdrawal request #{$requestId} {$action}ed for {$request['username']} - Amount: ₦{$request['amount']}");
                        }
                        
                        $message = "Withdrawal request #{$requestId} has been {$action}ed successfully.";
                        $messageType = 'success';
                        
                    } else {
                        $message = 'Failed to update withdrawal request.';
                        $messageType = 'danger';
                    }
                    $updateStmt->close();
                } else {
                    $message = 'Withdrawal request not found or already processed.';
                    $messageType = 'warning';
                }
                $requestStmt->close();
                $conn->close();
                
            } catch (Exception $e) {
                logError("Withdrawal request processing error: " . $e->getMessage());
                $message = 'An error occurred while processing the request.';
                $messageType = 'danger';
            }
        }
    }
}

// Get withdrawal requests with pagination
$page = intval($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$status = $_GET['status'] ?? 'all';

try {
    $conn = getConnection();
    
    // Build query based on status filter
    $whereClause = '';
    $params = [];
    $types = '';
    
    if ($status !== 'all') {
        $whereClause = 'WHERE wr.status = ?';
        $params[] = $status;
        $types = 's';
    }
    
    // Get total count
    $countQuery = "SELECT COUNT(*) as total FROM withdrawal_requests wr $whereClause";
    if (!empty($params)) {
        $countStmt = $conn->prepare($countQuery);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $totalRequests = $countStmt->get_result()->fetch_assoc()['total'];
        $countStmt->close();
    } else {
        $totalRequests = $conn->query($countQuery)->fetch_assoc()['total'];
    }
    
    // Get withdrawal requests
    $query = "
        SELECT wr.*, u.username, u.email, u.full_name, u.phone_number 
        FROM withdrawal_requests wr 
        JOIN users u ON wr.user_id = u.id 
        $whereClause 
        ORDER BY wr.created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $params[] = $limit;
        $params[] = $offset;
        $types .= 'ii';
        $stmt->bind_param($types, ...$params);
    } else {
        $stmt->bind_param('ii', $limit, $offset);
    }
    
    $stmt->execute();
    $requests = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    // Get summary statistics
    $summaryQuery = "
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_requests,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_requests,
            SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
            SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
        FROM withdrawal_requests
    ";
    $summary = $conn->query($summaryQuery)->fetch_assoc();
    
    $conn->close();
    
} catch (Exception $e) {
    logError("Withdrawal requests page error: " . $e->getMessage());
    $requests = [];
    $totalRequests = 0;
    $summary = [];
}

$totalPages = ceil($totalRequests / $limit);
$csrfToken = generateCSRFToken();
?>

<div class="container-fluid">
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $summary['total_requests'] ?? 0; ?></h4>
                            <p class="mb-0">Total Requests</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $summary['pending_requests'] ?? 0; ?></h4>
                            <p class="mb-0">Pending</p>
                            <small>₦<?php echo number_format($summary['pending_amount'] ?? 0, 2); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $summary['approved_requests'] ?? 0; ?></h4>
                            <p class="mb-0">Approved</p>
                            <small>₦<?php echo number_format($summary['approved_amount'] ?? 0, 2); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $summary['rejected_requests'] ?? 0; ?></h4>
                            <p class="mb-0">Rejected</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>Withdrawal Requests
                </h5>
                
                <!-- Status Filter -->
                <div class="btn-group">
                    <a href="?status=all" class="btn btn-sm <?php echo $status === 'all' ? 'btn-light' : 'btn-outline-light'; ?>">All</a>
                    <a href="?status=pending" class="btn btn-sm <?php echo $status === 'pending' ? 'btn-light' : 'btn-outline-light'; ?>">Pending</a>
                    <a href="?status=approved" class="btn btn-sm <?php echo $status === 'approved' ? 'btn-light' : 'btn-outline-light'; ?>">Approved</a>
                    <a href="?status=rejected" class="btn btn-sm <?php echo $status === 'rejected' ? 'btn-light' : 'btn-outline-light'; ?>">Rejected</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            
            <?php if (empty($requests)): ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No withdrawal requests found</h5>
                <p class="text-muted">
                    <?php echo $status === 'all' ? 'No withdrawal requests have been made yet.' : "No {$status} withdrawal requests found."; ?>
                </p>
            </div>
            <?php else: ?>
            
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Amount</th>
                            <th>Bank Details</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($requests as $request): ?>
                        <tr>
                            <td>#<?php echo $request['id']; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($request['username']); ?></strong><br>
                                <small class="text-muted"><?php echo htmlspecialchars($request['email']); ?></small>
                            </td>
                            <td>
                                <strong>₦<?php echo number_format($request['amount'], 2); ?></strong>
                            </td>
                            <td>
                                <small>
                                    <strong><?php echo htmlspecialchars($request['bank_name']); ?></strong><br>
                                    <?php echo htmlspecialchars($request['account_number']); ?><br>
                                    <?php echo htmlspecialchars($request['account_name']); ?>
                                </small>
                            </td>
                            <td>
                                <?php
                                $statusClass = [
                                    'pending' => 'warning',
                                    'approved' => 'success',
                                    'rejected' => 'danger'
                                ];
                                ?>
                                <span class="badge bg-<?php echo $statusClass[$request['status']] ?? 'secondary'; ?>">
                                    <?php echo ucfirst($request['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('M j, Y g:i A', strtotime($request['created_at'])); ?>
                                <?php if ($request['processed_at']): ?>
                                <br><small class="text-muted">Processed: <?php echo date('M j, Y', strtotime($request['processed_at'])); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($request['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-success me-1" onclick="processRequest(<?php echo $request['id']; ?>, 'approve')">
                                    <i class="fas fa-check"></i> Approve
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="processRequest(<?php echo $request['id']; ?>, 'reject')">
                                    <i class="fas fa-times"></i> Reject
                                </button>
                                <?php else: ?>
                                <small class="text-muted">
                                    By: <?php echo htmlspecialchars($request['processed_by'] ?? 'System'); ?>
                                    <?php if ($request['admin_notes']): ?>
                                    <br>Notes: <?php echo htmlspecialchars($request['admin_notes']); ?>
                                    <?php endif; ?>
                                </small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Withdrawal requests pagination">
                <ul class="pagination justify-content-center">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
            
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Process Request Modal -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="request_id" id="modalRequestId">
                <input type="hidden" name="action" id="modalAction">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Process Withdrawal Request</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                  placeholder="Optional notes about this decision..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="modalMessage"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function processRequest(requestId, action) {
    document.getElementById('modalRequestId').value = requestId;
    document.getElementById('modalAction').value = action;
    
    const modal = new bootstrap.Modal(document.getElementById('processModal'));
    const title = document.getElementById('modalTitle');
    const message = document.getElementById('modalMessage');
    const submitBtn = document.getElementById('modalSubmitBtn');
    
    if (action === 'approve') {
        title.textContent = 'Approve Withdrawal Request';
        message.textContent = 'This will approve the withdrawal request and mark it as processed.';
        submitBtn.className = 'btn btn-success';
        submitBtn.innerHTML = '<i class="fas fa-check me-2"></i>Approve Request';
    } else {
        title.textContent = 'Reject Withdrawal Request';
        message.textContent = 'This will reject the withdrawal request and refund the amount to the user\'s wallet.';
        submitBtn.className = 'btn btn-danger';
        submitBtn.innerHTML = '<i class="fas fa-times me-2"></i>Reject Request';
    }
    
    modal.show();
}
</script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
