-- 
-- Fixed Admin Table for Universal Reciters
-- Compatible with MySQL 5.7+ and MariaDB 10.2+
-- Uses utf8mb4_general_ci collation for maximum compatibility
--

-- Drop existing admin table if it exists (optional - remove this line if you want to keep existing data)
-- DROP TABLE IF EXISTS `admins`;

-- Create admins table with compatible collation
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON>IMAR<PERSON>E<PERSON> (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_username` (`username`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert the existing admin user with the correct password hash
-- Username: admin, Password: 1@3Usazladan
INSERT INTO `admins` (`id`, `username`, `password`, `email`, `full_name`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$xYhrgKAA83a37AXGAXrjDOmNOxUkVBMXt/DCpsznGu5GoVV6GHyDW', '<EMAIL>', 'System Administrator', 1, '2025-07-10 02:02:55', '2025-07-19 12:48:35')
ON DUPLICATE KEY UPDATE 
    password = VALUES(password),
    email = VALUES(email),
    full_name = VALUES(full_name),
    updated_at = CURRENT_TIMESTAMP;

-- Create admin_logs table with compatible collation
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_username` varchar(100) DEFAULT NULL,
  `action` varchar(255) DEFAULT NULL,
  `target_user_id` int DEFAULT NULL,
  `details` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_username` (`admin_username`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Set AUTO_INCREMENT starting value for admins table
ALTER TABLE `admins` AUTO_INCREMENT = 2;

-- Set AUTO_INCREMENT starting value for admin_logs table  
ALTER TABLE `admin_logs` AUTO_INCREMENT = 1;

-- Verify the admin user was created
SELECT 'Admin table setup complete!' as status;
SELECT id, username, email, full_name, is_active, created_at FROM admins WHERE username = 'admin';
