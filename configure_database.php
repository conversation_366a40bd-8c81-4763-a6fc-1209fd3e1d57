<?php
/**
 * Database Configuration for Universal Reciters
 * Updates the centralized database configuration in config/db_config.php
 */

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? '';
    $db_user = $_POST['db_user'] ?? '';
    $db_pass = $_POST['db_pass'] ?? '';
    
    // Test the database connection first
    try {
        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
        
        if ($conn->connect_error) {
            throw new Exception('Connection failed: ' . $conn->connect_error);
        }
        
        // Connection successful, update the config file
        $configFile = __DIR__ . '/config/db_config.php';
        $content = file_get_contents($configFile);
        
        // Replace the database configuration
        $content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '$db_host');", $content);
        $content = str_replace("define('DB_NAME', 'univers1_recite_app');", "define('DB_NAME', '$db_name');", $content);
        $content = str_replace("define('DB_USER', 'univers1_recite_app');", "define('DB_USER', '$db_user');", $content);
        $content = str_replace("define('DB_PASS', 'your_database_password');", "define('DB_PASS', '$db_pass');", $content);
        
        if (file_put_contents($configFile, $content)) {
            $message = 'Database configuration updated successfully! You can now try the admin login.';
            $messageType = 'success';
        } else {
            $message = 'Database connection successful, but failed to update config file. Please check file permissions.';
            $messageType = 'warning';
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        $message = 'Database connection failed: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Configuration - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="setup-card p-5">
                    <div class="text-center mb-4">
                        <h1 class="text-primary">
                            <i class="fas fa-database me-2"></i>Database Configuration
                        </h1>
                        <p class="text-muted">Universal Reciters - Centralized Database Setup</p>
                    </div>
                    
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'warning' ? 'exclamation-triangle' : 'times-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Centralized Configuration</h6>
                        <p class="mb-0">All database credentials are now stored in <strong>one file only</strong>: <code>config/db_config.php</code></p>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">
                                <i class="fas fa-server me-2"></i>Database Host
                            </label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="localhost" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">
                                <i class="fas fa-database me-2"></i>Database Name
                            </label>
                            <input type="text" class="form-control" id="db_name" name="db_name" 
                                   value="univers1_recite_app" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">
                                <i class="fas fa-user me-2"></i>Database Username
                            </label>
                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                   value="univers1_recite_app" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">
                                <i class="fas fa-lock me-2"></i>Database Password
                            </label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" 
                                   placeholder="Enter your database password" required>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>Update Database Configuration
                            </button>
                        </div>
                    </form>
                    
                    <?php if ($messageType === 'success'): ?>
                    <div class="mt-4 text-center">
                        <a href="admin/login.php" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>Try Admin Login
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-check-circle me-2 text-success"></i>Benefits of Centralized Config</h6>
                                <ul class="mb-0 small">
                                    <li>✅ All database credentials in one file</li>
                                    <li>✅ No more scattered configuration</li>
                                    <li>✅ Easy to update and maintain</li>
                                    <li>✅ Consistent across entire application</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
