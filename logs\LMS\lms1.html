<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduCore - Learning Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .feature-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .stats-counter {
            animation: countUp 2s ease-out;
        }
        
        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .mobile-menu {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .mobile-menu.active {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold text-gray-900">EduCore</span>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">Home</a>
                    <a href="#features" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">Features</a>
                    <a href="#courses" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">Courses</a>
                    <a href="#pricing" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">Pricing</a>
                    <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition duration-300">Contact</a>
                    <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                        Get Started
                    </button>
                </div>
                
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="md:hidden text-gray-700">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-menu md:hidden fixed inset-y-0 left-0 w-64 bg-white shadow-xl z-50">
            <div class="p-6">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-lg"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900">EduCore</span>
                    </div>
                    <button id="close-menu" class="text-gray-500">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <a href="#home" class="block text-gray-700 hover:text-blue-600 font-medium py-2">Home</a>
                    <a href="#features" class="block text-gray-700 hover:text-blue-600 font-medium py-2">Features</a>
                    <a href="#courses" class="block text-gray-700 hover:text-blue-600 font-medium py-2">Courses</a>
                    <a href="#pricing" class="block text-gray-700 hover:text-blue-600 font-medium py-2">Pricing</a>
                    <a href="#contact" class="block text-gray-700 hover:text-blue-600 font-medium py-2">Contact</a>
                    <button class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-300 mt-4">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-left">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                        Transform Your Learning Experience
                    </h1>
                    <p class="text-xl mb-8 text-blue-100 leading-relaxed">
                        Discover the most comprehensive Learning Management System designed for modern education. 
                        Engage, learn, and succeed with our innovative platform.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <button class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 shadow-lg">
                            <i class="fas fa-play mr-2"></i>
                            Start Learning
                        </button>
                        <button class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-300">
                            <i class="fas fa-video mr-2"></i>
                            Watch Demo
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <div class="animate-float">
                        <div class="bg-white rounded-2xl p-8 shadow-2xl">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-gray-900 font-semibold">John Doe</h3>
                                    <p class="text-gray-600 text-sm">Course Progress</p>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-700 text-sm">Web Development</span>
                                    <span class="text-blue-600 text-sm font-semibold">85%</span>
                                </div>
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div class="gradient-bg rounded-full h-2" style="width: 85%"></div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-700 text-sm">Data Science</span>
                                    <span class="text-blue-600 text-sm font-semibold">92%</span>
                                </div>
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div class="gradient-bg rounded-full h-2" style="width: 92%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="bg-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2 stats-counter">50K+</div>
                    <p class="text-gray-600">Active Students</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2 stats-counter">1.2K+</div>
                    <p class="text-gray-600">Expert Instructors</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2 stats-counter">5K+</div>
                    <p class="text-gray-600">Courses Available</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2 stats-counter">98%</div>
                    <p class="text-gray-600">Success Rate</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="bg-gray-50 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Discover the comprehensive tools and features that make our LMS the perfect choice for your learning journey.
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature Card 1 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-play-circle text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Interactive Video Lessons</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Engage with high-quality video content featuring interactive elements, quizzes, and real-time discussions.
                    </p>
                </div>
                
                <!-- Feature Card 2 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Progress Tracking</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Monitor your learning progress with detailed analytics and personalized insights to optimize your studies.
                    </p>
                </div>
                
                <!-- Feature Card 3 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Collaborative Learning</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Connect with peers, join study groups, and participate in collaborative projects and discussions.
                    </p>
                </div>
                
                <!-- Feature Card 4 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-mobile-alt text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Mobile Learning</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Learn anywhere, anytime with our fully responsive design and dedicated mobile applications.
                    </p>
                </div>
                
                <!-- Feature Card 5 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-certificate text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Certifications</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Earn industry-recognized certificates and credentials to advance your career and showcase your skills.
                    </p>
                </div>
                
                <!-- Feature Card 6 -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-brain text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">AI-Powered Learning</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Experience personalized learning paths powered by AI that adapt to your learning style and pace.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="bg-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Popular Courses</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Explore our most popular courses designed by industry experts to help you master new skills.
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Course Card 1 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover border border-gray-100">
                    <div class="h-48 gradient-bg flex items-center justify-center">
                        <i class="fas fa-code text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-blue-100 text-blue-600 text-xs font-semibold px-3 py-1 rounded-full">Programming</span>
                            <div class="ml-auto flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <span class="text-gray-600 text-sm ml-1">4.9</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Full Stack Web Development</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Master modern web development with React, Node.js, and MongoDB in this comprehensive course.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="fas fa-clock mr-2"></i>
                                <span>40 hours</span>
                            </div>
                            <div class="text-blue-600 font-semibold text-xl">$99</div>
                        </div>
                    </div>
                </div>
                
                <!-- Course Card 2 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover border border-gray-100">
                    <div class="h-48 bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center">
                        <i class="fas fa-chart-bar text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-100 text-green-600 text-xs font-semibold px-3 py-1 rounded-full">Data Science</span>
                            <div class="ml-auto flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <span class="text-gray-600 text-sm ml-1">4.8</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Data Science with Python</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Learn data analysis, machine learning, and visualization using Python and popular libraries.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="fas fa-clock mr-2"></i>
                                <span>35 hours</span>
                            </div>
                            <div class="text-blue-600 font-semibold text-xl">$129</div>
                        </div>
                    </div>
                </div>
                
                <!-- Course Card 3 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden card-hover border border-gray-100">
                    <div class="h-48 bg-gradient-to-r from-purple-400 to-pink-500 flex items-center justify-center">
                        <i class="fas fa-paint-brush text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-purple-100 text-purple-600 text-xs font-semibold px-3 py-1 rounded-full">Design</span>
                            <div class="ml-auto flex items-center text-yellow-400">
                                <i class="fas fa-star"></i>
                                <span class="text-gray-600 text-sm ml-1">4.7</span>
                            </div>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">UI/UX Design Masterclass</h3>
                        <p class="text-gray-600 text-sm mb-4">
                            Create stunning user interfaces and experiences with modern design principles and tools.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-gray-500 text-sm">
                                <i class="fas fa-clock mr-2"></i>
                                <span>25 hours</span>
                            </div>
                            <div class="text-blue-600 font-semibold text-xl">$79</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <button class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                    View All Courses
                </button>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="bg-gray-50 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Select the perfect plan for your learning journey. All plans include our core features with varying levels of access.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Basic Plan -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Basic</h3>
                        <div class="text-4xl font-bold text-blue-600 mb-1">$29</div>
                        <p class="text-gray-600">per month</p>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Access to 50+ courses</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Basic progress tracking</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Community forum access</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Email support</span>
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                        Get Started
                    </button>
                </div>
                
                <!-- Pro Plan -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover border-2 border-blue-500 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Pro</h3>
                        <div class="text-4xl font-bold text-blue-600 mb-1">$59</div>
                        <p class="text-gray-600">per month</p>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Access to all courses</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Advanced analytics</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>1-on-1 mentorship</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Priority support</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Certificates included</span>
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                        Get Started
                    </button>
                </div>
                
                <!-- Enterprise Plan -->
                <div class="bg-white rounded-xl p-8 shadow-lg card-hover">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                        <div class="text-4xl font-bold text-blue-600 mb-1">$99</div>
                        <p class="text-gray-600">per month</p>
                    </div>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Everything in Pro</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Team management</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>Custom branding</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>API access</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            <span>24/7 dedicated support</span>
                        </li>
                    </ul>
                    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="bg-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">What Our Students Say</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Hear from thousands of students who have transformed their careers with our platform.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-gray-50 rounded-xl p-8 card-hover">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 text-lg">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6 leading-relaxed">
                        "EduCore completely transformed my learning experience. The interactive courses and expert guidance helped me land my dream job in tech!"
                    </p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            S
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900">Sarah Johnson</h4>
                            <p class="text-gray-600 text-sm">Software Developer</p>
                        </div>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-gray-50 rounded-xl p-8 card-hover">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400 text-lg">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6 leading-relaxed">
                        "The AI-powered learning paths adapted perfectly to my pace. I was able to master data science concepts faster than I ever imagined."
                    </p>
                   <div class="flex items-center">
                       <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                           M
                       </div>
                       <div>
                           <h4 class="font-semibold text-gray-900">Michael Chen</h4>
                           <p class="text-gray-600 text-sm">Data Scientist</p>
                       </div>
                   </div>
               </div>
               
               <!-- Testimonial 3 -->
               <div class="bg-gray-50 rounded-xl p-8 card-hover">
                   <div class="flex items-center mb-4">
                       <div class="text-yellow-400 text-lg">
                           <i class="fas fa-star"></i>
                           <i class="fas fa-star"></i>
                           <i class="fas fa-star"></i>
                           <i class="fas fa-star"></i>
                           <i class="fas fa-star"></i>
                       </div>
                   </div>
                   <p class="text-gray-700 mb-6 leading-relaxed">
                       "The collaborative features and community support made learning enjoyable. I've built lasting connections with peers and mentors."
                   </p>
                   <div class="flex items-center">
                       <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                           E
                       </div>
                       <div>
                           <h4 class="font-semibold text-gray-900">Emily Rodriguez</h4>
                           <p class="text-gray-600 text-sm">UX Designer</p>
                       </div>
                   </div>
               </div>
           </div>
       </div>
   </section>

   <!-- CTA Section -->
   <section class="gradient-bg text-white py-20">
       <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
           <h2 class="text-4xl font-bold mb-6">Ready to Start Your Learning Journey?</h2>
           <p class="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
               Join thousands of students who are already transforming their careers with EduCore. 
               Start your free trial today and experience the future of online learning.
           </p>
           <div class="flex flex-col sm:flex-row gap-4 justify-center">
               <button class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 shadow-lg">
                   <i class="fas fa-rocket mr-2"></i>
                   Start Free Trial
               </button>
               <button class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition duration-300">
                   <i class="fas fa-phone mr-2"></i>
                   Contact Sales
               </button>
           </div>
           <p class="text-blue-100 text-sm mt-6">No credit card required • 14-day free trial • Cancel anytime</p>
       </div>
   </section>

   <!-- Footer -->
   <footer class="bg-gray-900 text-white py-16">
       <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
           <div class="grid md:grid-cols-4 gap-8">
               <!-- Company Info -->
               <div class="col-span-1 md:col-span-1">
                   <div class="flex items-center space-x-2 mb-6">
                       <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                           <i class="fas fa-graduation-cap text-white text-lg"></i>
                       </div>
                       <span class="text-2xl font-bold">EduCore</span>
                   </div>
                   <p class="text-gray-400 mb-6 leading-relaxed">
                       Transforming education through innovative technology and personalized learning experiences.
                   </p>
                   <div class="flex space-x-4">
                       <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition duration-300">
                           <i class="fab fa-facebook-f"></i>
                       </a>
                       <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition duration-300">
                           <i class="fab fa-twitter"></i>
                       </a>
                       <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition duration-300">
                           <i class="fab fa-linkedin-in"></i>
                       </a>
                       <a href="#" class="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-blue-600 transition duration-300">
                           <i class="fab fa-instagram"></i>
                       </a>
                   </div>
               </div>
               
               <!-- Quick Links -->
               <div>
                   <h3 class="text-lg font-semibold mb-6">Quick Links</h3>
                   <ul class="space-y-3">
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">About Us</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Courses</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Instructors</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Blog</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Career</a></li>
                   </ul>
               </div>
               
               <!-- Support -->
               <div>
                   <h3 class="text-lg font-semibold mb-6">Support</h3>
                   <ul class="space-y-3">
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Help Center</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Contact Us</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Terms of Service</a></li>
                       <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">FAQ</a></li>
                   </ul>
               </div>
               
               <!-- Contact Info -->
               <div>
                   <h3 class="text-lg font-semibold mb-6">Contact Info</h3>
                   <ul class="space-y-3">
                       <li class="flex items-center text-gray-400">
                           <i class="fas fa-map-marker-alt mr-3"></i>
                           <span>123 Learning St, Education City</span>
                       </li>
                       <li class="flex items-center text-gray-400">
                           <i class="fas fa-phone mr-3"></i>
                           <span>+****************</span>
                       </li>
                       <li class="flex items-center text-gray-400">
                           <i class="fas fa-envelope mr-3"></i>
                           <span><EMAIL></span>
                       </li>
                       <li class="flex items-center text-gray-400">
                           <i class="fas fa-clock mr-3"></i>
                           <span>Mon-Fri: 9AM-6PM</span>
                       </li>
                   </ul>
               </div>
           </div>
           
           <div class="border-t border-gray-800 mt-12 pt-8 text-center">
               <p class="text-gray-400">
                   © 2025 EduCore. All rights reserved. Made with ❤️ for learners worldwide.
               </p>
           </div>
       </div>
   </footer>

   <!-- Back to Top Button -->
   <button id="back-to-top" class="fixed bottom-8 right-8 bg-blue-600 text-white w-12 h-12 rounded-full shadow-lg hover:bg-blue-700 transition duration-300 opacity-0 pointer-events-none">
       <i class="fas fa-arrow-up"></i>
   </button>

   <script>
       // Mobile Menu Toggle
       const mobileMenuBtn = document.getElementById('mobile-menu-btn');
       const mobileMenu = document.getElementById('mobile-menu');
       const closeMenu = document.getElementById('close-menu');

       mobileMenuBtn.addEventListener('click', () => {
           mobileMenu.classList.add('active');
       });

       closeMenu.addEventListener('click', () => {
           mobileMenu.classList.remove('active');
       });

       // Close mobile menu when clicking outside
       document.addEventListener('click', (e) => {
           if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
               mobileMenu.classList.remove('active');
           }
       });

       // Smooth scrolling for navigation links
       document.querySelectorAll('a[href^="#"]').forEach(anchor => {
           anchor.addEventListener('click', function (e) {
               e.preventDefault();
               const target = document.querySelector(this.getAttribute('href'));
               if (target) {
                   target.scrollIntoView({
                       behavior: 'smooth',
                       block: 'start'
                   });
                   // Close mobile menu if open
                   mobileMenu.classList.remove('active');
               }
           });
       });

       // Back to top button
       const backToTopBtn = document.getElementById('back-to-top');

       window.addEventListener('scroll', () => {
           if (window.pageYOffset > 300) {
               backToTopBtn.style.opacity = '1';
               backToTopBtn.style.pointerEvents = 'auto';
           } else {
               backToTopBtn.style.opacity = '0';
               backToTopBtn.style.pointerEvents = 'none';
           }
       });

       backToTopBtn.addEventListener('click', () => {
           window.scrollTo({
               top: 0,
               behavior: 'smooth'
           });
       });

       // Animate stats counters when in view
       const animateCounters = () => {
           const counters = document.querySelectorAll('.stats-counter');
           
           counters.forEach(counter => {
               const target = parseInt(counter.innerText.replace(/\D/g, ''));
               const increment = target / 200;
               let current = 0;
               
               const updateCounter = () => {
                   if (current < target) {
                       current += increment;
                       const suffix = counter.innerText.includes('K') ? 'K+' : 
                                    counter.innerText.includes('%') ? '%' : '+';
                       counter.innerText = Math.ceil(current) + suffix;
                       requestAnimationFrame(updateCounter);
                   } else {
                       counter.innerText = counter.innerText;
                   }
               };
               
               updateCounter();
           });
       };

       // Intersection Observer for animations
       const observerOptions = {
           threshold: 0.1,
           rootMargin: '0px 0px -50px 0px'
       };

       const observer = new IntersectionObserver((entries) => {
           entries.forEach(entry => {
               if (entry.isIntersecting) {
                   if (entry.target.classList.contains('stats-counter')) {
                       animateCounters();
                       observer.unobserve(entry.target);
                   }
               }
           });
       }, observerOptions);

       // Observe stats counters
       document.querySelectorAll('.stats-counter').forEach(counter => {
           observer.observe(counter);
       });

       // Add loading animation
       window.addEventListener('load', () => {
           document.body.classList.add('loaded');
       });

       // Add navbar background on scroll
       window.addEventListener('scroll', () => {
           const navbar = document.querySelector('nav');
           if (window.scrollY > 50) {
               navbar.classList.add('bg-white', 'shadow-lg');
           } else {
               navbar.classList.remove('shadow-lg');
           }
       });

       // Add hover effect to feature cards
       document.querySelectorAll('.card-hover').forEach(card => {
           card.addEventListener('mouseenter', () => {
               card.style.transform = 'translateY(-8px)';
           });
           
           card.addEventListener('mouseleave', () => {
               card.style.transform = 'translateY(0)';
           });
       });

       // Add typing effect to hero title
       const heroTitle = document.querySelector('h1');
       const titleText = heroTitle.innerText;
       heroTitle.innerHTML = '';

       let i = 0;
       const typeWriter = () => {
           if (i < titleText.length) {
               heroTitle.innerHTML += titleText.charAt(i);
               i++;
               setTimeout(typeWriter, 50);
           }
       };

       // Start typing effect after a short delay
       setTimeout(typeWriter, 500);
   </script>
</body>
</html>