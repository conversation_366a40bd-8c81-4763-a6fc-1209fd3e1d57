# Universal Reciters Production Setup Guide

## Issue: Admin Login Showing Blank/White Page

The blank page issue is caused by database configuration problems. Here's how to fix it:

## Quick Fix (Recommended)

### Option 1: Use the Setup Script
1. Visit: `https://universalreciters.name.ng/setup_production.php`
2. Follow the 3-step setup process:
   - Configure your database credentials
   - Create admin user
   - Complete setup

### Option 2: Manual Configuration

#### Step 1: Update Database Configuration
Edit `config/server_config.php` and replace these lines:
```php
define('DB_NAME', 'your_database_name'); // CHANGE THIS to your actual database name
define('DB_USER', 'your_db_username');   // CHANGE THIS to your actual database username  
define('DB_PASS', 'your_db_password');   // CHANGE THIS to your actual database password
```

With your actual database credentials:
```php
define('DB_NAME', 'your_actual_db_name');
define('DB_USER', 'your_actual_db_user');
define('DB_PASS', 'your_actual_db_password');
```

#### Step 2: Create Admin Table and User
Run this SQL in your database:
```sql
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO admins (username, password) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
```

## Admin Login Credentials
- **Username:** admin
- **Password:** 1@3Usazladan

## Diagnostic Tools Available

### 1. Production Fix Tool
Visit: `https://universalreciters.name.ng/admin/production_fix.php`
- Runs comprehensive tests
- Shows step-by-step diagnostics
- Fixes common issues automatically

### 2. Database Setup Helper
Visit: `https://universalreciters.name.ng/config/db_setup.php`
- Test database connection
- Update configuration automatically

### 3. Admin Diagnostic Tool
Visit: `https://universalreciters.name.ng/admin/diagnostic.php`
- Check all admin login requirements
- Verify database tables
- Test session functionality

## Common Issues and Solutions

### Issue 1: White/Blank Page
**Cause:** Database not configured or connection failed
**Solution:** Use setup_production.php or update config/server_config.php

### Issue 2: "Service temporarily unavailable"
**Cause:** Database credentials are incorrect
**Solution:** Verify database name, username, and password in hosting control panel

### Issue 3: "Database not configured" Error
**Cause:** Placeholder values still in config file
**Solution:** Replace placeholder values with actual database credentials

### Issue 4: Admin table doesn't exist
**Cause:** Database tables not created
**Solution:** Use production_fix.php or run SQL commands manually

## File Locations

### Configuration Files
- `config/server_config.php` - Main server configuration
- `config/database.php` - Database helper functions
- `config.php` - Main config loader

### Admin Files
- `admin/login.php` - Admin login page
- `admin/dashboard.php` - Admin dashboard
- `components/admin_header.php` - Admin navigation

### Setup Tools
- `setup_production.php` - Quick setup tool
- `admin/production_fix.php` - Comprehensive diagnostic and fix tool
- `config/db_setup.php` - Database configuration helper
- `admin/diagnostic.php` - Admin login diagnostics

## Security Notes

1. **Delete setup files** after configuration:
   - `setup_production.php`
   - `admin/production_fix.php`
   - `admin/diagnostic.php`
   - `config/db_setup.php`

2. **Change default admin password** after first login

3. **Set DEVELOPMENT_MODE to false** in production:
   ```php
   define('DEVELOPMENT_MODE', false);
   ```

## URLs Updated for Production

All URLs have been updated to use `https://universalreciters.name.ng/`:
- Base URL: `https://universalreciters.name.ng/`
- Site URL: `https://universalreciters.name.ng/`
- Referral links now use dynamic SITE_URL constant

## Database Requirements

### Minimum Requirements
- MySQL 5.7+ or MariaDB 10.2+
- PHP 7.4+ (8.0+ recommended)
- Database user with CREATE, INSERT, SELECT, UPDATE, DELETE permissions

### Required Tables
The setup will create these tables automatically:
- `admins` - Admin user accounts
- `admin_logs` - Admin action logging
- `users` - Regular user accounts
- Other app-specific tables

## Testing the Fix

1. Visit: `https://universalreciters.name.ng/admin/login.php`
2. Login with: admin / 1@3Usazladan
3. Should redirect to admin dashboard

If still having issues:
1. Check server error logs
2. Run diagnostic tools
3. Verify database credentials in hosting control panel
4. Ensure database exists and user has proper permissions

## Support

If you continue to experience issues:
1. Check the error logs in `/logs/php_errors.log`
2. Run all diagnostic tools
3. Verify your hosting provider's database configuration
4. Ensure your database user has all necessary permissions
