<?php
$page_title = 'User Dashboard';
require_once __DIR__ . '/../components/user_header.php';

$conn = getConnection();

// Get selected content ID from URL parameter
$selectedContentId = isset($_GET['content_id']) ? intval($_GET['content_id']) : null;

// Fetch available Surahs from content table
$surahs = [];
try {
    $surah_stmt = $conn->prepare("SELECT id, surah_number, surah_name, youtube_id, arabic_text, unlock_price FROM content ORDER BY surah_number ASC");
    $surah_stmt->execute();
    $surah_result = $surah_stmt->get_result();
    while ($row = $surah_result->fetch_assoc()) {
        $surahs[] = $row;
    }
    $surah_stmt->close();
} catch (Exception $e) {
    error_log("Error fetching Surahs: " . $e->getMessage());
}

// Get current video to display (either selected Surah or latest admin video)
$currentVideo = null;
if ($selectedContentId) {
    // User selected a specific Surah
    try {
        $content_stmt = $conn->prepare("SELECT id, surah_name as title, youtube_id, arabic_text as transcript, 'Quran Recitation' as category, 'Various Reciters' as reciter FROM content WHERE id = ?");
        $content_stmt->bind_param("i", $selectedContentId);
        $content_stmt->execute();
        $currentVideo = $content_stmt->get_result()->fetch_assoc();
        $content_stmt->close();
    } catch (Exception $e) {
        error_log("Error fetching selected content: " . $e->getMessage());
    }
}

// Fallback to latest admin video if no Surah selected or found
if (!$currentVideo) {
    try {
        $video_stmt = $conn->prepare("SELECT * FROM videos WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1");
        $video_stmt->execute();
        $currentVideo = $video_stmt->get_result()->fetch_assoc();
        $video_stmt->close();
    } catch (Exception $e) {
        error_log("Error fetching admin videos: " . $e->getMessage());
    }
}

// Fetch user ranking info (placeholders for now)
$user_rank = [
    'ward_rank' => 1,
    'lgea_rank' => 5,
    'state_rank' => 25,
    'country_rank' => 103,
];

$conn->close();
?>

<div class="interactive-dashboard" data-transcript="<?php echo htmlspecialchars($currentVideo['transcript'] ?? ''); ?>">

    <!-- Recitation Engine Section -->
    <div class="dashboard-card recitation-engine-card">
        <div class="card-header">
            <h4><i class="fas fa-book-quran me-2"></i> Recitation Engine</h4>
            <span class="text-muted">Select a Surah to practice</span>
        </div>
        <div class="card-body">
            <div class="surah-selector">
                <label for="surahSelect" class="form-label">Choose a Surah:</label>
                <select id="surahSelect" class="form-select" onchange="loadSurah(this.value)">
                    <option value="">-- Select a Surah --</option>
                    <?php foreach ($surahs as $surah): ?>
                        <option value="<?php echo $surah['id']; ?>"
                                data-youtube-id="<?php echo htmlspecialchars($surah['youtube_id']); ?>"
                                data-arabic-text="<?php echo htmlspecialchars($surah['arabic_text']); ?>"
                                <?php echo ($selectedContentId == $surah['id']) ? 'selected' : ''; ?>>
                            <?php echo $surah['surah_number']; ?>. <?php echo htmlspecialchars($surah['surah_name']); ?>
                            <?php if ($surah['unlock_price'] > 0): ?>
                                (₦<?php echo number_format($surah['unlock_price']); ?>)
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <?php if ($selectedContentId): ?>
                <div class="selected-surah-info mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Selected:</strong> <?php echo htmlspecialchars($currentVideo['title'] ?? 'Unknown Surah'); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Part 1: Video Player -->
    <div class="dashboard-card video-card">
        <div class="card-header">
            <h4><i class="fas fa-video me-2"></i> <?php echo htmlspecialchars($currentVideo['title'] ?? 'No Video Available'); ?></h4>
            <span class="text-muted">by <?php echo htmlspecialchars($currentVideo['reciter'] ?? 'N/A'); ?></span>
        </div>
        <div class="card-body">
            <?php if ($currentVideo && !empty($currentVideo['youtube_id'])): ?>
                <div class="video-container">
                    <iframe id="videoPlayer" src="https://www.youtube.com/embed/<?php echo htmlspecialchars($currentVideo['youtube_id']); ?>" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                </div>
            <?php else: ?>
                <div class="text-center p-5">
                    <i class="fas fa-video-slash fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No recitation video available. Please select a Surah from the Recitation Engine above or wait for admin to upload videos.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Part 2: Mirror Camera -->
    <div class="dashboard-card mirror-card">
        <div class="card-header">
            <h4><i class="fas fa-camera-retro me-2"></i> Reciter's Mirror</h4>
        </div>
        <div class="card-body">
            <div id="mirror-container">
                <video id="mirrorVideo" autoplay muted playsinline></video>
                <div id="cameraPlaceholder" class="placeholder-content">
                    <i class="fas fa-video fa-3x mb-3"></i>
                    <p>Camera is off</p>
                </div>
            </div>
            <button id="startCameraBtn" class="btn btn-primary w-100 mt-3"><i class="fas fa-video me-2"></i>Start Camera</button>
        </div>
    </div>

    <!-- Part 3: Recitation Panel -->
    <div class="dashboard-card recitation-card">
        <div class="card-header">
            <h4><i class="fas fa-microphone-alt me-2"></i> Recite & Get Feedback</h4>
            <span class="text-muted">Practice your recitation with AI feedback</span>
        </div>
        <div class="card-body">
            <div id="transcript-container">
                <?php if ($currentVideo && !empty($currentVideo['transcript'])): ?>
                    <div class="arabic-text">
                        <h6>Arabic Text:</h6>
                        <p class="arabic-content"><?php echo nl2br(htmlspecialchars($currentVideo['transcript'])); ?></p>
                    </div>
                <?php else: ?>
                    <p class="transcript-placeholder">
                        <i class="fas fa-info-circle me-2"></i>
                        Select a Surah from the Recitation Engine above to see the Arabic text and start practicing.
                    </p>
                <?php endif; ?>
            </div>
            <div class="recitation-controls">
                <button id="startRecitationBtn" class="btn btn-success btn-lg" <?php echo ($currentVideo && !empty($currentVideo['youtube_id'])) ? '' : 'disabled'; ?>>
                    <i class="fas fa-play-circle me-2"></i>Start Reciting
                </button>
                <?php if ($selectedContentId): ?>
                    <button id="resetSelectionBtn" class="btn btn-outline-secondary btn-lg ms-2" onclick="resetSelection()">
                        <i class="fas fa-undo me-2"></i>Reset Selection
                    </button>
                <?php endif; ?>
            </div>
            <div id="recitation-status" class="mt-3 text-center" style="display: none;">
                <span class="badge bg-primary">Listening...</span>
            </div>
        </div>
    </div>

    <!-- Part 4: Ranking System -->
    <div class="dashboard-card ranking-card">
        <div class="card-header">
            <h4><i class="fas fa-trophy me-2"></i> Your Current Rank</h4>
        </div>
        <div class="card-body">
            <ul class="ranking-list">
                <li><span>Ward</span><span class="rank-value">#<?php echo $user_rank['ward_rank']; ?></span></li>
                <li><span>LGEA</span><span class="rank-value">#<?php echo $user_rank['lgea_rank']; ?></span></li>
                <li><span>State</span><span class="rank-value">#<?php echo $user_rank['state_rank']; ?></span></li>
                <li><span>Country</span><span class="rank-value">#<?php echo $user_rank['country_rank']; ?></span></li>
            </ul>
            <a href="rankings.php" class="btn btn-outline-primary w-100 mt-3">View Full Leaderboard</a>
        </div>
    </div>
</div>


<?php require_once __DIR__ . '/../components/user_footer.php'; ?>

<script>
// Recitation Engine functionality
function loadSurah(contentId) {
    if (!contentId) {
        // Reset to default state
        resetSelection();
        return;
    }

    // Show loading state
    const videoPlayer = document.getElementById('videoPlayer');
    const transcriptContainer = document.getElementById('transcript-container');
    const startRecitationBtn = document.getElementById('startRecitationBtn');

    // Get selected option data
    const selectElement = document.getElementById('surahSelect');
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const youtubeId = selectedOption.getAttribute('data-youtube-id');
    const arabicText = selectedOption.getAttribute('data-arabic-text');

    if (youtubeId) {
        // Update video player
        videoPlayer.src = `https://www.youtube.com/embed/${youtubeId}`;

        // Update transcript
        if (arabicText) {
            transcriptContainer.innerHTML = `
                <div class="arabic-text">
                    <h6>Arabic Text:</h6>
                    <p class="arabic-content">${arabicText}</p>
                </div>
            `;
        }

        // Enable recitation button
        startRecitationBtn.disabled = false;

        // Update URL without page reload
        const newUrl = new URL(window.location);
        newUrl.searchParams.set('content_id', contentId);
        window.history.pushState({}, '', newUrl);

        // Show success message
        showNotification('Surah loaded successfully! You can now start reciting.', 'success');
    } else {
        showNotification('This Surah video is not available yet.', 'warning');
    }
}

function resetSelection() {
    // Reset select element
    document.getElementById('surahSelect').value = '';

    // Reset transcript
    document.getElementById('transcript-container').innerHTML = `
        <p class="transcript-placeholder">
            <i class="fas fa-info-circle me-2"></i>
            Select a Surah from the Recitation Engine above to see the Arabic text and start practicing.
        </p>
    `;

    // Disable recitation button
    document.getElementById('startRecitationBtn').disabled = true;

    // Update URL
    const newUrl = new URL(window.location);
    newUrl.searchParams.delete('content_id');
    window.history.pushState({}, '', newUrl);

    // Reset video player to default
    window.location.reload();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Mirror camera functionality
const mirrorVideo = document.getElementById('mirrorVideo');
const startCameraBtn = document.getElementById('startCameraBtn');
const cameraPlaceholder = document.getElementById('cameraPlaceholder');
let stream = null;

startCameraBtn.addEventListener('click', async () => {
    try {
        if (!stream) {
            stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'user' },
                audio: false
            });
            
            mirrorVideo.srcObject = stream;
            mirrorVideo.style.display = 'block';
            cameraPlaceholder.style.display = 'none';
            startCameraBtn.innerHTML = '<i class="fas fa-stop me-2"></i>Stop Camera';
            startCameraBtn.classList.remove('btn-primary');
            startCameraBtn.classList.add('btn-danger');
        } else {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
            mirrorVideo.style.display = 'none';
            cameraPlaceholder.style.display = 'block';
            startCameraBtn.innerHTML = '<i class="fas fa-video me-2"></i>Start Camera';
            startCameraBtn.classList.remove('btn-danger');
            startCameraBtn.classList.add('btn-primary');
        }
    } catch (error) {
        console.error('Error accessing camera:', error);
        alert('Unable to access camera. Please check permissions.');
    }
});

// Auto-update time-based elements
function updateTimeElements() {
    // Update any time-based content if needed
}

setInterval(updateTimeElements, 60000); // Update every minute

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script> 