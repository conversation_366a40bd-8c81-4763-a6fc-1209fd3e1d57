<?php
/**
 * User Dashboard for Recite! App
 * Clean, Mobile-First Dashboard with Simple Functionality
 */

require_once 'config/db_config.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Dashboard';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize variables
$message = '';
$error = '';
$conn = getConnection();

// SIMPLE UNLOCK PROCESS
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['unlock_content'])) {
    $contentId = intval($_POST['content_id'] ?? 0);
    
    if ($contentId > 0) {
        // Get content details
        $contentQuery = "SELECT * FROM content WHERE id = $contentId";
        $contentResult = $conn->query($contentQuery);
        
        if ($contentResult && $contentResult->num_rows > 0) {
            $content = $contentResult->fetch_assoc();
            $unlockPrice = floatval($content['unlock_price']);
            
            // Check if already unlocked
            $checkQuery = "SELECT id FROM unlocked_content WHERE user_id = $userId AND content_id = $contentId";
            $checkResult = $conn->query($checkQuery);
            
            if ($checkResult && $checkResult->num_rows > 0) {
                $message = 'Content already unlocked!';
            } else {
                $currentBalance = floatval($user['wallet_balance']);
                
                if ($currentBalance >= $unlockPrice) {
                    // Deduct from wallet
                    $updateWallet = "UPDATE users SET wallet_balance = wallet_balance - $unlockPrice WHERE id = $userId";
                    $walletResult = $conn->query($updateWallet);
                    
                    if ($walletResult) {
                        // Add unlock record
                        $unlockQuery = "INSERT INTO unlocked_content (user_id, content_id) VALUES ($userId, $contentId)";
                        $unlockResult = $conn->query($unlockQuery);
                        
                        if ($unlockResult) {
                            $message = 'Content unlocked successfully! ₦' . number_format($unlockPrice, 2) . ' deducted.';
                            $user = getUserById($userId); // Refresh user data
                        } else {
                            $error = 'Failed to unlock content.';
                        }
                    } else {
                        $error = 'Failed to update wallet.';
                    }
                } else {
                    $error = 'Insufficient wallet balance.';
                }
            }
        } else {
            $error = 'Content not found.';
        }
    } else {
        $error = 'Invalid content ID.';
    }
}

// Get available content
$contentResult = $conn->query("SELECT * FROM content ORDER BY surah_number");
$availableContent = [];
while ($row = $contentResult->fetch_assoc()) {
    $availableContent[] = $row;
}

// Get unlocked content
$unlockedResult = $conn->query("SELECT content_id FROM unlocked_content WHERE user_id = $userId");
$unlockedContent = [];
while ($row = $unlockedResult->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}

// Get selected content for Recitation Engine
$selectedContent = null;
if (isset($_GET['selected_content']) && !empty($_GET['selected_content'])) {
    $selectedContentId = intval($_GET['selected_content']);
    foreach ($availableContent as $content) {
        if ($content['id'] == $selectedContentId && in_array($content['id'], $unlockedContent)) {
            $selectedContent = $content;
            break;
        }
    }
}

// Get user recordings
$recordingsResult = $conn->query("SELECT * FROM screen_records WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$userRecordings = [];
while ($row = $recordingsResult->fetch_assoc()) {
    $userRecordings[] = $row;
}

$mirrorResult = $conn->query("SELECT * FROM mirror_recordings WHERE user_id = $userId ORDER BY created_at DESC LIMIT 5");
$mirrorRecordings = [];
while ($row = $mirrorResult->fetch_assoc()) {
    $mirrorRecordings[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem 0.5rem;
            }
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }





        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .wallet-balance {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            color: white;
        }

        .wallet-balance i {
            font-size: 1rem;
            color: #4CAF50;
        }

        .wallet-balance .balance-amount {
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
        }

        .wallet-balance .balance-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            opacity: 0.8;
            padding: 0.25rem;
            border-radius: 50%;
        }

        .wallet-balance .balance-toggle:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.1);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }



        .balance-amount {
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0;
        }

        /* Messages */
        .message {
            padding: 1.25rem 1.5rem;
            margin: 1.5rem 0;
            border-radius: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .message.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }

        .message.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }

        .message i {
            font-size: 1.2rem;
        }

        /* Cards */
        .card {
            background: white;
            border: none;
            border-radius: 16px;
            margin-bottom: 1.5rem;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            padding: 1.25rem 1.5rem;
            border-bottom: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-title i {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.875rem 1.25rem;
            border: none;
            border-radius: 12px;
            font-size: 0.95rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 48px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #333;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
        }

        .btn-sm {
            padding: 0.625rem 1rem;
            font-size: 0.85rem;
            min-height: 40px;
            border-radius: 10px;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
        }

        /* Content List */
        .content-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .content-item:hover {
            background: #f8f9fa;
            transform: translateX(4px);
        }

        .content-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .content-info h6 {
            margin: 0;
            font-weight: 600;
            color: #2c3e50;
        }

        .content-info small {
            color: #6c757d;
            font-weight: 500;
        }

        /* Video Container */
        .video-container {
            margin: 1.5rem 0;
            border-radius: 12px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .video-container iframe {
            width: 100%;
            height: 300px;
            border: none;
            border-radius: 12px;
        }

        .video-placeholder {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 16px;
            padding: 3rem 2rem;
            text-align: center;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .video-placeholder:hover {
            border-color: var(--primary);
            background: linear-gradient(135deg, #f1f3f4 0%, #e3e6ea 100%);
        }

        .video-placeholder i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.6;
        }

        .video-placeholder p {
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
        }

        iframe {
            width: 100%;
            height: 200px;
            border: none;
            border-radius: 8px;
        }

        /* Arabic Text */
        .arabic-text {
            direction: rtl;
            text-align: right;
            font-size: 1.3rem;
            line-height: 2.2;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            margin: 1rem 0;
            font-family: 'Amiri', 'Traditional Arabic', serif;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* Translation */
        .translation {
            background: linear-gradient(135deg, #f0f8f0 0%, #e8f5e8 100%);
            border: 2px solid #c3e6cb;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        /* Recitation Engine Specific Styles */
        .recitation-engine {
            position: relative;
        }

        .surah-selector-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }

        .selected-surah-info {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .video-container {
            position: relative;
            transition: all 0.3s ease;
        }

        .video-container iframe {
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        /* Enhanced form controls for Recitation Engine */
        #surahSelector {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.875rem 1rem;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        #surahSelector:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(26, 95, 63, 0.1);
            outline: none;
        }

        #surahSelector option {
            padding: 0.5rem;
            font-weight: 500;
        }

        /* Practice button styling */
        #startPracticeBtn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            transition: all 0.3s ease;
        }

        #startPracticeBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            padding: 0.5rem 0;
            z-index: 100;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text);
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .bottom-nav-item:hover {
            background: var(--secondary);
            color: var(--primary);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.1);
        }

        .bottom-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .bottom-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
            text-align: center;
        }

        /* Form Controls */
        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 0.95rem;
            background: white;
            min-height: 48px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(26, 95, 63, 0.1);
        }

        .form-control::placeholder {
            color: #adb5bd;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(26, 95, 63, 0.1);
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 768px;
                margin: 0 auto;
            }

            .grid-2 {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 767px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 1.3rem;
            }

            .user-info {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>



            <div class="header-right">
                <div class="wallet-balance">
                    <i class="fas fa-wallet"></i>
                    <span class="balance-amount" id="header-balance-display" data-balance="₦<?php echo number_format($user['wallet_balance'], 2); ?>">
                        ₦<?php echo number_format($user['wallet_balance'], 2); ?>
                    </span>
                    <button class="balance-toggle" onclick="toggleBalance()">
                        <i class="fas fa-eye" id="balance-eye"></i>
                    </button>
                </div>
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">3</div>
                </div>
            </div>
        </div>


    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="message success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="message error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Dashboard Grid -->
        <div class="grid grid-2">
            <!-- Selfie Mirror -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-video"></i> Selfie Mirror
                    </div>
                </div>
                <div class="card-body">
                    <div class="video-container">
                        <video id="selfie-video" style="width: 100%; height: 200px; background: #000; border-radius: 8px; display: none;"></video>
                        <div id="camera-placeholder" class="video-placeholder">
                            <i class="fas fa-camera" style="font-size: 2rem;"></i>
                            <p>Start camera to practice</p>
                        </div>
                    </div>

                    <div class="grid grid-2" style="margin-top: 1rem;">
                        <button id="start-camera-btn" class="btn btn-primary" onclick="startCamera()">
                            <i class="fas fa-camera"></i> Start Camera
                        </button>
                        <button id="stop-camera-btn" class="btn btn-warning" onclick="stopCamera()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Camera
                        </button>
                    </div>

                    <div class="grid grid-2" style="margin-top: 0.5rem;">
                        <button id="start-record-btn" class="btn btn-success" onclick="startRecording()" style="display: none;">
                            <i class="fas fa-record-vinyl"></i> Record
                        </button>
                        <button id="stop-record-btn" class="btn btn-warning" onclick="stopRecording()" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Record
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recitation Engine -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-play-circle"></i> Recitation Engine
                    </div>
                    <small style="color: rgba(255,255,255,0.8);">Select any Surah to practice recitation</small>
                </div>
                <div class="card-body">
                    <!-- Content Selection - Show ALL Surahs -->
                    <div style="margin-bottom: 1rem;">
                        <label for="surahSelector" style="font-weight: 600; margin-bottom: 0.5rem; display: block;">
                            <i class="fas fa-book-quran"></i> Choose a Surah to Practice:
                        </label>
                        <select id="surahSelector" class="form-control" onchange="loadSelectedSurah(this.value)">
                            <option value="">-- Select a Surah --</option>
                            <?php foreach ($availableContent as $content): ?>
                                <option value="<?php echo $content['id']; ?>"
                                        data-youtube-id="<?php echo htmlspecialchars($content['youtube_id']); ?>"
                                        data-arabic-text="<?php echo htmlspecialchars($content['arabic_text']); ?>"
                                        data-translation="<?php echo htmlspecialchars($content['translation'] ?? ''); ?>"
                                        data-surah-name="<?php echo htmlspecialchars($content['surah_name']); ?>"
                                        data-unlock-price="<?php echo $content['unlock_price']; ?>"
                                        data-is-unlocked="<?php echo in_array($content['id'], $unlockedContent) ? 'true' : 'false'; ?>"
                                        <?php echo (isset($_GET['selected_content']) && $_GET['selected_content'] == $content['id']) ? 'selected' : ''; ?>>
                                    <?php echo $content['surah_number']; ?>. <?php echo htmlspecialchars($content['surah_name']); ?>
                                    <?php if (!in_array($content['id'], $unlockedContent)): ?>
                                        (₦<?php echo number_format($content['unlock_price']); ?> to unlock)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Selected Surah Info -->
                    <div id="selectedSurahInfo" style="display: none; margin-bottom: 1rem;">
                        <div class="alert" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border: 1px solid #2196f3; border-radius: 8px; padding: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong id="selectedSurahName"></strong>
                                    <div id="selectedSurahStatus" style="font-size: 0.9rem; margin-top: 0.25rem;"></div>
                                </div>
                                <div id="unlockButtonContainer" style="display: none;">
                                    <button id="unlockButton" class="btn btn-primary btn-sm" onclick="unlockSelectedSurah()">
                                        <i class="fas fa-unlock"></i> Unlock Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Video Display -->
                    <div class="video-container">
                        <div id="videoPlayer" style="display: none;">
                            <iframe id="youtubeFrame" src="" allowfullscreen style="width: 100%; height: 300px; border: none; border-radius: 8px;"></iframe>
                        </div>

                        <div id="videoPlaceholder" class="video-placeholder">
                            <i class="fab fa-youtube" style="font-size: 2rem; color: #FF0000;"></i>
                            <p>Select a Surah above to watch the recitation video</p>
                            <small style="color: #666; margin-top: 0.5rem; display: block;">
                                📖 Choose from <?php echo count($availableContent); ?> available Surahs
                            </small>
                        </div>
                    </div>

                    <!-- Arabic Text and Translation -->
                    <div id="contentDisplay" style="display: none;">
                        <div id="arabicTextContainer" class="arabic-text" style="display: none;">
                            <!-- Arabic text will be loaded here -->
                        </div>

                        <div id="translationContainer" class="translation" style="display: none;">
                            <strong><i class="fas fa-language"></i> Translation:</strong><br>
                            <span id="translationText"></span>
                        </div>

                        <div style="margin-top: 1rem; text-align: center;">
                            <button id="startPracticeBtn" class="btn btn-success" style="display: none;" onclick="startPracticeMode()">
                                <i class="fas fa-microphone"></i> Start Practice Mode
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Library -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-book"></i> Content Library
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <small><?php echo count($unlockedContent); ?>/<?php echo count($availableContent); ?></small>
                        <a href="content_library.php" class="btn btn-sm" style="background: var(--primary); color: white; text-decoration: none;">
                            <i class="fas fa-eye"></i> View More
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php
                    $displayContent = array_slice($availableContent, 0, 3); // Show only first 3
                    foreach ($displayContent as $content):
                    ?>
                        <div class="content-item">
                            <div class="content-info">
                                <h6><?php echo htmlspecialchars($content['surah_name']); ?></h6>
                                <small>Surah <?php echo $content['surah_number']; ?></small>
                            </div>
                            <div>
                                <?php if (in_array($content['id'], $unlockedContent)): ?>
                                    <span class="btn btn-success btn-sm">
                                        <i class="fas fa-unlock"></i> Unlocked
                                    </span>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="content_id" value="<?php echo $content['id']; ?>">
                                        <input type="hidden" name="unlock_content" value="1">
                                        <button type="submit" class="btn btn-primary btn-sm"
                                                onclick="return confirm('Unlock <?php echo htmlspecialchars($content['surah_name']); ?> for ₦<?php echo number_format($content['unlock_price']); ?>?');">
                                            <i class="fas fa-lock"></i> ₦<?php echo number_format($content['unlock_price']); ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <?php if (count($availableContent) > 3): ?>
                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="content_library.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> View All <?php echo count($availableContent); ?> Surahs
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Your Recordings with Performance Ranking -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-trophy"></i> Top Recordings & Performance
                    </div>
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <?php
                        // Include points system
                        require_once 'includes/points_system.php';
                        $userPoints = getUserPointsSummary($userId);
                        $userRank = getUserRanking($userId);
                        ?>
                        <small>Rank: #<?php echo $userRank ?? 'N/A'; ?></small>
                        <small><?php echo $userPoints['total_points']; ?> pts</small>
                        <a href="leaderboard.php" class="btn btn-sm" style="background: #ff6b35; color: white; text-decoration: none;">
                            <i class="fas fa-trophy"></i> Leaderboard
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Performance Stats -->
                    <div class="performance-stats" style="margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;">
                        <div class="grid grid-2" style="gap: 1rem;">
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: var(--primary);">
                                    <?php echo $userPoints['total_points']; ?>
                                </div>
                                <small>Total Points</small>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #ff6b35;">
                                    <?php echo $userPoints['current_streak']; ?>
                                </div>
                                <small>Current Streak</small>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 0.5rem;">
                            <small>🏆 Best Streak: <?php echo $userPoints['best_streak']; ?> | 📊 Total Recitations: <?php echo $userPoints['total_recitations']; ?></small>
                        </div>
                    </div>

                    <!-- Latest Recording -->
                    <?php
                    // Get user's latest recording with performance data
                    $latestQuery = "
                        SELECT sr.*, re.accuracy_score, re.duration_seconds, re.points_earned,
                               DATE_FORMAT(sr.created_at, '%M %d, %Y') as formatted_date
                        FROM screen_records sr
                        LEFT JOIN recitation_evaluations re ON sr.id = re.recording_id AND re.recording_type = 'screen_record'
                        WHERE sr.user_id = ?
                        ORDER BY sr.created_at DESC
                        LIMIT 1
                    ";
                    $stmt = $conn->prepare($latestQuery);
                    $stmt->bind_param("i", $userId);
                    $stmt->execute();
                    $latestRecording = $stmt->get_result()->fetch_assoc();
                    ?>

                    <?php if ($latestRecording): ?>
                        <h6 style="margin-bottom: 1rem;"><i class="fas fa-star"></i> Latest Recording</h6>
                        <div class="content-item" style="border-left: 3px solid var(--primary); padding-left: 1rem;">
                            <div class="content-info">
                                <div style="display: flex; justify-content: space-between; align-items: start;">
                                    <div style="flex: 1;">
                                        <h6 style="margin: 0;">
                                            <?php echo htmlspecialchars($latestRecording['title'] ?? 'Recording'); ?>
                                        </h6>
                                        <small><?php echo $latestRecording['formatted_date']; ?></small>
                                    </div>
                                    <div style="text-align: right;">
                                        <?php if ($latestRecording['points_earned']): ?>
                                            <div style="background: var(--primary); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.7rem; margin-bottom: 0.25rem;">
                                                +<?php echo $latestRecording['points_earned']; ?> pts
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($latestRecording['accuracy_score']): ?>
                                            <div style="font-size: 0.7rem; color: #666;">
                                                <?php echo $latestRecording['accuracy_score']; ?>% accuracy
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($latestRecording['duration_seconds']): ?>
                                            <div style="font-size: 0.7rem; color: #666;">
                                                <?php echo gmdate("i:s", $latestRecording['duration_seconds']); ?> duration
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="streams.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-stream"></i> View All Recordings
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="video-placeholder">
                            <i class="fas fa-video" style="font-size: 2rem;"></i>
                            <p>No recordings yet. Start practicing to earn points!</p>
                            <small style="color: #666; margin-top: 0.5rem; display: block;">
                                🏆 Record at least 5 minutes to earn points • Get likes/comments for bonus points
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <a href="dashboard.php" class="bottom-nav-item active">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="streams.php" class="bottom-nav-item">
            <i class="fas fa-video"></i>
            <span>Streams</span>
        </a>
        
        <a href="community.php" class="bottom-nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="wallet.php" class="bottom-nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="profile.php" class="bottom-nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </nav>

    <!-- JavaScript for Recitation Engine and Camera -->
    <script>
        // Recitation Engine Variables
        let selectedSurahData = null;
        let currentUnlockPrice = 0;
        let currentSurahId = null;

        // Load Selected Surah Function
        function loadSelectedSurah(surahId) {
            const selector = document.getElementById('surahSelector');
            const selectedOption = selector.options[selector.selectedIndex];

            // Reset display if no selection
            if (!surahId || surahId === '') {
                resetRecitationEngine();
                return;
            }

            // Get data from selected option
            const youtubeId = selectedOption.getAttribute('data-youtube-id');
            const arabicText = selectedOption.getAttribute('data-arabic-text');
            const translation = selectedOption.getAttribute('data-translation');
            const surahName = selectedOption.getAttribute('data-surah-name');
            const unlockPrice = parseFloat(selectedOption.getAttribute('data-unlock-price'));
            const isUnlocked = selectedOption.getAttribute('data-is-unlocked') === 'true';

            // Store current selection
            selectedSurahData = {
                id: surahId,
                youtubeId: youtubeId,
                arabicText: arabicText,
                translation: translation,
                surahName: surahName,
                unlockPrice: unlockPrice,
                isUnlocked: isUnlocked
            };

            currentSurahId = surahId;
            currentUnlockPrice = unlockPrice;

            // Show selected Surah info
            showSelectedSurahInfo(selectedSurahData);

            // Load video if available
            if (youtubeId && youtubeId.trim() !== '') {
                loadYouTubeVideo(youtubeId);
            } else {
                showVideoPlaceholder('Video not available for this Surah yet');
            }

            // Show content if unlocked
            if (isUnlocked) {
                showSurahContent(arabicText, translation);
            } else {
                hideSurahContent();
            }

            // Update URL without page reload
            const newUrl = new URL(window.location);
            newUrl.searchParams.set('selected_content', surahId);
            window.history.pushState({}, '', newUrl);
        }

        // Show Selected Surah Information
        function showSelectedSurahInfo(data) {
            const infoContainer = document.getElementById('selectedSurahInfo');
            const nameElement = document.getElementById('selectedSurahName');
            const statusElement = document.getElementById('selectedSurahStatus');
            const unlockContainer = document.getElementById('unlockButtonContainer');
            const unlockButton = document.getElementById('unlockButton');

            nameElement.textContent = data.surahName;

            if (data.isUnlocked) {
                statusElement.innerHTML = '<span style="color: #28a745;"><i class="fas fa-unlock"></i> Unlocked - Ready to practice</span>';
                unlockContainer.style.display = 'none';
            } else {
                statusElement.innerHTML = `<span style="color: #ffc107;"><i class="fas fa-lock"></i> Requires ₦${data.unlockPrice.toLocaleString()} to unlock full content</span>`;
                unlockContainer.style.display = 'block';
                unlockButton.innerHTML = `<i class="fas fa-unlock"></i> Unlock for ₦${data.unlockPrice.toLocaleString()}`;
            }

            infoContainer.style.display = 'block';
        }

        // Load YouTube Video
        function loadYouTubeVideo(youtubeId) {
            const videoPlayer = document.getElementById('videoPlayer');
            const youtubeFrame = document.getElementById('youtubeFrame');
            const placeholder = document.getElementById('videoPlaceholder');

            youtubeFrame.src = `https://www.youtube.com/embed/${youtubeId}?rel=0&modestbranding=1`;
            videoPlayer.style.display = 'block';
            placeholder.style.display = 'none';

            showNotification('Video loaded successfully! You can now watch the recitation.', 'success');
        }

        // Show Video Placeholder
        function showVideoPlaceholder(message = 'Select a Surah above to watch the recitation video') {
            const videoPlayer = document.getElementById('videoPlayer');
            const placeholder = document.getElementById('videoPlaceholder');

            placeholder.querySelector('p').textContent = message;
            videoPlayer.style.display = 'none';
            placeholder.style.display = 'block';
        }

        // Show Surah Content (Arabic text and translation)
        function showSurahContent(arabicText, translation) {
            const contentDisplay = document.getElementById('contentDisplay');
            const arabicContainer = document.getElementById('arabicTextContainer');
            const translationContainer = document.getElementById('translationContainer');
            const translationText = document.getElementById('translationText');
            const practiceBtn = document.getElementById('startPracticeBtn');

            if (arabicText && arabicText.trim() !== '') {
                arabicContainer.innerHTML = arabicText;
                arabicContainer.style.display = 'block';
            } else {
                arabicContainer.style.display = 'none';
            }

            if (translation && translation.trim() !== '') {
                translationText.textContent = translation;
                translationContainer.style.display = 'block';
            } else {
                translationContainer.style.display = 'none';
            }

            contentDisplay.style.display = 'block';
            practiceBtn.style.display = 'inline-flex';
        }

        // Hide Surah Content
        function hideSurahContent() {
            const contentDisplay = document.getElementById('contentDisplay');
            contentDisplay.style.display = 'none';
        }

        // Reset Recitation Engine
        function resetRecitationEngine() {
            document.getElementById('selectedSurahInfo').style.display = 'none';
            document.getElementById('contentDisplay').style.display = 'none';
            showVideoPlaceholder();
            selectedSurahData = null;
            currentSurahId = null;
            currentUnlockPrice = 0;

            // Clear URL parameter
            const newUrl = new URL(window.location);
            newUrl.searchParams.delete('selected_content');
            window.history.pushState({}, '', newUrl);
        }

        // Unlock Selected Surah
        function unlockSelectedSurah() {
            if (!currentSurahId || currentUnlockPrice <= 0) {
                showNotification('Invalid selection. Please try again.', 'error');
                return;
            }

            const confirmMessage = `Unlock ${selectedSurahData.surahName} for ₦${currentUnlockPrice.toLocaleString()}?\n\nThis will deduct the amount from your wallet balance.`;

            if (confirm(confirmMessage)) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                const contentIdInput = document.createElement('input');
                contentIdInput.type = 'hidden';
                contentIdInput.name = 'content_id';
                contentIdInput.value = currentSurahId;

                const unlockInput = document.createElement('input');
                unlockInput.type = 'hidden';
                unlockInput.name = 'unlock_content';
                unlockInput.value = '1';

                form.appendChild(contentIdInput);
                form.appendChild(unlockInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Start Practice Mode
        function startPracticeMode() {
            if (!selectedSurahData || !selectedSurahData.isUnlocked) {
                showNotification('Please unlock this Surah first to start practice mode.', 'warning');
                return;
            }

            showNotification('Practice mode activated! Start your camera and begin reciting.', 'info');

            // Scroll to camera section
            document.querySelector('.card:first-child').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // Show Notification Function
        function showNotification(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            alertDiv.style.maxWidth = '400px';
            alertDiv.style.borderRadius = '12px';
            alertDiv.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';

            const iconMap = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            alertDiv.innerHTML = `
                <i class="${iconMap[type] || iconMap.info}" style="margin-right: 0.5rem;"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(alertDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check if there's a selected content from URL
            const urlParams = new URLSearchParams(window.location.search);
            const selectedContent = urlParams.get('selected_content');

            if (selectedContent) {
                const selector = document.getElementById('surahSelector');
                selector.value = selectedContent;
                loadSelectedSurah(selectedContent);
            }
        });

        // Camera functionality
        let currentStream = null;
        let mediaRecorder = null;
        let recordedChunks = [];

        // Start Camera
        function startCamera() {
            // Request both video and audio with specific constraints for better audio quality
            const constraints = {
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    frameRate: { ideal: 30 }
                },
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100,
                    channelCount: 2
                }
            };

            navigator.mediaDevices.getUserMedia(constraints)
                .then(function(stream) {
                    currentStream = stream;
                    const video = document.getElementById('selfie-video');
                    const placeholder = document.getElementById('camera-placeholder');

                    video.srcObject = stream;
                    video.play();
                    video.style.display = 'block';
                    placeholder.style.display = 'none';

                    document.getElementById('start-camera-btn').style.display = 'none';
                    document.getElementById('stop-camera-btn').style.display = 'block';
                    document.getElementById('start-record-btn').style.display = 'block';

                    console.log('Camera and microphone started successfully');
                    console.log('Audio tracks:', stream.getAudioTracks().length);
                    console.log('Video tracks:', stream.getVideoTracks().length);
                })
                .catch(function(error) {
                    alert('Camera or microphone access denied. Please allow both camera and microphone permissions for recording.');
                    console.error('Camera/Microphone error:', error);
                });
        }

        // Stop Camera
        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');

            video.style.display = 'none';
            placeholder.style.display = 'block';

            document.getElementById('start-camera-btn').style.display = 'block';
            document.getElementById('stop-camera-btn').style.display = 'none';
            document.getElementById('start-record-btn').style.display = 'none';
            document.getElementById('stop-record-btn').style.display = 'none';
        }

        // Start Recording
        function startRecording() {
            if (currentStream) {
                recordedChunks = [];

                // Check audio tracks before recording
                const audioTracks = currentStream.getAudioTracks();
                const videoTracks = currentStream.getVideoTracks();

                console.log('Starting recording with:', audioTracks.length, 'audio tracks and', videoTracks.length, 'video tracks');

                if (audioTracks.length === 0) {
                    alert('No microphone detected. Please ensure microphone access is granted and try again.');
                    return;
                }

                // Configure MediaRecorder with audio-optimized settings
                let options = {};

                // Try different codec combinations for best audio support
                if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
                    options.mimeType = 'video/webm;codecs=vp9,opus';
                } else if (MediaRecorder.isTypeSupported('video/webm;codecs=vp8,opus')) {
                    options.mimeType = 'video/webm;codecs=vp8,opus';
                } else if (MediaRecorder.isTypeSupported('video/webm;codecs=opus')) {
                    options.mimeType = 'video/webm;codecs=opus';
                } else {
                    options.mimeType = 'video/webm';
                }

                // Set bitrates for quality audio
                options.videoBitsPerSecond = 2500000; // 2.5 Mbps for video
                options.audioBitsPerSecond = 128000;  // 128 kbps for audio

                console.log('Using MediaRecorder options:', options);

                mediaRecorder = new MediaRecorder(currentStream, options);

                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                        console.log('Data chunk received:', event.data.size, 'bytes');
                    }
                };

                mediaRecorder.onstop = function() {
                    console.log('Recording stopped. Total chunks:', recordedChunks.length);

                    const blob = new Blob(recordedChunks, { type: options.mimeType || 'video/webm' });
                    const filename = 'recitation-' + new Date().getTime() + '.webm';

                    console.log('Created blob:', blob.size, 'bytes, type:', blob.type);

                    // Save to database only (no auto-download)
                    saveRecordingToDatabase(blob, filename);

                    // Show success message
                    showMessage('Recording with audio saved successfully!', 'success');
                };

                mediaRecorder.onerror = function(event) {
                    console.error('MediaRecorder error:', event.error);
                    alert('Recording error: ' + event.error.message);
                };

                // Start recording with data collection every second
                mediaRecorder.start(1000);

                document.getElementById('start-record-btn').style.display = 'none';
                document.getElementById('stop-record-btn').style.display = 'block';

                console.log('Recording started successfully');
            } else {
                alert('Please start the camera first before recording.');
            }
        }

        // Stop Recording
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();

                document.getElementById('start-record-btn').style.display = 'block';
                document.getElementById('stop-record-btn').style.display = 'none';
            }
        }

        // Show message function
        function showMessage(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Save recording to database
        function saveRecordingToDatabase(blob, filename) {
            const formData = new FormData();
            formData.append('recording', blob, filename);
            formData.append('action', 'save_recording');

            fetch('save_recording.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Recording saved successfully!');
                    // Refresh the page to show new recording
                    window.location.reload();
                } else {
                    alert('Failed to save recording: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error saving recording:', error);
                alert('Error saving recording');
            });
        }

        // Toggle balance visibility
        let balanceVisible = true;
        function toggleBalance() {
            const balanceDisplay = document.getElementById('header-balance-display');
            const eyeIcon = document.getElementById('balance-eye');

            if (balanceVisible) {
                balanceDisplay.textContent = '****';
                eyeIcon.className = 'fas fa-eye-slash';
                balanceVisible = false;
            } else {
                balanceDisplay.textContent = '₦<?php echo number_format($user['wallet_balance'], 2); ?>';
                eyeIcon.className = 'fas fa-eye';
                balanceVisible = true;
            }
        }

        // Show notifications
        function showNotifications() {
            alert('Notifications:\n• New Surah unlocked!\n• Recording saved successfully\n• Welcome to RECITE!');
        }
    </script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
