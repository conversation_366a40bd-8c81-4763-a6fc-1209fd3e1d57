<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/points_system.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

$user_id = $_SESSION['user_id'];
$points_to_buy = filter_input(INPUT_POST, 'points', FILTER_VALIDATE_INT);

if (!$points_to_buy || $points_to_buy <= 0) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid number of points to buy.']);
    exit;
}

// --- Configuration ---
define('NAIRA_PER_POINT_BUY', 10);
$cost = $points_to_buy * NAIRA_PER_POINT_BUY;

$conn = getConnection();

try {
    $conn->begin_transaction();

    // 1. Get current wallet balance and lock the row for update
    $stmt = $conn->prepare("SELECT balance FROM user_stats WHERE user_id = ? FOR UPDATE");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    $current_balance = $result['balance'] ?? 0;

    if ($current_balance < $cost) {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'Insufficient wallet balance.']);
        exit;
    }

    // 2. Deduct cost from wallet balance
    $new_balance = $current_balance - $cost;
    $update_wallet_stmt = $conn->prepare("UPDATE user_stats SET balance = ? WHERE user_id = ?");
    $update_wallet_stmt->bind_param("di", $new_balance, $user_id);
    $update_wallet_stmt->execute();

    // 3. Add points to user
    awardPoints($user_id, $points_to_buy, 'point_purchase', "Purchased {$points_to_buy} points");

    // 4. Log the wallet transaction
    $trans_stmt = $conn->prepare("INSERT INTO transactions (user_id, amount, type, description) VALUES (?, ?, 'point_purchase', ?)");
    $negative_cost = -$cost;
    $description = "Purchase of {$points_to_buy} points";
    $trans_stmt->bind_param("ids", $user_id, $negative_cost, $description);
    $trans_stmt->execute();

    $conn->commit();

    // 5. Fetch updated points balance
    $points_summary = getUserPointsSummary($user_id);
    $new_points_balance = $points_summary['total_points'] ?? 0;

    echo json_encode([
        'success' => true, 
        'message' => 'Successfully purchased ' . $points_to_buy . ' points!', 
        'data' => [
            'new_balance' => $new_balance,
            'new_points_balance' => $new_points_balance
        ]
    ]);

} catch (Exception $e) {
    $conn->rollback();
    logError("Buy Points API Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request.']);
}

$conn->close();
?>