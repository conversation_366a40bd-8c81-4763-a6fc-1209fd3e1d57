<?php
/**
 * Test Headers Fix
 * This script tests if the headers issue has been resolved
 */

// Start output buffering
ob_start();

echo "Testing header fix...\n";

try {
    require_once '../config/db_config.php';
    echo "✅ Config loaded successfully\n";
    
    // Test database connection
    $conn = getConnection();
    echo "✅ Database connection successful\n";
    
    // Test admin functions
    if (function_exists('isAdmin')) {
        echo "✅ isAdmin function exists\n";
    } else {
        echo "❌ isAdmin function missing\n";
    }
    
    if (function_exists('generateCSRFToken')) {
        $token = generateCSRFToken();
        echo "✅ CSRF token generated: " . substr($token, 0, 10) . "...\n";
    } else {
        echo "❌ generateCSRFToken function missing\n";
    }
    
    // Test session
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "✅ Session is active\n";
    } else {
        echo "❌ Session not active\n";
    }
    
    echo "\n🎉 All tests passed! Headers issue should be fixed.\n";
    echo "\nYou can now try logging in at: admin/login.php\n";
    echo "Username: admin\n";
    echo "Password: 1@3Usazladan\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nIf you see database errors, make sure to:\n";
    echo "1. Import the fixed SQL file: database/recite_app_fixed.sql\n";
    echo "2. Update database credentials in config/server_config.php\n";
    echo "3. Run setup_production.php\n";
}

// Clean output buffer (this simulates what happens before header redirects)
$output = ob_get_clean();

// Display results
echo "<pre>$output</pre>";

// Test if we can send headers now
if (!headers_sent()) {
    echo "<div style='color: green; font-weight: bold;'>✅ Headers can be sent - redirect issue is FIXED!</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ Headers already sent - there may still be an issue</div>";
}
?>
