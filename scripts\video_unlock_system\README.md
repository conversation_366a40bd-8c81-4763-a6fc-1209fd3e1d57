# Video Unlock System

A comprehensive video unlock system for the RECITE app that allows users to unlock videos for 3 Naira, with automatic payment splitting (1 Naira to creator, 2 Naira to admin).

## 🎯 Features

- **Secure Payment Processing**: 3 Naira unlock fee with automatic wallet deduction
- **Payment Split**: 1 Naira to creator, 2 Naira to admin
- **Transaction Tracking**: Complete audit trail for all unlock transactions
- **Admin Dashboard**: Real-time monitoring of unlock transactions and earnings
- **User Interface**: Seamless unlock experience with confirmation dialogs
- **Real-time Updates**: Instant wallet balance updates and video access

## 📁 File Structure

```
scripts/video_unlock_system/
├── migrate_video_unlock.php    # Database migration script
├── setup_video_unlock.php      # Complete setup script
├── create_test_data.php        # Test data creation (optional)
└── README.md                   # This file

api/
└── unlock_video.php            # API endpoint for unlock operations

admin/
├── video-unlock-transactions.php    # Admin transaction dashboard
└── get-transaction-details.php      # Transaction details helper

assets/js/
└── video-unlock.js             # Frontend JavaScript functionality
```

## 🚀 Quick Setup

1. **Run the setup script**:
   ```bash
   cd scripts/video_unlock_system/
   php setup_video_unlock.php
   ```

2. **Include JavaScript in your pages**:
   ```html
   <script src="/assets/js/video-unlock.js"></script>
   ```

3. **Add user ID to your pages**:
   ```html
   <div data-user-id="<?php echo $user_id; ?>">
       <!-- Your content -->
   </div>
   ```

4. **Use locked video elements**:
   ```html
   <div class="video-container position-relative" 
        data-recording-id="1" 
        data-recording-type="video">
       <div class="video-lock-overlay position-absolute w-100 h-100 d-flex align-items-center justify-content-center" 
            style="background: rgba(0,0,0,0.7); z-index: 10;">
           <div class="text-center text-white">
               <i class="fas fa-lock fa-3x mb-3"></i>
               <h5>Video Locked</h5>
               <p>Unlock this video to watch</p>
               <button class="btn btn-primary unlock-video-btn" 
                       data-recording-id="1" 
                       data-recording-type="video" 
                       data-unlock-price="3.00">
                   <i class="fas fa-unlock"></i> Unlock for ₦3.00
               </button>
           </div>
       </div>
       <div class="video-content" style="display: none;">
           <!-- Video content here -->
       </div>
   </div>
   ```

## 💰 Payment Structure

- **Total Cost**: ₦3.00 per video unlock
- **Creator Earnings**: ₦1.00 (33.33%)
- **Admin Earnings**: ₦2.00 (66.67%)

## 📊 Database Tables

### New Tables Created:
- `video_unlocks`: Tracks all unlock transactions
- `admin_earnings`: Records admin earnings from unlocks

### Modified Tables:
- `videos`: Added `unlock_price`, `is_locked`, `view_count`
- `screen_records`: Added `unlock_price`, `is_locked`, `view_count`
- `mirror_recordings`: Added `unlock_price`, `is_locked`, `view_count`
- `wallet_transactions`: Added new transaction types

## 🔧 API Endpoints

### POST /api/unlock_video.php

**Unlock a video**:
```json
{
    "action": "unlock",
    "user_id": 123,
    "recording_id": 456,
    "recording_type": "video"
}
```

**Check unlock status**:
```json
{
    "action": "check_status",
    "user_id": 123,
    "recording_id": 456,
    "recording_type": "video"
}
```

## 🎛️ Admin Features

### Video Unlock Transactions Dashboard
- **Location**: `/admin/video-unlock-transactions.php`
- **Features**:
  - View all unlock transactions
  - Filter by status, date, and user
  - Summary statistics
  - Detailed transaction information
  - Export capabilities

### Transaction Details
- Complete transaction history
- User and creator information
- Related wallet transactions
- Payment breakdown

## 🔒 Security Features

- **Transaction Validation**: Prevents duplicate unlocks
- **Wallet Balance Checks**: Ensures sufficient funds
- **Database Transactions**: Atomic operations for data integrity
- **Input Validation**: Sanitized user inputs
- **Session Management**: Secure admin access

## 📱 User Experience

- **Confirmation Dialogs**: Clear pricing information
- **Loading States**: Visual feedback during processing
- **Success/Error Notifications**: User-friendly messages
- **Real-time Updates**: Instant balance and access updates
- **Responsive Design**: Works on all devices

## 🧪 Testing

1. **Create test data**:
   ```bash
   php create_test_data.php
   ```

2. **Test unlock flow**:
   - Create a test user with wallet balance
   - Upload a test video
   - Attempt to unlock the video
   - Verify payment split and transaction records

## 🔄 Maintenance

### Regular Tasks:
- Monitor transaction logs
- Review admin earnings
- Check for failed transactions
- Update unlock prices if needed

### Backup:
- Regular database backups
- Transaction log exports
- Admin earnings reports

## 🐛 Troubleshooting

### Common Issues:

1. **"User not found" error**:
   - Check if user ID is correctly passed
   - Verify user exists in database

2. **"Insufficient balance" error**:
   - Check user's wallet balance
   - Ensure minimum ₦3.00 balance

3. **"Video already unlocked" error**:
   - Check existing unlock records
   - Clear duplicate entries if needed

4. **Transaction failures**:
   - Check database connection
   - Verify table structure
   - Review error logs

## 📞 Support

For technical support or questions about the video unlock system, please refer to the main application documentation or contact the development team.

---

**Version**: 1.0.0  
**Last Updated**: August 2025  
**Compatibility**: PHP 8.0+, MySQL 8.0+ 