<?php
$page_title = 'Manage Users';
require_once __DIR__ . '/../components/admin_header.php';

// Add CSRF token to page
echo '<meta name="csrf-token" content="' . generateCSRFToken() . '">';

$conn = getConnection();
$message = '';
$error = '';

// Handle user actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $userId = intval($_POST['user_id'] ?? 0);

    if (!isset($_POST['csrf_token']) || !validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        // Handle status change action
        if ($action === 'change_status') {
            $userId = intval($_POST['user_id'] ?? 0);
            $newStatus = intval($_POST['new_status'] ?? 0);

            if ($userId > 0) {
                try {
                    if ($newStatus === 1) {
                        // Set both is_active and payment_verified to 1 (activate fully)
                        $updateStmt = $conn->prepare("UPDATE users SET is_active = 1, payment_verified = 1 WHERE id = ?");
                        $updateStmt->bind_param("i", $userId);
                    } else {
                        // Deactivate: set only is_active to 0
                        $updateStmt = $conn->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
                        $updateStmt->bind_param("i", $userId);
                    }

                    if ($updateStmt->execute()) {
                        $statusText = $newStatus ? 'activated' : 'deactivated';
                        $message = "User has been $statusText successfully!";
                        $messageType = 'success';
                    } else {
                        $error = 'Failed to update user status';
                    }
                    $updateStmt->close();
                } catch (Exception $e) {
                    $error = 'Error updating user status: ' . $e->getMessage();
                }
            } else {
                $error = 'Invalid user ID';
            }
        }

        // Handle add new user action
        elseif ($action === 'add_user') {
            $fullName = sanitize($_POST['full_name'] ?? '');
            $email = sanitize($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';
            $phoneNumber = sanitize($_POST['phone_number'] ?? '');
            $ward = sanitize($_POST['ward'] ?? '');
            $state = sanitize($_POST['state'] ?? '');
            $country = sanitize($_POST['country'] ?? 'Nigeria');
            $referralCode = sanitize($_POST['referral_code'] ?? '');
            $initialBalance = floatval($_POST['initial_balance'] ?? 0);

            // Validation
            $errors = [];
            if (empty($fullName)) $errors[] = 'Full name is required';
            if (empty($email)) $errors[] = 'Email is required';
            if (!validateEmail($email)) $errors[] = 'Invalid email format';
            if (empty($password)) $errors[] = 'Password is required';
            if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';

            if (empty($errors)) {
                try {
                    // Check if email already exists
                    $checkStmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                    $checkStmt->bind_param("s", $email);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();

                    if ($result->num_rows > 0) {
                        $error = 'Email already exists';
                    } else {
                        // Generate unique referral code if not provided
                        if (empty($referralCode)) {
                            do {
                                $referralCode = generateReferralCode();
                                $refCheckStmt = $conn->prepare("SELECT id FROM users WHERE referral_code = ?");
                                $refCheckStmt->bind_param("s", $referralCode);
                                $refCheckStmt->execute();
                                $refExists = $refCheckStmt->get_result()->num_rows > 0;
                                $refCheckStmt->close();
                            } while ($refExists);
                        }

                        // Hash password and insert user
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $insertStmt = $conn->prepare("
                            INSERT INTO users (full_name, email, password_hash, ward, state, country, referral_code, wallet_balance, is_active, payment_verified, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1, NOW())
                        ");
                        $insertStmt->bind_param("sssssssd", $fullName, $email, $hashedPassword, $ward, $state, $country, $referralCode, $initialBalance);

                        if ($insertStmt->execute()) {
                            $message = "User '$fullName' created successfully!";
                        } else {
                            $error = 'Failed to create user: ' . $insertStmt->error;
                        }
                        $insertStmt->close();
                    }
                    $checkStmt->close();
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
            } else {
                $error = implode('<br>', $errors);
            }
        }
        // Handle existing user actions
        elseif ($userId > 0) {
            switch ($action) {
            case 'update_user':
                $fullName = sanitize($_POST['full_name']);
                $email = sanitize($_POST['email']);
                $walletBalance = floatval($_POST['wallet_balance']);
                $pointsBalance = intval($_POST['points_balance']);
                $isBlocked = isset($_POST['is_blocked']) ? 1 : 0;

                try {
                    $stmt = $conn->prepare("UPDATE users SET full_name = ?, email = ?, wallet_balance = ?, points_balance = ?, is_blocked = ? WHERE id = ?");
                    $stmt->bind_param("ssddii", $fullName, $email, $walletBalance, $pointsBalance, $isBlocked, $userId);

                    if ($stmt->execute()) {
                        $message = 'User updated successfully!';
                    } else {
                        $error = 'Failed to update user.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_user':
                try {
                    // First delete related records
                    $conn->query("DELETE FROM transactions WHERE user_id = $userId");
                    $conn->query("DELETE FROM unlocked_content WHERE user_id = $userId");
                    $conn->query("DELETE FROM screen_records WHERE user_id = $userId");
                    $conn->query("DELETE FROM streams WHERE user_id = $userId");

                    // Then delete the user
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                    $stmt->bind_param("i", $userId);

                    if ($stmt->execute()) {
                        $message = 'User deleted successfully!';
                    } else {
                        $error = 'Failed to delete user.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'toggle_block':
                try {
                    $stmt = $conn->prepare("UPDATE users SET is_blocked = NOT is_blocked WHERE id = ?");
                    $stmt->bind_param("i", $userId);

                    if ($stmt->execute()) {
                        $message = 'User status updated successfully!';
                    } else {
                        $error = 'Failed to update user status.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
            }
        }
    }
}

// Fetch users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

$search = $_GET['search'] ?? '';

try {
    $conn = getConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

// Build search query safely
$where_sql = '';
$params = [];
$types = '';

if (!empty($search)) {
    $where_sql = 'WHERE (full_name LIKE ? OR email LIKE ?)';
    $search_param = "%$search%";
    $params = [$search_param, $search_param];
    $types = 'ss';
}

// Get total count for pagination
if (!empty($search)) {
    $count_stmt = $conn->prepare("SELECT COUNT(*) as count FROM users $where_sql");
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_result = $count_stmt->get_result();
    $total_users = $total_result->fetch_assoc()['count'];
    $count_stmt->close();
} else {
    $total_result = $conn->query("SELECT COUNT(*) as count FROM users");
    $total_users = $total_result->fetch_assoc()['count'];
}

$total_pages = ceil($total_users / $limit);

// Fetch users
$query = "SELECT * FROM users $where_sql ORDER BY created_at DESC LIMIT ? OFFSET ?";
if (!empty($search)) {
    $stmt = $conn->prepare($query);
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $users_result = $stmt->get_result();
    $users = $users_result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
} else {
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ii', $limit, $offset);
    $stmt->execute();
    $users_result = $stmt->get_result();
    $users = $users_result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
}

$conn->close();

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
    $users = [];
    $total_users = 0;
    $total_pages = 0;
    logError("Users page error: " . $e->getMessage());
}
?>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Debug Information for Live Server -->
<?php if (isset($error) && !empty($error)): ?>
<div class="alert alert-info">
    <h6><i class="fas fa-info-circle me-2"></i>Debug Information:</h6>
    <small>
        Total Users: <?php echo $total_users ?? 0; ?><br>
        Search Query: <?php echo htmlspecialchars($search); ?><br>
        Current Page: <?php echo $page; ?><br>
        <a href="debug-live-server.php" class="btn btn-sm btn-primary mt-2">
            <i class="fas fa-bug me-1"></i>Run Full Diagnostic
        </a>
    </small>
</div>
<?php endif; ?>

<!-- Search and Filter Bar -->
<div class="data-table-container mb-4">
    <div class="table-header">
        <h4 class="table-title">All Users</h4>
        <div class="d-flex gap-2 align-items-center">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus"></i> Add New User
            </button>
            <form action="users.php" method="GET" class="d-flex gap-2">
                <input type="text" name="search" class="form-control" placeholder="Search by name or email..." value="<?php echo htmlspecialchars($search); ?>">
                <button type="submit" class="btn btn-primary">Search</button>
            </form>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="data-table-container">
    <div class="table-responsive">
        <table class="modern-table" style="width:100%; border-collapse:separate; border-spacing:0;">
            <thead style="background: #e6f9ed; color: #15803d;">
                <tr>
                    <th style="padding: 0.9rem 0.7rem;">User</th>
                    <th style="padding: 0.9rem 0.7rem;">Email</th>
                    <th style="padding: 0.9rem 0.7rem;">Wallet</th>
                    <th style="padding: 0.9rem 0.7rem;">Points</th>
                    <th style="padding: 0.9rem 0.7rem;">Status</th>
                    <th style="padding: 0.9rem 0.7rem;">Joined</th>
                    <th class="text-end" style="padding: 0.9rem 0.7rem;">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($users)): ?>
                    <tr><td colspan="7" class="text-center py-5">No users found.</td></tr>
                <?php else: ?>
                    <?php foreach ($users as $user): ?>
                    <tr style="background: <?php echo $user['id'] % 2 == 0 ? '#f6fff9' : '#fff'; ?>;">
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle;">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar" style="background: #15803d; color: #fff; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 1.1rem;">
                                    <?php echo strtoupper(substr($user['full_name'] ?? $user['email'], 0, 1)); ?>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-semibold" style="color: #15803d;"><?php echo htmlspecialchars($user['full_name'] ?? 'N/A'); ?></div>
                                    <small class="text-muted">ID: <?php echo $user['id']; ?></small>
                                </div>
                            </div>
                        </td>
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle; color: #15803d;"><?php echo htmlspecialchars($user['email']); ?></td>
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle;">
                            <span class="fw-semibold" style="color: #15803d;">₦<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?></span>
                        </td>
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle;">
                            <span class="fw-semibold" style="color: #15803d;"><?php echo number_format($user['points_balance'] ?? 0); ?></span>
                        </td>
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle;">
                            <?php if ($user['is_blocked']): ?>
                                <span class="badge" style="background:#e53935; color:#fff;">Blocked</span>
                            <?php elseif ($user['is_active'] == 0): ?>
                                <span class="badge" style="background:#bdbdbd; color:#fff;">Inactive</span>
                            <?php elseif ($user['payment_verified']): ?>
                                <span class="badge" style="background:#15803d; color:#fff;">Active</span>
                            <?php else: ?>
                                <span class="badge" style="background:#fbc02d; color:#fff;">Pending</span>
                            <?php endif; ?>
                        </td>
                        <td style="padding: 0.8rem 0.7rem; vertical-align: middle; color: #15803d;">
                            <?php echo date('M d, Y', strtotime($user['created_at'])); ?>
                        </td>
                        <td class="text-end" style="padding: 0.8rem 0.7rem; vertical-align: middle;">
                            <button class="icon-action" title="Edit User" onclick="editUser(<?php echo $user['id']; ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="icon-action" title="<?php echo $user['is_active'] ? 'Deactivate' : 'Activate'; ?> User" onclick="toggleStatus(<?php echo $user['id']; ?>, <?php echo $user['is_active'] ? '0' : '1'; ?>)">
                                <i class="fas fa-<?php echo $user['is_active'] ? 'pause' : 'play'; ?>"></i>
                            </button>
                            <button class="icon-action" title="<?php echo $user['is_blocked'] ? 'Unblock' : 'Block'; ?> User" onclick="toggleBlock(<?php echo $user['id']; ?>)">
                                <i class="fas fa-<?php echo $user['is_blocked'] ? 'unlock' : 'lock'; ?>"></i>
                            </button>
                            <button class="icon-action" title="Delete User" onclick="deleteUser(<?php echo $user['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="table-footer p-3 d-flex justify-content-end">
        <nav>
            <ul class="pagination mb-0">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" id="editUserId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-3">
                        <label for="editFullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="editFullName" name="full_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editWalletBalance" class="form-label">Wallet Balance (₦)</label>
                                <input type="number" class="form-control" id="editWalletBalance" name="wallet_balance" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editPointsBalance" class="form-label">Points Balance</label>
                                <input type="number" class="form-control" id="editPointsBalance" name="points_balance" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsBlocked" name="is_blocked">
                            <label class="form-check-label" for="editIsBlocked">
                                Block this user
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Action Forms -->
<form id="toggleBlockForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="toggle_block">
    <input type="hidden" name="user_id" id="toggleUserId">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
</form>

<form id="deleteUserForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete_user">
    <input type="hidden" name="user_id" id="deleteUserId">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
</form>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>
<style>
        body {
            background: var(--gray-50);
            min-height: 100vh;
            margin: 0;
            font-family: 'Inter', 'Amiri', sans-serif;
        }
        .admin-header {
            background: var(--white);
            box-shadow: 0 2px 8px rgba(0,0,0,0.04);
            padding: 1.2rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }
        .admin-logo {
            display: flex;
            align-items: center;
            gap: 0.7rem;
            text-decoration: none;
            color: var(--primary);
            font-size: 1.3rem;
            font-weight: 700;
        }
        .admin-nav-links {
            display: flex;
            gap: 1rem;
        }
        .admin-nav-links a {
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.7rem;
            transition: background 0.2s, color 0.2s;
        }
        .admin-nav-links a.active, .admin-nav-links a:hover {
            background: var(--primary-light);
    color: var(--primary);
}
        .main-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1.5rem;
        }
        .admin-card {
            background: var(--white);
            border-radius: 1.2rem;
            box-shadow: 0 4px 24px rgba(0,0,0,0.07);
            padding: 2rem 1.5rem;
            margin-bottom: 2rem;
        }
        .btn, .btn-primary {
            padding: 0.6rem 1.3rem !important;
            font-size: 1rem !important;
            border-radius: 1.2rem !important;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        @media (max-width: 900px) {
            .main-container { padding: 0 0.5rem; }
        }
        @media (max-width: 600px) {
            .admin-nav { flex-direction: column; align-items: flex-start; padding: 0 0.5rem; }
            .admin-nav-links { width: 100%; justify-content: flex-end; margin-top: 0.5rem; }
            .main-container { padding: 0 0.2rem; }
            .admin-card { padding: 1rem 0.5rem; }
        }

    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }

    .icon-action {
        background: #15803d;
        color: #fff;
        border: none;
    border-radius: 50%;
        width: 36px;
        height: 36px;
        display: inline-flex;
    align-items: center;
    justify-content: center;
        margin-right: 0.2rem;
    font-size: 1.1rem;
        transition: background 0.2s, box-shadow 0.2s;
        box-shadow: 0 2px 8px rgba(21,128,61,0.07);
        cursor: pointer;
        outline: none;
    }
    .icon-action:last-child { margin-right: 0; }
    .icon-action:hover, .icon-action:focus {
        background: #43a047;
        color: #fff;
        box-shadow: 0 4px 16px rgba(21,128,61,0.13);
    }
    .icon-action .fa-trash { color: #fff; }
    .icon-action[title*='Delete'] { background: #e53935; }
    .icon-action[title*='Delete']:hover { background: #b71c1c; }
    .icon-action[title*='Block'] { background: #388e3c; }
    .icon-action[title*='Block']:hover { background: #2e7031; }
    .icon-action[title*='Unblock'] { background: #388e3c; }
    .icon-action[title*='Unblock']:hover { background: #2e7031; }
    .icon-action[title*='Deactivate'] { background: #fbc02d; color: #fff; }
    .icon-action[title*='Deactivate']:hover { background: #f9a825; }
    .icon-action[title*='Activate'] { background: #43a047; }
    .icon-action[title*='Activate']:hover { background: #15803d; }

</style>

<!-- Add New User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                <input type="hidden" name="action" value="add_user">

                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Add New User
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">

                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" minlength="6" required>
                            </div>
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone_number" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number">
                            </div>
                            <div class="mb-3">
                                <label for="ward" class="form-label">Ward</label>
                                <input type="text" class="form-control" id="ward" name="ward">
                            </div>
                            <div class="mb-3">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state">
                            </div>
                            <div class="mb-3">
                                <label for="initial_balance" class="form-label">Initial Wallet Balance (₦)</label>
                                <input type="number" class="form-control" id="initial_balance" name="initial_balance" value="0" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus me-2"></i>Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/admin_error_log_widget.php'; ?>

<script src="../assets/js/admin-users.js"></script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>