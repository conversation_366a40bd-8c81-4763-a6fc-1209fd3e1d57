<?php
/**
 * Debug script for withdrawal approval issues
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Approval Debug - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .debug-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .test-result { padding: 0.5rem; margin: 0.25rem 0; border-radius: 5px; }
        .test-success { background: #d4edda; color: #155724; }
        .test-error { background: #f8d7da; color: #721c24; }
        .test-warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='debug-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-check-circle me-2'></i>Approval Debug</h1>
                        <p class='text-muted'>Debugging withdrawal approval functionality</p>
                    </div>";

$tests = [];
$errors = [];

// Test 1: Database Connection
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-database me-2'></i>Test 1: Database Connection</h6>";

try {
    $conn = getConnection();
    if ($conn) {
        echo "<p class='mb-0'>✅ Database connection successful</p>";
        $tests['database'] = true;
    } else {
        echo "<p class='mb-0'>❌ Database connection failed</p>";
        $tests['database'] = false;
        $errors[] = "Database connection failed";
    }
} catch (Exception $e) {
    echo "<p class='mb-0'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
    $tests['database'] = false;
    $errors[] = $e->getMessage();
}

echo "</div>";

// Test 2: Check Pending Withdrawals
echo "<div class='test-result " . ($tests['database'] ? 'test-success' : 'test-error') . "'>
        <h6><i class='fas fa-clock me-2'></i>Test 2: Pending Withdrawals</h6>";

if ($tests['database']) {
    try {
        // Check withdrawal_requests table
        $pendingQuery = "
            SELECT wr.id, wr.user_id, wr.amount, wr.status, wr.created_at, u.full_name
            FROM withdrawal_requests wr
            JOIN users u ON wr.user_id = u.id
            WHERE wr.status = 'pending'
            ORDER BY wr.created_at DESC LIMIT 5
        ";
        $pendingResult = $conn->query($pendingQuery);

        if ($pendingResult && $pendingResult->num_rows > 0) {
            echo "<p class='mb-2'>✅ Found " . $pendingResult->num_rows . " pending withdrawal requests:</p>";
            while ($row = $pendingResult->fetch_assoc()) {
                echo "<small class='text-muted'>ID: {$row['id']}, User: {$row['full_name']} (ID: {$row['user_id']}), Amount: ₦{$row['amount']}, Date: {$row['created_at']}</small><br>";
            }
        } else {
            echo "<p class='mb-0'>⚠️ No pending withdrawal requests found in withdrawal_requests table</p>";
        }
        
        // Check transactions table for legacy withdrawals
        $legacyQuery = "SELECT id, user_id, amount, status, created_at FROM transactions WHERE transaction_type = 'withdrawal' AND status = 'pending' ORDER BY created_at DESC LIMIT 5";
        $legacyResult = $conn->query($legacyQuery);
        
        if ($legacyResult && $legacyResult->num_rows > 0) {
            echo "<p class='mb-2'>✅ Found " . $legacyResult->num_rows . " pending legacy withdrawals:</p>";
            while ($row = $legacyResult->fetch_assoc()) {
                echo "<small class='text-muted'>Legacy ID: {$row['id']}, User: {$row['user_id']}, Amount: ₦{$row['amount']}, Date: {$row['created_at']}</small><br>";
            }
        } else {
            echo "<p class='mb-0'>⚠️ No pending legacy withdrawals found in transactions table</p>";
        }
        
        $tests['pending'] = true;
    } catch (Exception $e) {
        echo "<p class='mb-0'>❌ Query error: " . htmlspecialchars($e->getMessage()) . "</p>";
        $tests['pending'] = false;
        $errors[] = $e->getMessage();
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database failed)</p>";
    $tests['pending'] = false;
}

echo "</div>";

// Test 3: Test CSRF Token
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-shield-alt me-2'></i>Test 3: CSRF Token</h6>";

$csrfToken = generateCSRFToken();
if ($csrfToken && validateCSRFToken($csrfToken)) {
    echo "<p class='mb-0'>✅ CSRF token generation and validation working</p>";
    echo "<p class='mb-0'>Token: " . substr($csrfToken, 0, 20) . "...</p>";
    $tests['csrf'] = true;
} else {
    echo "<p class='mb-0'>❌ CSRF token system not working</p>";
    $tests['csrf'] = false;
    $errors[] = "CSRF token system failed";
}

echo "</div>";

// Test 4: Test Admin Session
echo "<div class='test-result test-success'>
        <h6><i class='fas fa-user-shield me-2'></i>Test 4: Admin Session</h6>";

echo "<p class='mb-0'>✅ Admin logged in: " . (isAdmin() ? 'Yes' : 'No') . "</p>";
echo "<p class='mb-0'>✅ Admin username: " . ($_SESSION['admin_username'] ?? 'Not set') . "</p>";
echo "<p class='mb-0'>✅ Session ID: " . session_id() . "</p>";

echo "</div>";

// Test 5: Manual Approval Test
echo "<div class='test-result test-warning'>
        <h6><i class='fas fa-tools me-2'></i>Test 5: Manual Approval Test</h6>";

if ($tests['database'] && $tests['pending']) {
    // Get the first pending withdrawal for testing
    $testQuery = "SELECT id, user_id, amount FROM withdrawal_requests WHERE status = 'pending' LIMIT 1";
    $testResult = $conn->query($testQuery);
    
    if ($testResult && $testResult->num_rows > 0) {
        $testWithdrawal = $testResult->fetch_assoc();
        echo "<p class='mb-2'>🧪 Test withdrawal found: ID #{$testWithdrawal['id']}</p>";
        echo "<form method='POST' action='payouts.php' class='mt-3'>
                <input type='hidden' name='csrf_token' value='$csrfToken'>
                <input type='hidden' name='action' value='approve'>
                <input type='hidden' name='request_id' value='{$testWithdrawal['id']}'>
                <div class='mb-3'>
                    <label class='form-label'>Admin Notes:</label>
                    <textarea name='admin_notes' class='form-control' rows='2'>Test approval from debug script</textarea>
                </div>
                <button type='submit' class='btn btn-success btn-sm me-2'>
                    <i class='fas fa-check me-1'></i>Test Approve
                </button>
              </form>";
    } else {
        echo "<p class='mb-0'>⚠️ No pending withdrawals available for testing</p>";
    }
} else {
    echo "<p class='mb-0'>⏭️ Skipped (database or pending withdrawals failed)</p>";
}

echo "</div>";

// Close database connection
if (isset($conn) && $conn) {
    $conn->close();
}

// Summary
$totalTests = count($tests);
$passedTests = count(array_filter($tests));

if (empty($errors)) {
    echo "<div class='alert alert-success'>
            <h5><i class='fas fa-check-circle me-2'></i>Debug Complete ($passedTests/$totalTests tests passed)</h5>
            <p class='mb-3'>The approval system appears to be working correctly.</p>
            <div class='text-center'>
                <a href='payouts.php' class='btn btn-primary btn-lg'>
                    <i class='fas fa-money-bill-wave me-2'></i>Go to Payouts Page
                </a>
            </div>
          </div>";
} else {
    echo "<div class='alert alert-warning'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Issues Found ($passedTests/$totalTests tests passed)</h5>
            <h6>Issues to fix:</h6>
            <ul class='mb-3'>";
    
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    
    echo "            </ul>
            <div class='text-center'>
                <a href='dashboard.php' class='btn btn-secondary btn-lg'>
                    <i class='fas fa-tachometer-alt me-2'></i>Back to Dashboard
                </a>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-info-circle me-1'></i>
                            This debug script helps identify approval issues.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
