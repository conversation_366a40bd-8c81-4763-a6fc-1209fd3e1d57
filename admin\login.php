<?php
// Start output buffering to prevent header issues
ob_start();

// Load centralized database configuration
require_once '../config/db_config.php';

// Enable error reporting for debugging (remove in production)
if (DEVELOPMENT_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Check if admin is already logged in
if (isAdmin()) {
    ob_clean();
    header('Location: dashboard.php');
    exit;
}

$error = '';
$debug_info = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    try {
        if (validateCSRFToken($_POST['csrf_token'])) {
            $username = sanitize($_POST['username']);
            $password = $_POST['password'];

            if (empty($username) || empty($password)) {
                $error = 'Please enter both username and password.';
            } else {
                $conn = getConnection();

                if (!$conn) {
                    throw new Exception('Database connection failed');
                }

                // Create admins table if it doesn't exist (compatible collation)
                $createTableQuery = "
                    CREATE TABLE IF NOT EXISTS admins (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        password VARCHAR(255) NOT NULL,
                        email VARCHAR(100) DEFAULT NULL,
                        full_name VARCHAR(100) DEFAULT NULL,
                        is_active TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                ";
                $conn->query($createTableQuery);

                // For initial setup, check if password matches the master password
                if ($username === 'admin' && $password === '1@3Usazladan') {
                    // Create or update admin account
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    $stmt = $conn->prepare("
                        INSERT INTO admins (username, password)
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE password = ?, updated_at = CURRENT_TIMESTAMP
                    ");

                    if (!$stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }

                    $stmt->bind_param("sss", $username, $hashedPassword, $hashedPassword);

                    if (!$stmt->execute()) {
                        throw new Exception('Execute failed: ' . $stmt->error);
                    }

                    $adminId = $conn->insert_id ?: 1; // Use 1 if updating existing admin
                    $stmt->close();

                    // Set admin session
                    $_SESSION['admin_id'] = $adminId;
                    $_SESSION['admin_username'] = $username;

                    $conn->close();

                    // Clean output buffer before redirect
                    ob_clean();
                    header('Location: dashboard.php');
                    exit;
                } else {
                    // Check against database
                    $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = ?");

                    if (!$stmt) {
                        throw new Exception('Prepare statement failed: ' . $conn->error);
                    }

                    $stmt->bind_param("s", $username);

                    if (!$stmt->execute()) {
                        throw new Exception('Execute failed: ' . $stmt->error);
                    }

                    $result = $stmt->get_result();

                    if ($admin = $result->fetch_assoc()) {
                        if (password_verify($password, $admin['password'])) {
                            // Login successful
                            $_SESSION['admin_id'] = $admin['id'];
                            $_SESSION['admin_username'] = $admin['username'];

                            $stmt->close();
                            $conn->close();

                            // Clean output buffer before redirect
                            ob_clean();
                            header('Location: dashboard.php');
                            exit;
                        } else {
                            $error = 'Invalid username or password.';
                        }
                    } else {
                        $error = 'Invalid username or password.';
                    }

                    $stmt->close();
                    $conn->close();
                }
            }
        } else {
            $error = 'Invalid security token. Please try again.';
        }
    } catch (Exception $e) {
        $error = 'Login failed. Please try again.';
        if (DEVELOPMENT_MODE) {
            $debug_info = 'Debug: ' . $e->getMessage();
        }
        error_log('Admin login error: ' . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Universal Reciters</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="../assets/css/recite-design-system.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            margin: 0;
        }
        .auth-container {
            background: var(--white);
            border-radius: 1.5rem;
            box-shadow: 0 4px 24px rgba(0,0,0,0.07);
            overflow: hidden;
            max-width: 420px;
            width: 100%;
            padding: 2rem 1.5rem;
        }
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .auth-logo {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }
        .auth-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: var(--primary);
        }
        .auth-subtitle {
            font-size: 1.1rem;
            color: var(--gray-600);
            margin-bottom: 2rem;
        }
        .form-group {
            margin-bottom: 1.2rem;
        }
        .form-label {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 0.3rem;
            display: block;
        }
        .form-control {
            width: 100%;
            padding: 0.6rem 1rem;
            border-radius: 0.7rem;
            border: 1px solid var(--gray-200);
            font-size: 1rem;
            margin-top: 0.2rem;
        }
        .btn, .btn-primary {
            padding: 0.6rem 1.3rem !important;
            font-size: 1rem !important;
            border-radius: 1.2rem !important;
            font-weight: 700;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            justify-content: center;
        }
        .alert {
            padding: 0.8rem 1rem;
            border-radius: 0.7rem;
            margin-bottom: 1.2rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.7rem;
        }
        .alert-danger {
            background-color: #ffeaea;
            color: #b91c1c;
        }
        .alert-info {
            background-color: #eaffea;
            color: #15803d;
        }
        .text-center { text-align: center; }
        .text-muted { color: var(--gray-500); }
        .text-decoration-none { text-decoration: none; }
        .mt-4 { margin-top: 1rem; }
        .security-notice {
            background: var(--gray-50);
            border-radius: 0.7rem;
            padding: 0.8rem 1rem;
            margin-top: 1.2rem;
        }
        .security-notice h6 {
            color: var(--primary);
            font-weight: 600;
            margin-bottom: 0.3rem;
        }
        .security-notice ul {
            margin: 0;
            padding-left: 1.2rem;
        }
        .security-notice li {
            font-size: 0.98rem;
            color: var(--gray-600);
            margin-bottom: 0.2rem;
        }
        @media (max-width: 600px) {
            .auth-container { padding: 1.2rem 0.5rem; }
            .auth-title { font-size: 1.2rem; }
            .auth-logo { font-size: 2rem; }
            .btn, .btn-primary { font-size: 0.95rem !important; padding: 0.5rem 1rem !important; }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h1 class="auth-title">Admin Portal</h1>
            <p class="auth-subtitle">Universal Reciters Administration</p>
        </div>
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($debug_info && DEVELOPMENT_MODE): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <?php echo htmlspecialchars($debug_info); ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user"></i>
                    Username
                </label>
                <input type="text" class="form-control" id="username" name="username"
                       placeholder="Enter your username" required
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" class="form-control" id="password" name="password"
                       placeholder="Enter your password" required>
            </div>

            <button type="submit" name="login" class="btn btn-primary w-full">
                <i class="fas fa-sign-in-alt"></i>
                Login to Admin Panel
            </button>
        </form>

        <div class="text-center mt-4">
            <a href="../index.php" class="text-muted text-decoration-none">
                <i class="fas fa-arrow-left"></i>
                Back to Main Site
            </a>
        </div>

        <!-- Security Notice -->
        <div class="security-notice">
            <h6>
                <i class="fas fa-exclamation-triangle"></i>
                Security Notice
            </h6>
            <ul>
                <li>Admin access is restricted and monitored</li>
                <li>Unauthorized access attempts are logged</li>
                <li>Default credentials: admin / 1@3Usazladan</li>
            </ul>
        </div>
    </div>

    <script>
        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>