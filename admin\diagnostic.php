<?php
/**
 * Admin Login Diagnostic Tool
 * Use this to troubleshoot admin login issues
 */

// Enable error reporting for diagnostics
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/db_config.php';

$diagnostics = [];
$errors = [];

// Test 1: Check if config files exist
$diagnostics['config_files'] = [
    'config.php' => file_exists('../config.php'),
    'database.php' => file_exists('../config/database.php'),
    'server_config.php' => file_exists('../config/server_config.php'),
];

// Test 2: Check database connection
try {
    $conn = getConnection();
    $diagnostics['database_connection'] = true;
    $diagnostics['database_info'] = [
        'host' => DB_HOST,
        'database' => DB_NAME,
        'user' => DB_USER,
        'charset' => DB_CHARSET ?? 'utf8mb4'
    ];
} catch (Exception $e) {
    $diagnostics['database_connection'] = false;
    $errors[] = 'Database connection failed: ' . $e->getMessage();
}

// Test 3: Check if admins table exists
if ($diagnostics['database_connection']) {
    try {
        $result = $conn->query("SHOW TABLES LIKE 'admins'");
        $diagnostics['admins_table_exists'] = $result->num_rows > 0;
        
        if ($diagnostics['admins_table_exists']) {
            $result = $conn->query("SELECT COUNT(*) as count FROM admins");
            $count = $result->fetch_assoc()['count'];
            $diagnostics['admin_count'] = $count;
        }
    } catch (Exception $e) {
        $diagnostics['admins_table_exists'] = false;
        $errors[] = 'Failed to check admins table: ' . $e->getMessage();
    }
}

// Test 4: Check session functionality
session_start();
$_SESSION['test'] = 'working';
$diagnostics['session_working'] = isset($_SESSION['test']) && $_SESSION['test'] === 'working';
unset($_SESSION['test']);

// Test 5: Check CSRF token generation
try {
    $csrf_token = generateCSRFToken();
    $diagnostics['csrf_token_generation'] = !empty($csrf_token);
} catch (Exception $e) {
    $diagnostics['csrf_token_generation'] = false;
    $errors[] = 'CSRF token generation failed: ' . $e->getMessage();
}

// Test 6: Check if admin functions exist
$diagnostics['admin_functions'] = [
    'isAdmin' => function_exists('isAdmin'),
    'validateCSRFToken' => function_exists('validateCSRFToken'),
    'generateCSRFToken' => function_exists('generateCSRFToken'),
    'sanitize' => function_exists('sanitize'),
];

// Test 7: Check server environment
$diagnostics['server_environment'] = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
    'development_mode' => defined('DEVELOPMENT_MODE') ? DEVELOPMENT_MODE : 'Not defined',
];

// Test 8: Check file permissions
$diagnostics['file_permissions'] = [
    'config_readable' => is_readable('../config.php'),
    'admin_dir_writable' => is_writable(__DIR__),
    'uploads_dir_exists' => is_dir('../uploads/'),
    'uploads_dir_writable' => is_writable('../uploads/'),
];

// Test 9: Try to create admin table
if ($diagnostics['database_connection'] && !$diagnostics['admins_table_exists']) {
    try {
        $createTableQuery = "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ";
        $conn->query($createTableQuery);
        $diagnostics['admin_table_created'] = true;
    } catch (Exception $e) {
        $diagnostics['admin_table_created'] = false;
        $errors[] = 'Failed to create admin table: ' . $e->getMessage();
    }
}

// Test 10: Check if default admin can be created
if ($diagnostics['database_connection'] && ($diagnostics['admins_table_exists'] || $diagnostics['admin_table_created'])) {
    try {
        $hashedPassword = password_hash('1@3Usazladan', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("
            INSERT INTO admins (username, password) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE password = ?, updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->bind_param("sss", $username = 'admin', $hashedPassword, $hashedPassword);
        $stmt->execute();
        $diagnostics['default_admin_created'] = true;
        $stmt->close();
    } catch (Exception $e) {
        $diagnostics['default_admin_created'] = false;
        $errors[] = 'Failed to create default admin: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Diagnostics - RECITE App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="fas fa-stethoscope me-2"></i>Admin Login Diagnostics</h4>
                    </div>
                    <div class="card-body">
                        
                        <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Errors Found:</h5>
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Configuration Files</h5>
                                <ul class="list-group mb-3">
                                    <?php foreach ($diagnostics['config_files'] as $file => $exists): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <?php echo $file; ?>
                                        <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                            <?php echo $exists ? 'Found' : 'Missing'; ?>
                                        </span>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                
                                <h5>Database Connection</h5>
                                <ul class="list-group mb-3">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Connection Status
                                        <span class="badge bg-<?php echo $diagnostics['database_connection'] ? 'success' : 'danger'; ?>">
                                            <?php echo $diagnostics['database_connection'] ? 'Connected' : 'Failed'; ?>
                                        </span>
                                    </li>
                                    <?php if (isset($diagnostics['database_info'])): ?>
                                    <?php foreach ($diagnostics['database_info'] as $key => $value): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <?php echo ucfirst($key); ?>
                                        <code><?php echo htmlspecialchars($value); ?></code>
                                    </li>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </ul>
                                
                                <h5>Admin Table</h5>
                                <ul class="list-group mb-3">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Table Exists
                                        <span class="badge bg-<?php echo $diagnostics['admins_table_exists'] ? 'success' : 'warning'; ?>">
                                            <?php echo $diagnostics['admins_table_exists'] ? 'Yes' : 'No'; ?>
                                        </span>
                                    </li>
                                    <?php if (isset($diagnostics['admin_count'])): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Admin Count
                                        <span class="badge bg-info"><?php echo $diagnostics['admin_count']; ?></span>
                                    </li>
                                    <?php endif; ?>
                                    <?php if (isset($diagnostics['admin_table_created'])): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Table Created
                                        <span class="badge bg-<?php echo $diagnostics['admin_table_created'] ? 'success' : 'danger'; ?>">
                                            <?php echo $diagnostics['admin_table_created'] ? 'Yes' : 'Failed'; ?>
                                        </span>
                                    </li>
                                    <?php endif; ?>
                                    <?php if (isset($diagnostics['default_admin_created'])): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Default Admin Created
                                        <span class="badge bg-<?php echo $diagnostics['default_admin_created'] ? 'success' : 'danger'; ?>">
                                            <?php echo $diagnostics['default_admin_created'] ? 'Yes' : 'Failed'; ?>
                                        </span>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>Session & Security</h5>
                                <ul class="list-group mb-3">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Session Working
                                        <span class="badge bg-<?php echo $diagnostics['session_working'] ? 'success' : 'danger'; ?>">
                                            <?php echo $diagnostics['session_working'] ? 'Yes' : 'No'; ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        CSRF Token Generation
                                        <span class="badge bg-<?php echo $diagnostics['csrf_token_generation'] ? 'success' : 'danger'; ?>">
                                            <?php echo $diagnostics['csrf_token_generation'] ? 'Working' : 'Failed'; ?>
                                        </span>
                                    </li>
                                </ul>
                                
                                <h5>Required Functions</h5>
                                <ul class="list-group mb-3">
                                    <?php foreach ($diagnostics['admin_functions'] as $func => $exists): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <?php echo $func; ?>()
                                        <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                            <?php echo $exists ? 'Found' : 'Missing'; ?>
                                        </span>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                                
                                <h5>File Permissions</h5>
                                <ul class="list-group mb-3">
                                    <?php foreach ($diagnostics['file_permissions'] as $perm => $status): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <?php echo ucwords(str_replace('_', ' ', $perm)); ?>
                                        <span class="badge bg-<?php echo $status ? 'success' : 'warning'; ?>">
                                            <?php echo $status ? 'OK' : 'Issue'; ?>
                                        </span>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>Server Environment</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <?php foreach ($diagnostics['server_environment'] as $key => $value): ?>
                                        <tr>
                                            <td><strong><?php echo ucwords(str_replace('_', ' ', $key)); ?></strong></td>
                                            <td><code><?php echo htmlspecialchars($value); ?></code></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Next Steps:</h6>
                                <ol class="mb-0">
                                    <li>If database connection failed, check your database credentials in <code>config/server_config.php</code></li>
                                    <li>If admin table doesn't exist, it should be created automatically on first login attempt</li>
                                    <li>Try logging in with username: <code>admin</code> and password: <code>1@3Usazladan</code></li>
                                    <li>If issues persist, check server error logs</li>
                                    <li><strong>Delete this diagnostic file after troubleshooting for security</strong></li>
                                </ol>
                            </div>
                            
                            <div class="text-center">
                                <a href="login.php" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Try Admin Login
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
