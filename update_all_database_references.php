<?php
/**
 * Update All Database References Script
 * Updates all files to use the new centralized config/db_config.php
 * Universal Reciters - https://universalreciters.name.ng/
 */

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Update Database References - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .update-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .step { padding: 1rem; margin: 0.5rem 0; border-radius: 8px; }
        .step.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .step.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .step.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .file-list { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='update-card p-5'>
                    <div class='text-center mb-4'>
                        <h1 class='text-primary'><i class='fas fa-sync-alt me-2'></i>Update Database References</h1>
                        <p class='text-muted'>Converting all files to use centralized config/db_config.php</p>
                    </div>";

$updatedFiles = [];
$errors = [];

// Define files that need updating and their replacement patterns
$filesToUpdate = [
    // Root files
    'verify_payment.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    'register.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    'leaderboard.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    'setup_database.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    'setup_user_tables.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    'debug.php' => [
        'old' => "require_once 'config/database.php';",
        'new' => "require_once 'config/db_config.php';"
    ],
    
    // API files
    'api/paystack-webhook.php' => [
        'old' => "require_once '../config.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'api/init-sample-data.php' => [
        'old' => "require_once '../config.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'api/get_user.php' => [
        'old' => "require_once '../config/database.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'api/save_recording.php' => [
        'old' => "require_once '../config/database.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'api/upload_recording.php' => [
        'old' => "require_once '../config/database.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    
    // Admin files
    'admin/diagnostic.php' => [
        'old' => "require_once '../config.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'admin/production_fix.php' => [
        'old' => "require_once '../config.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    'admin/test_headers.php' => [
        'old' => "require_once '../config.php';",
        'new' => "require_once '../config/db_config.php';"
    ],
    
    // Component files
    'components/user_header.php' => [
        'old' => "require_once __DIR__ . '/../config/database.php';",
        'new' => "require_once __DIR__ . '/../config/db_config.php';"
    ]
];

echo "<div class='step info'>
        <h5><i class='fas fa-list me-2'></i>Files to Update</h5>
        <div class='file-list'>
            <ul class='mb-0'>";

foreach ($filesToUpdate as $file => $replacement) {
    echo "<li><code>$file</code></li>";
}

echo "        </ul>
        </div>
      </div>";

// Update each file
foreach ($filesToUpdate as $file => $replacement) {
    echo "<div class='step info'>
            <h6><i class='fas fa-file-code me-2'></i>Updating: $file</h6>";
    
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $originalContent = $content;
        
        // Apply the replacement
        $content = str_replace($replacement['old'], $replacement['new'], $content);
        
        // Check if any changes were made
        if ($content !== $originalContent) {
            if (file_put_contents($file, $content)) {
                echo "<p class='text-success mb-0'>✅ Updated successfully</p>";
                $updatedFiles[] = $file;
            } else {
                echo "<p class='text-danger mb-0'>❌ Failed to write file</p>";
                $errors[] = "Failed to write: $file";
            }
        } else {
            echo "<p class='text-warning mb-0'>⚠️ No changes needed (already updated or pattern not found)</p>";
        }
    } else {
        echo "<p class='text-warning mb-0'>⚠️ File not found</p>";
    }
    
    echo "</div>";
}

// Additional specific updates for complex files
echo "<div class='step info'>
        <h5><i class='fas fa-wrench me-2'></i>Special File Updates</h5>";

// Update config/db_setup.php to point to new config
if (file_exists('config/db_setup.php')) {
    $content = file_get_contents('config/db_setup.php');
    $content = str_replace(
        "\$configFile = __DIR__ . '/server_config.php';",
        "\$configFile = __DIR__ . '/db_config.php';",
        $content
    );
    if (file_put_contents('config/db_setup.php', $content)) {
        echo "<p>✅ Updated config/db_setup.php to use db_config.php</p>";
        $updatedFiles[] = 'config/db_setup.php';
    }
}

echo "</div>";

// Summary
if (empty($errors)) {
    echo "<div class='step success'>
            <h5><i class='fas fa-check-circle me-2'></i>Update Complete!</h5>
            <p class='mb-3'>All files have been successfully updated to use the centralized database configuration.</p>
            
            <div class='row'>
                <div class='col-md-6'>
                    <h6>Files Updated:</h6>
                    <div class='file-list'>
                        <ul class='small'>";
    
    foreach ($updatedFiles as $file) {
        echo "<li><code>$file</code></li>";
    }
    
    echo "                </ul>
                    </div>
                </div>
                <div class='col-md-6'>
                    <h6>What Changed:</h6>
                    <ul class='small'>
                        <li>✅ All files now use <code>config/db_config.php</code></li>
                        <li>✅ Removed dependencies on old config files</li>
                        <li>✅ Centralized database configuration</li>
                        <li>✅ Consistent configuration across all files</li>
                    </ul>
                </div>
            </div>
            
            <div class='alert alert-info mt-3'>
                <h6><i class='fas fa-info-circle me-2'></i>Next Steps:</h6>
                <ol class='mb-0'>
                    <li>Configure your database credentials in <code>config/db_config.php</code></li>
                    <li>Test the application to ensure everything works</li>
                    <li>Delete old configuration files if no longer needed</li>
                </ol>
            </div>
            
            <div class='text-center mt-4'>
                <a href='configure_database.php' class='btn btn-primary btn-lg me-2'>
                    <i class='fas fa-database me-2'></i>Configure Database
                </a>
                <a href='admin/login.php' class='btn btn-success btn-lg'>
                    <i class='fas fa-sign-in-alt me-2'></i>Test Admin Login
                </a>
            </div>
          </div>";
} else {
    echo "<div class='step error'>
            <h5><i class='fas fa-exclamation-triangle me-2'></i>Some Errors Occurred</h5>
            <div class='alert alert-warning'>
                <h6>Errors:</h6>
                <ul class='mb-0'>";
    
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    
    echo "                </ul>
            </div>
            
            <div class='alert alert-info'>
                <h6>Troubleshooting:</h6>
                <ul class='mb-0'>
                    <li>Check file permissions (files should be writable)</li>
                    <li>Ensure you have proper access to modify files</li>
                    <li>Some files may not exist in your installation</li>
                </ul>
            </div>
          </div>";
}

echo "                <div class='mt-4 text-center'>
                        <small class='text-muted'>
                            <i class='fas fa-shield-alt me-1'></i>
                            Delete this update script after successful completion for security.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
