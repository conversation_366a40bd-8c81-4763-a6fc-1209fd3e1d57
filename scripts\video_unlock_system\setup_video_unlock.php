<?php
/**
 * Video Unlock System Setup Script
 * This script sets up the complete video unlock system
 */

echo "🎬 Video Unlock System Setup\n";
echo "============================\n\n";

// Check if we're in the right directory
if (!file_exists('../../config/db_config.php')) {
    echo "❌ Error: Please run this script from the scripts/video_unlock_system/ directory\n";
    exit(1);
}

// Step 1: Run database migration
echo "📊 Step 1: Running database migration...\n";
include_once 'migrate_video_unlock.php';

echo "\n";

// Step 2: Verify setup
echo "🔍 Step 2: Verifying setup...\n";
$conn = getConnection();

if (!$conn) {
    echo "❌ Database connection failed\n";
    exit(1);
}

// Check if tables exist
$tables_to_check = [
    'video_unlocks',
    'admin_earnings'
];

$all_tables_exist = true;
foreach ($tables_to_check as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✅ Table '$table' exists\n";
    } else {
        echo "❌ Table '$table' missing\n";
        $all_tables_exist = false;
    }
}

// Check if columns exist in existing tables
$columns_to_check = [
    'videos' => ['unlock_price', 'is_locked', 'view_count'],
    'screen_records' => ['unlock_price', 'is_locked', 'view_count'],
    'mirror_recordings' => ['unlock_price', 'is_locked', 'view_count']
];

foreach ($columns_to_check as $table => $columns) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        foreach ($columns as $column) {
            $col_result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($col_result->num_rows > 0) {
                echo "✅ Column '$column' exists in table '$table'\n";
            } else {
                echo "❌ Column '$column' missing in table '$table'\n";
                $all_tables_exist = false;
            }
        }
    }
}

$conn->close();

echo "\n";

// Step 3: Create test data (optional)
echo "🧪 Step 3: Creating test data...\n";
echo "Do you want to create test data? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) === 'y') {
    include_once 'create_test_data.php';
}

echo "\n";

// Step 4: Setup instructions
echo "📋 Step 4: Setup Instructions\n";
echo "=============================\n\n";

echo "✅ Video Unlock System has been set up successfully!\n\n";

echo "📁 Files created:\n";
echo "   - scripts/video_unlock_system/migrate_video_unlock.php\n";
echo "   - api/unlock_video.php\n";
echo "   - admin/video-unlock-transactions.php\n";
echo "   - admin/get-transaction-details.php\n";
echo "   - assets/js/video-unlock.js\n\n";

echo "🔧 Next steps:\n";
echo "   1. Include video-unlock.js in your pages:\n";
echo "      <script src='/assets/js/video-unlock.js'></script>\n\n";
echo "   2. Add user ID to your pages:\n";
echo "      <div data-user-id='<?php echo \$user_id; ?>'>\n\n";
echo "   3. Use locked video elements:\n";
echo "      " . htmlspecialchars(VideoUnlockSystem::createLockedVideoElement(1, 'video', '3.00')) . "\n\n";
echo "   4. Access admin panel at: /admin/video-unlock-transactions.php\n\n";

echo "💰 Payment Split:\n";
echo "   - Total cost: ₦3.00 per video\n";
echo "   - Creator receives: ₦1.00\n";
echo "   - Admin receives: ₦2.00\n\n";

echo "🎯 Features:\n";
echo "   - Automatic wallet deduction\n";
echo "   - Transaction tracking\n";
echo "   - Admin earnings dashboard\n";
echo "   - User unlock history\n";
echo "   - Real-time balance updates\n\n";

echo "🚀 Ready to use!\n";
?> 