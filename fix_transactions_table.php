<?php
require_once 'config/db_config.php';

try {
    $conn = getConnection();
    
    // The SQL statement to modify the ENUM column
    // It redefines the column with the original values PLUS the new 'deposit' value.
    $sql = "
        ALTER TABLE `transactions` 
        CHANGE COLUMN `transaction_type` `transaction_type` 
        ENUM('payment', 'referral', 'earning', 'withdrawal', 'deposit', 'point_purchase', 'point_sale') 
        NOT NULL;
    ";
    
    if ($conn->query($sql) === TRUE) {
        echo "<h1>Database Update Successful!</h1>";
        echo "<p>The 'transactions' table has been successfully updated to include the 'deposit' transaction type.</p>";
        echo "<p>You can now safely delete this file (<code>fix_transactions_table.php</code>).</p>";
    } else {
        echo "<h1>Error updating database!</h1>";
        echo "<p>An error occurred: " . $conn->error . "</p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h1>An exception occurred!</h1>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?> 