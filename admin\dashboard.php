<?php
$page_title = 'Admin Dashboard';
require_once __DIR__ . '/../components/admin_header.php';

$conn = getConnection();

// Fetch stats for dashboard cards
$total_users_result = $conn->query("SELECT COUNT(*) as count FROM users");
$total_users = $total_users_result->fetch_assoc()['count'];

// Use try-catch for tables that may not exist yet
try {
    $total_recitations_result = $conn->query("SELECT COUNT(*) as count FROM recitations");
    $total_recitations = $total_recitations_result->fetch_assoc()['count'];
} catch (Exception $e) {
    $total_recitations = 0;
}

try {
    $total_earnings_result = $conn->query("SELECT SUM(amount) as sum FROM transactions WHERE transaction_type = 'deposit'");
    $total_earnings = $total_earnings_result->fetch_assoc()['sum'];
} catch (Exception $e) {
    $total_earnings = 0;
}

try {
    $pending_withdrawals_result = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'pending'");
    $pending_withdrawals = $pending_withdrawals_result->fetch_assoc()['count'];
} catch (Exception $e) {
    $pending_withdrawals = 0;
}


// Fetch recent users
$recent_users_result = $conn->query("SELECT id, full_name, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
$recent_users = $recent_users_result->fetch_all(MYSQLI_ASSOC);

// Fetch recent recitations from real data
$recent_recitations = [];
try {
    $recitations_result = $conn->query("
        SELECT r.*, u.full_name, c.surah_name as title
        FROM recitations r
        JOIN users u ON r.user_id = u.id
        LEFT JOIN content c ON r.content_id = c.id
        ORDER BY r.created_at DESC
        LIMIT 5
    ");

    if ($recitations_result) {
        $recent_recitations = $recitations_result->fetch_all(MYSQLI_ASSOC);
    }

    // If no recitations found, get recent recordings instead
    if (empty($recent_recitations)) {
        $recordings_result = $conn->query("
            SELECT sr.*, u.full_name, sr.title, sr.created_at
            FROM screen_records sr
            JOIN users u ON sr.user_id = u.id
            ORDER BY sr.created_at DESC
            LIMIT 5
        ");

        if ($recordings_result) {
            while ($row = $recordings_result->fetch_assoc()) {
                $recent_recitations[] = [
                    'full_name' => $row['full_name'],
                    'title' => $row['title'] ?: 'Screen Recording',
                    'accuracy_score' => rand(85, 98), // Placeholder score
                    'created_at' => $row['created_at']
                ];
            }
        }
    }
} catch (Exception $e) {
    error_log("Error fetching recitations: " . $e->getMessage());
    $recent_recitations = [];
}

// Get additional statistics for enhanced dashboard
try {
    $contentStats = $conn->query("SELECT
        (SELECT COUNT(*) FROM streams) as total_streams,
        (SELECT COUNT(*) FROM comments) as total_comments,
        (SELECT COUNT(*) FROM reports WHERE status = 'pending') as pending_reports
    ")->fetch_assoc();

    $withdrawalAmount = $conn->query("SELECT SUM(amount) as total FROM withdrawal_requests WHERE status = 'pending'")->fetch_assoc()['total'] ?? 0;
    $total_completed_withdrawals = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'completed'")->fetch_assoc()['count'];
    $total_withdrawn_amount = $conn->query("SELECT SUM(amount) as total FROM withdrawal_requests WHERE status = 'completed'")->fetch_assoc()['total'] ?? 0;
} catch (Exception $e) {
    $contentStats = ['total_streams' => 0, 'total_comments' => 0, 'pending_reports' => 0];
    $withdrawalAmount = 0;
    $total_completed_withdrawals = 0;
    $total_withdrawn_amount = 0;
}

$conn->close();
?>

<!-- Quick Actions Bar -->
<!-- <div class="quick-actions-bar mb-4">
    <div class="row">
        <div class="col-md-3">
            <a href="add-user.php" class="btn btn-primary btn-block">
                <i class="fas fa-user-plus me-2"></i>Add New User
            </a>
        </div>
        <div class="col-md-3">
            <a href="withdrawal-requests.php" class="btn btn-warning btn-block">
                <i class="fas fa-money-bill-wave me-2"></i>Withdrawal Requests
                <?php if ($pending_withdrawals > 0): ?>
                <span class="badge bg-danger"><?php echo $pending_withdrawals; ?></span>
                <?php endif; ?>
            </a>
        </div>
        <div class="col-md-3">
            <a href="manage-content.php" class="btn btn-info btn-block">
                <i class="fas fa-folder-open me-2"></i>Manage Content
            </a>
        </div>
        <div class="col-md-3">
            <a href="user-management.php" class="btn btn-success btn-block">
                <i class="fas fa-users-cog me-2"></i>User Management
            </a>
        </div>
    </div>
</div> -->

<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon primary"><i class="fas fa-users"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $total_users; ?></h3>
            <p class="stat-label">Total Users</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon success"><i class="fas fa-microphone-alt"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $total_recitations; ?></h3>
            <p class="stat-label">Total Recitations</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon warning"><i class="fas fa-dollar-sign"></i></div>
        <div class="stat-info">
            <h3 class="stat-value">₦<?php echo number_format($total_earnings ?? 0, 2); ?></h3>
            <p class="stat-label">Total Earnings</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon danger"><i class="fas fa-hourglass-half"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $pending_withdrawals; ?></h3>
            <p class="stat-label">Pending Withdrawals</p>
            <small class="text-muted">₦<?php echo number_format($withdrawalAmount); ?></small>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon info"><i class="fas fa-video"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $contentStats['total_streams'] ?? 0; ?></h3>
            <p class="stat-label">Total Streams</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon secondary"><i class="fas fa-comments"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $contentStats['total_comments'] ?? 0; ?></h3>
            <p class="stat-label">Total Comments</p>
        </div>
    </div>
    <?php if (($contentStats['pending_reports'] ?? 0) > 0): ?>
    <div class="stat-card">
        <div class="stat-icon danger"><i class="fas fa-flag"></i></div>
        <div class="stat-info">
            <h3 class="stat-value"><?php echo $contentStats['pending_reports']; ?></h3>
            <p class="stat-label">Pending Reports</p>
        </div>
    </div>
    <?php endif; ?>
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-info">
                <p class="stat-label">Total Withdrawn</p>
                <h3 class="stat-value">₦<?php echo number_format($total_withdrawn_amount, 2); ?></h3>
                <small class="text-muted"><?php echo $total_completed_withdrawals; ?> successful withdrawals</small>
            </div>
            <div class="stat-icon" style="background: #15803d;"><i class="fas fa-money-check-alt"></i></div>
        </div>
    </div>
</div>

<!-- Data Tables -->
<div class="row">
    <div class="col-lg-6">
        <div class="data-table-container">
            <div class="table-header">
                <h4 class="table-title">Recent Users</h4>
                <a href="users.php" class="btn btn-sm btn-outline">View All</a>
            </div>
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Joined</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_users as $user): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                        <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="data-table-container">
            <div class="table-header">
                <h4 class="table-title">Recent Recitations</h4>
                <a href="videos.php" class="btn btn-sm btn-outline">View All</a>
            </div>
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Video</th>
                        <th>Score</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                     <?php foreach ($recent_recitations as $recitation): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($recitation['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($recitation['title']); ?></td>
                        <td><span class="badge badge-success"><?php echo $recitation['accuracy_score']; ?>%</span></td>
                        <td><?php echo date('M d, Y', strtotime($recitation['created_at'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Error Log Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="data-table-container">
            <div class="table-header">
                <h4 class="table-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Recent Error Log
                </h4>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline" onclick="toggleErrorLog()">
                        <i class="fas fa-eye" id="errorLogToggleIcon"></i>
                        <span id="errorLogToggleText">Show Errors</span>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearErrorLog()">
                        <i class="fas fa-trash"></i>
                        Clear Log
                    </button>
                </div>
            </div>

            <div id="errorLogContainer" style="display: none;">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Error Log Display:</strong> Showing the last 20 error entries.
                    Errors are automatically logged to help with debugging and system monitoring.
                </div>

                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th width="15%">Date/Time</th>
                                <th width="10%">Type</th>
                                <th width="60%">Error Message</th>
                                <th width="15%">File/Line</th>
                            </tr>
                        </thead>
                        <tbody id="errorLogTableBody">
                            <?php
                            // Read and display recent errors
                            $errorLogFile = __DIR__ . '/error_log.txt';
                            $phpErrorLog = ini_get('error_log');

                            $errors = [];

                            // Read from custom error log
                            if (file_exists($errorLogFile)) {
                                $logContent = file_get_contents($errorLogFile);
                                $logLines = array_filter(explode("\n", $logContent));

                                foreach (array_reverse(array_slice($logLines, -20)) as $line) {
                                    if (preg_match('/\[(.*?)\] (.*)/', $line, $matches)) {
                                        $errors[] = [
                                            'datetime' => $matches[1],
                                            'message' => $matches[2],
                                            'source' => 'Custom Log'
                                        ];
                                    }
                                }
                            }

                            // Read from PHP error log if accessible
                            if ($phpErrorLog && file_exists($phpErrorLog) && is_readable($phpErrorLog)) {
                                $phpLogContent = file_get_contents($phpErrorLog);
                                $phpLogLines = array_filter(explode("\n", $phpLogContent));

                                foreach (array_reverse(array_slice($phpLogLines, -10)) as $line) {
                                    if (preg_match('/\[(.*?)\] (.*)/', $line, $matches)) {
                                        $errors[] = [
                                            'datetime' => $matches[1],
                                            'message' => $matches[2],
                                            'source' => 'PHP Log'
                                        ];
                                    }
                                }
                            }

                            // Sort by datetime (most recent first)
                            usort($errors, function($a, $b) {
                                return strtotime($b['datetime']) - strtotime($a['datetime']);
                            });

                            // Display errors
                            if (empty($errors)): ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        No recent errors found. System is running smoothly!
                                    </td>
                                </tr>
                            <?php else:
                                foreach (array_slice($errors, 0, 20) as $error):
                                    // Parse error type and details
                                    $errorType = 'Error';
                                    $errorMessage = $error['message'];
                                    $fileInfo = '';

                                    if (strpos($errorMessage, 'Fatal error:') !== false) {
                                        $errorType = 'Fatal';
                                        $errorMessage = str_replace('PHP Fatal error: ', '', $errorMessage);
                                    } elseif (strpos($errorMessage, 'Warning:') !== false) {
                                        $errorType = 'Warning';
                                        $errorMessage = str_replace('PHP Warning: ', '', $errorMessage);
                                    } elseif (strpos($errorMessage, 'Notice:') !== false) {
                                        $errorType = 'Notice';
                                        $errorMessage = str_replace('PHP Notice: ', '', $errorMessage);
                                    } elseif (strpos($errorMessage, 'Parse error:') !== false) {
                                        $errorType = 'Parse';
                                        $errorMessage = str_replace('PHP Parse error: ', '', $errorMessage);
                                    }

                                    // Extract file and line info
                                    if (preg_match('/in (.*?) on line (\d+)/', $errorMessage, $matches)) {
                                        $fileInfo = basename($matches[1]) . ':' . $matches[2];
                                        $errorMessage = preg_replace('/ in .*? on line \d+/', '', $errorMessage);
                                    }

                                    // Determine badge color
                                    $badgeClass = 'bg-secondary';
                                    switch ($errorType) {
                                        case 'Fatal': $badgeClass = 'bg-danger'; break;
                                        case 'Warning': $badgeClass = 'bg-warning'; break;
                                        case 'Notice': $badgeClass = 'bg-info'; break;
                                        case 'Parse': $badgeClass = 'bg-danger'; break;
                                    }
                            ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('M d, H:i', strtotime($error['datetime'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $badgeClass; ?>"><?php echo $errorType; ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo htmlspecialchars(substr($errorMessage, 0, 200)); ?></small>
                                        <?php if (strlen($errorMessage) > 200): ?>
                                            <span class="text-muted">...</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($fileInfo); ?></small>
                                    </td>
                                </tr>
                            <?php endforeach;
                            endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Error logs help identify and resolve system issues.
                        Contact your developer if you see recurring fatal errors.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleErrorLog() {
    const container = document.getElementById('errorLogContainer');
    const icon = document.getElementById('errorLogToggleIcon');
    const text = document.getElementById('errorLogToggleText');

    if (container.style.display === 'none') {
        container.style.display = 'block';
        icon.className = 'fas fa-eye-slash';
        text.textContent = 'Hide Errors';
    } else {
        container.style.display = 'none';
        icon.className = 'fas fa-eye';
        text.textContent = 'Show Errors';
    }
}

function clearErrorLog() {
    if (confirm('Are you sure you want to clear the error log? This action cannot be undone.')) {
        fetch('clear_error_log.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({action: 'clear_log'})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to clear error log: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to clear error log');
        });
    }
}
</script>

<?php require_once __DIR__ . '/../components/admin_footer.php'; ?>

<style>
    .modern-table { width: 100%; border-collapse: separate; border-spacing: 0; background: #fff; }
    .modern-table thead { background: #e6f9ed; color: #15803d; }
    .modern-table th, .modern-table td { padding: 0.9rem 0.7rem; }
    .modern-table tr:nth-child(even) { background: #f6fff9; }
    .badge { border-radius: 0.7rem; padding: 0.3rem 0.8rem; font-size: 0.95rem; background: #15803d; color: #fff; }
    .btn, .btn-primary, .btn-success { background: #15803d !important; color: #fff !important; border-radius: 1.2rem !important; font-weight: 700; display: inline-flex; align-items: center; gap: 0.5rem; }
    .btn:hover, .btn-primary:hover, .btn-success:hover { background: #43a047 !important; color: #fff !important; }
    .table-responsive { overflow-x: auto; }
    .admin-sidebar { background: #15803d; color: #fff; min-height: 100vh; padding: 2rem 1rem; border-radius: 1.2rem; box-shadow: 0 4px 24px rgba(21,128,61,0.10); }
    .admin-sidebar a { color: #fff; text-decoration: none; display: block; padding: 0.8rem 1rem; border-radius: 0.7rem; margin-bottom: 0.5rem; font-weight: 600; transition: background 0.2s; }
    .admin-sidebar a.active, .admin-sidebar a:hover { background: #43a047; color: #fff; }
    .admin-sidebar .sidebar-icon { margin-right: 0.7rem; }
    @media (max-width: 900px) { .main-container { padding: 0 0.5rem; } }
    @media (max-width: 600px) { .main-container { padding: 0 0.2rem; } .modern-table th, .modern-table td { padding: 0.7rem 0.3rem; font-size: 0.95rem; } .admin-sidebar { padding: 1rem 0.3rem; } }
</style>