<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuraSupport - AI Virtual Customer Support</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide-react@0.292.0/dist/lucide-react.js"></script>
    <script src="https://unpkg.com/lucide@latest"></script>

    <style>
        /* Custom styles for the page */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .gradient-text {
            background: linear-gradient(to right, #4f46e5, #9333ea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .hero-bg-pattern {
            background-image: radial-gradient(#e2e8f0 1px, transparent 1px);
            background-size: 20px 20px;
        }
    </style>
</head>
<body class="text-slate-800">

    <!-- Header -->
    <header id="header" class="bg-white/80 backdrop-blur-lg fixed top-0 left-0 right-0 z-50 border-b border-slate-200">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="#" class="flex items-center space-x-2">
                        <svg width="32" height="32" viewBox="0 0 24 24" class="text-indigo-600" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                        <span class="text-2xl font-bold text-slate-900">AuraSupport</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-slate-600 hover:text-indigo-600 transition-colors">Features</a>
                    <a href="#pricing" class="text-slate-600 hover:text-indigo-600 transition-colors">Pricing</a>
                    <a href="#testimonials" class="text-slate-600 hover:text-indigo-600 transition-colors">Testimonials</a>
                    <a href="#faq" class="text-slate-600 hover:text-indigo-600 transition-colors">FAQ</a>
                </nav>

                <!-- CTA & Mobile Menu Button -->
                <div class="flex items-center">
                    <a href="#" class="hidden sm:inline-block bg-indigo-600 text-white px-5 py-2 rounded-lg text-sm font-semibold hover:bg-indigo-700 transition-all shadow-sm">Get Started Free</a>
                    <button id="mobile-menu-button" class="md:hidden ml-4 p-2 text-slate-600 hover:text-indigo-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <nav class="px-2 pt-2 pb-4 space-y-1 sm:px-3">
                <a href="#features" class="block px-3 py-2 rounded-md text-base font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100">Features</a>
                <a href="#pricing" class="block px-3 py-2 rounded-md text-base font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100">Pricing</a>
                <a href="#testimonials" class="block px-3 py-2 rounded-md text-base font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100">Testimonials</a>
                <a href="#faq" class="block px-3 py-2 rounded-md text-base font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100">FAQ</a>
                <a href="#" class="block w-full text-left mt-2 bg-indigo-50 text-indigo-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-indigo-100 transition-all">Get Started Free</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-16">

        <!-- Hero Section -->
        <section class="relative py-20 md:py-32 hero-bg-pattern">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl md:text-6xl font-extrabold text-slate-900 tracking-tight">
                    Your 24/7 <span class="gradient-text">AI Support Agent</span> is Here
                </h1>
                <p class="mt-6 max-w-2xl mx-auto text-lg text-slate-600">
                    Automate customer support, increase satisfaction, and reduce costs with our intelligent AI assistant. Never miss a customer query again.
                </p>
                <div class="mt-8 flex justify-center gap-4">
                    <a href="#" class="bg-indigo-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                        Get Started for Free
                    </a>
                    <a href="#" class="bg-white text-slate-700 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-slate-100 transition-all border border-slate-300 shadow-sm">
                        Request a Demo
                    </a>
                </div>
                <div class="mt-16">
                    <img src="https://placehold.co/1200x600/E9D5FF/4C1D95?text=AI+Dashboard+Analytics" alt="AI Dashboard Preview" class="rounded-xl shadow-2xl mx-auto border border-slate-200">
                </div>
            </div>
        </section>

        <!-- Social Proof -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <p class="text-center text-sm font-semibold text-slate-500 uppercase tracking-wider">Trusted by innovative companies worldwide</p>
                <div class="mt-6 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-8 items-center">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=InnovateCo" alt="InnovateCo Logo">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=QuantumLeap" alt="QuantumLeap Logo">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=Apex+Solutions" alt="Apex Solutions Logo">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=FutureTech" alt="FutureTech Logo">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=Synergy" alt="Synergy Logo">
                    <img class="col-span-1 h-8 mx-auto" src="https://placehold.co/150x40/CBD5E1/475569?text=Momentum" alt="Momentum Logo">
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20 md:py-28">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900">Everything you need, nothing you don't.</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-lg text-slate-600">Our AI is built to be powerful, flexible, and incredibly easy to use.</p>
                </div>
                <div class="mt-16 grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Feature 1 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="clock"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">24/7 Automated Support</h3>
                        <p class="mt-2 text-slate-600">Provide instant answers to your customers around the clock, even when your team is offline.</p>
                    </div>
                    <!-- Feature 2 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="zap"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">Seamless Integration</h3>
                        <p class="mt-2 text-slate-600">Connect with your existing tools like Zendesk, Shopify, and Slack in just a few clicks.</p>
                    </div>
                    <!-- Feature 3 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="languages"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">Multi-language Support</h3>
                        <p class="mt-2 text-slate-600">Engage with your global audience by offering support in over 50 languages automatically.</p>
                    </div>
                    <!-- Feature 4 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="brain-circuit"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">Natural Language AI</h3>
                        <p class="mt-2 text-slate-600">Our advanced AI understands context and sentiment for human-like conversations.</p>
                    </div>
                    <!-- Feature 5 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="bar-chart-3"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">Analytics & Reporting</h3>
                        <p class="mt-2 text-slate-600">Gain valuable insights into customer queries and bot performance with a powerful dashboard.</p>
                    </div>
                    <!-- Feature 6 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <div class="bg-indigo-100 text-indigo-600 w-12 h-12 rounded-lg flex items-center justify-center">
                            <i data-lucide="settings-2"></i>
                        </div>
                        <h3 class="mt-6 text-xl font-bold text-slate-900">Easy Customization</h3>
                        <p class="mt-2 text-slate-600">Train your AI on your own data and customize its personality to match your brand voice.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pricing Section -->
        <section id="pricing" class="py-20 md:py-28 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900">Simple, transparent pricing.</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-lg text-slate-600">Choose the plan that's right for your business. No hidden fees.</p>
                </div>
                <div class="mt-16 grid lg:grid-cols-3 gap-8">
                    <!-- Plan 1: Starter -->
                    <div class="border border-slate-200 rounded-xl p-8 flex flex-col">
                        <h3 class="text-lg font-semibold text-indigo-600">Starter</h3>
                        <p class="mt-2 text-slate-500">For startups and small businesses.</p>
                        <div class="mt-4">
                            <span class="text-4xl font-bold text-slate-900">$49</span>
                            <span class="text-slate-500">/ month</span>
                        </div>
                        <a href="#" class="mt-6 w-full bg-slate-100 text-slate-700 text-center py-3 rounded-lg font-semibold hover:bg-slate-200 transition-all">Get Started</a>
                        <ul class="mt-8 space-y-4 text-slate-600 flex-grow">
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>1,000 conversations/mo</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>1 AI Agent</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Basic Analytics</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Email Support</li>
                        </ul>
                    </div>
                    <!-- Plan 2: Pro (Most Popular) -->
                    <div class="border-2 border-indigo-600 rounded-xl p-8 flex flex-col relative">
                        <span class="absolute top-0 -translate-y-1/2 bg-indigo-600 text-white px-3 py-1 text-sm font-semibold rounded-full">Most Popular</span>
                        <h3 class="text-lg font-semibold text-indigo-600">Pro</h3>
                        <p class="mt-2 text-slate-500">For growing businesses that need more.</p>
                        <div class="mt-4">
                            <span class="text-4xl font-bold text-slate-900">$149</span>
                            <span class="text-slate-500">/ month</span>
                        </div>
                        <a href="#" class="mt-6 w-full bg-indigo-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-all shadow-md">Choose Pro</a>
                        <ul class="mt-8 space-y-4 text-slate-600 flex-grow">
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>5,000 conversations/mo</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>3 AI Agents</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Advanced Analytics</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Priority Support</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>CRM Integrations</li>
                        </ul>
                    </div>
                    <!-- Plan 3: Enterprise -->
                    <div class="border border-slate-200 rounded-xl p-8 flex flex-col">
                        <h3 class="text-lg font-semibold text-indigo-600">Enterprise</h3>
                        <p class="mt-2 text-slate-500">For large-scale organizations.</p>
                        <div class="mt-4">
                            <span class="text-4xl font-bold text-slate-900">Custom</span>
                        </div>
                        <a href="#" class="mt-6 w-full bg-slate-100 text-slate-700 text-center py-3 rounded-lg font-semibold hover:bg-slate-200 transition-all">Contact Sales</a>
                        <ul class="mt-8 space-y-4 text-slate-600 flex-grow">
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Unlimited conversations</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Unlimited AI Agents</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Dedicated Account Manager</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-5 h-5 text-green-500 mr-2"></i>Custom Integrations & API</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="py-20 md:py-28">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900">Loved by teams around the world.</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-lg text-slate-600">Don't just take our word for it. Here's what our customers say.</p>
                </div>
                <div class="mt-16 grid lg:grid-cols-3 gap-8">
                    <!-- Testimonial 1 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <p class="text-slate-700">"AuraSupport has been a game-changer for our support team. We've cut down response times by 90% and our customers are happier than ever."</p>
                        <div class="mt-6 flex items-center">
                            <img class="w-12 h-12 rounded-full" src="https://i.pravatar.cc/150?img=1" alt="Sarah Johnson">
                            <div class="ml-4">
                                <p class="font-bold text-slate-900">Sarah Johnson</p>
                                <p class="text-sm text-slate-500">Head of Support, InnovateCo</p>
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial 2 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <p class="text-slate-700">"The setup was incredibly simple. We were up and running in a single afternoon and saw an immediate impact on our support ticket volume."</p>
                        <div class="mt-6 flex items-center">
                            <img class="w-12 h-12 rounded-full" src="https://i.pravatar.cc/150?img=2" alt="Michael Chen">
                            <div class="ml-4">
                                <p class="font-bold text-slate-900">Michael Chen</p>
                                <p class="text-sm text-slate-500">CTO, FutureTech</p>
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial 3 -->
                    <div class="bg-white p-8 rounded-xl shadow-sm border border-slate-200">
                        <p class="text-slate-700">"The analytics dashboard is fantastic. We now have clear insights into what our customers are asking for, which helps inform our product roadmap."</p>
                        <div class="mt-6 flex items-center">
                            <img class="w-12 h-12 rounded-full" src="https://i.pravatar.cc/150?img=3" alt="Jessica Rodriguez">
                            <div class="ml-4">
                                <p class="font-bold text-slate-900">Jessica Rodriguez</p>
                                <p class="text-sm text-slate-500">Product Manager, Apex Solutions</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20 md:py-28 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
                <div class="text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-slate-900">Frequently Asked Questions</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-lg text-slate-600">Have questions? We've got answers.</p>
                </div>
                <div class="mt-12 space-y-4" id="faq-container">
                    <!-- FAQ Item 1 -->
                    <div class="bg-slate-50 rounded-lg border border-slate-200">
                        <button class="faq-question w-full flex justify-between items-center text-left p-6">
                            <span class="text-lg font-semibold text-slate-800">How do I train the AI?</span>
                            <i data-lucide="chevron-down" class="w-5 h-5 text-slate-500 transition-transform"></i>
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-slate-600">
                            <p>Training is simple! You can upload existing documentation, knowledge base articles, or even past customer conversations. Our system will automatically learn from the data. You can also manually add question-and-answer pairs to fine-tune its responses.</p>
                        </div>
                    </div>
                    <!-- FAQ Item 2 -->
                    <div class="bg-slate-50 rounded-lg border border-slate-200">
                        <button class="faq-question w-full flex justify-between items-center text-left p-6">
                            <span class="text-lg font-semibold text-slate-800">Can the AI hand over to a human agent?</span>
                            <i data-lucide="chevron-down" class="w-5 h-5 text-slate-500 transition-transform"></i>
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-slate-600">
                            <p>Absolutely. You can set up rules for when a conversation should be escalated to a human agent. This can be triggered by specific keywords, customer sentiment, or if the AI cannot answer a question. The handover is seamless and includes the full chat history.</p>
                        </div>
                    </div>
                    <!-- FAQ Item 3 -->
                    <div class="bg-slate-50 rounded-lg border border-slate-200">
                        <button class="faq-question w-full flex justify-between items-center text-left p-6">
                            <span class="text-lg font-semibold text-slate-800">What integrations do you support?</span>
                            <i data-lucide="chevron-down" class="w-5 h-5 text-slate-500 transition-transform"></i>
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-slate-600">
                            <p>We offer native integrations with popular platforms like Zendesk, Intercom, Salesforce, Shopify, and Slack. For custom needs, our Pro and Enterprise plans include API access to build your own integrations.</p>
                        </div>
                    </div>
                     <!-- FAQ Item 4 -->
                     <div class="bg-slate-50 rounded-lg border border-slate-200">
                        <button class="faq-question w-full flex justify-between items-center text-left p-6">
                            <span class="text-lg font-semibold text-slate-800">Is my data secure?</span>
                            <i data-lucide="chevron-down" class="w-5 h-5 text-slate-500 transition-transform"></i>
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-slate-600">
                            <p>Yes, data security is our top priority. We are fully GDPR compliant and use industry-standard encryption for data at rest and in transit. Your data is used exclusively to train your own AI model and is never shared.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Final CTA Section -->
        <section class="py-20">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="bg-slate-800 rounded-2xl p-12 text-center shadow-xl">
                    <h2 class="text-3xl md:text-4xl font-bold text-white">Ready to Revolutionize Your Support?</h2>
                    <p class="mt-4 max-w-2xl mx-auto text-lg text-slate-300">Join hundreds of businesses transforming their customer experience. Get started in minutes.</p>
                    <div class="mt-8">
                        <a href="#" class="bg-indigo-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-indigo-500 transition-all shadow-lg transform hover:-translate-y-0.5">
                            Start Your 14-Day Free Trial
                        </a>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-slate-200">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-5 gap-8">
                <div class="md:col-span-2">
                     <a href="#" class="flex items-center space-x-2">
                        <svg width="32" height="32" viewBox="0 0 24 24" class="text-indigo-600" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                        <span class="text-2xl font-bold text-slate-900">AuraSupport</span>
                    </a>
                    <p class="mt-4 text-slate-600 max-w-xs">The future of customer support is here. Intelligent, instant, and always on.</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-slate-900 uppercase tracking-wider">Product</h3>
                    <ul class="mt-4 space-y-3">
                        <li><a href="#features" class="text-slate-600 hover:text-indigo-600">Features</a></li>
                        <li><a href="#pricing" class="text-slate-600 hover:text-indigo-600">Pricing</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Integrations</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Request Demo</a></li>
                    </ul>
                </div>
                 <div>
                    <h3 class="text-sm font-semibold text-slate-900 uppercase tracking-wider">Company</h3>
                    <ul class="mt-4 space-y-3">
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">About Us</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Careers</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Contact</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Blog</a></li>
                    </ul>
                </div>
                 <div>
                    <h3 class="text-sm font-semibold text-slate-900 uppercase tracking-wider">Legal</h3>
                    <ul class="mt-4 space-y-3">
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Privacy Policy</a></li>
                        <li><a href="#" class="text-slate-600 hover:text-indigo-600">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-12 border-t border-slate-200 pt-8 flex flex-col sm:flex-row justify-between items-center">
                <p class="text-sm text-slate-500">&copy; 2024 AuraSupport, Inc. All rights reserved.</p>
                <div class="flex space-x-6 mt-4 sm:mt-0">
                    <a href="#" class="text-slate-500 hover:text-slate-700"><i data-lucide="twitter"></i></a>
                    <a href="#" class="text-slate-500 hover:text-slate-700"><i data-lucide="github"></i></a>
                    <a href="#" class="text-slate-500 hover:text-slate-700"><i data-lucide="linkedin"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide Icons
        lucide.createIcons();

        // Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when a link is clicked
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });
        });

        // FAQ Accordion
        const faqContainer = document.getElementById('faq-container');
        if (faqContainer) {
            faqContainer.addEventListener('click', (e) => {
                const questionButton = e.target.closest('.faq-question');
                if (!questionButton) return;

                const answer = questionButton.nextElementSibling;
                const icon = questionButton.querySelector('i');

                answer.classList.toggle('hidden');
                icon.classList.toggle('rotate-180');
            });
        }
    </script>
</body>
</html>
