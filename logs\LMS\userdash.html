<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - EduCore LMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .sidebar-active {
            transform: translateX(0);
        }
        
        .sidebar {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }
        
        @media (min-width: 1024px) {
            .sidebar {
                transform: translateX(0);
            }
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .animate-pulse-soft {
            animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse-soft {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .8;
            }
        }
        
        .notification-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }
        
        .activity-item {
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            border-left-color: #667eea;
            background-color: #f8fafc;
        }
        
        .calendar-day {
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            background-color: #667eea;
            color: white;
        }
        
        .calendar-day.today {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .calendar-day.has-event {
            background-color: #ddd6fe;
            color: #7c3aed;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="sidebar fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:relative lg:translate-x-0">
        <!-- Logo -->
        <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 gradient-bg rounded-lg flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-white text-lg"></i>
                </div>
                <span class="text-xl font-bold text-gray-900">EduCore</span>
            </div>
            <button id="close-sidebar" class="lg:hidden text-gray-500">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        
        <!-- Navigation -->
        <nav class="mt-6 px-3">
            <div class="space-y-1">
                <a href="#" class="nav-item active bg-blue-50 text-blue-600 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-home mr-3 text-lg"></i>
                    Dashboard
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-book-open mr-3 text-lg"></i>
                    My Courses
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-calendar-alt mr-3 text-lg"></i>
                    Schedule
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-chart-line mr-3 text-lg"></i>
                    Progress
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-users mr-3 text-lg"></i>
                    Community
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-certificate mr-3 text-lg"></i>
                    Certificates
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-envelope mr-3 text-lg"></i>
                    Messages
                    <span class="notification-badge ml-auto text-xs text-white px-2 py-1 rounded-full">3</span>
                </a>
                <a href="#" class="nav-item text-gray-700 hover:bg-gray-50 group flex items-center px-3 py-3 text-sm font-medium rounded-lg">
                    <i class="fas fa-cog mr-3 text-lg"></i>
                    Settings
                </a>
            </div>
        </nav>
        
        <!-- User Profile Section -->
        <div class="absolute bottom-0 w-full p-4 border-t border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center text-white font-semibold mr-3">
                    JD
                </div>
                <div class="flex-1">
                    <h4 class="text-sm font-semibold text-gray-900">John Doe</h4>
                    <p class="text-xs text-gray-500">Premium Student</p>
                </div>
                <button class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile Overlay -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>
    
    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Top Bar -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button id="menu-toggle" class="lg:hidden text-gray-500 mr-4">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Welcome back, John!</h1>
                        <p class="text-sm text-gray-600">Ready to continue your learning journey?</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- Search -->
                    <div class="relative hidden md:block">
                        <input type="text" placeholder="Search courses..." class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    
                    <!-- Notifications -->
                    <button class="relative text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="notification-badge absolute -top-1 -right-1 text-xs text-white px-1.5 py-0.5 rounded-full">2</span>
                    </button>
                    
                    <!-- Profile -->
                    <div class="w-8 h-8 gradient-bg rounded-full flex items-center justify-center text-white font-semibold">
                        JD
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Enrolled Courses -->
                <div class="bg-white rounded-xl p-6 shadow-sm card-hover border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Enrolled Courses</p>
                            <p class="text-3xl font-bold text-gray-900">12</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-book-open text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-green-600 text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>2 new this month</span>
                    </div>
                </div>
                
                <!-- Completed Courses -->
                <div class="bg-white rounded-xl p-6 shadow-sm card-hover border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Completed</p>
                            <p class="text-3xl font-bold text-gray-900">8</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-green-600 text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>3 completed this month</span>
                    </div>
                </div>
                
                <!-- Study Hours -->
                <div class="bg-white rounded-xl p-6 shadow-sm card-hover border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Study Hours</p>
                            <p class="text-3xl font-bold text-gray-900">247</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-green-600 text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>15 hours this week</span>
                    </div>
                </div>
                
                <!-- Certificates -->
                <div class="bg-white rounded-xl p-6 shadow-sm card-hover border border-gray-100">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 mb-1">Certificates</p>
                            <p class="text-3xl font-bold text-gray-900">5</p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-medal text-yellow-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-green-600 text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>1 earned this month</span>
                    </div>
                </div>
            </div>
            
            <!-- Main Dashboard Grid -->
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Left Column -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Continue Learning -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Continue Learning</h2>
                        </div>
                        <div class="p-6 space-y-4">
                            <!-- Course Item 1 -->
                            <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200 cursor-pointer">
                                <div class="w-16 h-16 gradient-bg rounded-lg flex items-center justify-center text-white text-2xl mr-4">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900">Full Stack Web Development</h3>
                                    <p class="text-sm text-gray-600">Lesson 12: Building REST APIs</p>
                                    <div class="mt-2 flex items-center">
                                        <div class="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="progress-bar h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">75%</span>
                                    </div>
                                </div>
                                <button class="ml-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                    Continue
                                </button>
                            </div>
                            
                            <!-- Course Item 2 -->
                            <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200 cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900">Data Science with Python</h3>
                                    <p class="text-sm text-gray-600">Lesson 8: Machine Learning Basics</p>
                                    <div class="mt-2 flex items-center">
                                        <div class="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="progress-bar h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">60%</span>
                                    </div>
                                </div>
                                <button class="ml-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                    Continue
                                </button>
                            </div>
                            
                            <!-- Course Item 3 -->
                            <div class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200 cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center text-white text-2xl mr-4">
                                    <i class="fas fa-paint-brush"></i>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900">UI/UX Design Masterclass</h3>
                                    <p class="text-sm text-gray-600">Lesson 15: Prototyping in Figma</p>
                                    <div class="mt-2 flex items-center">
                                        <div class="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="progress-bar h-2 rounded-full" style="width: 90%"></div>
                                        </div>
                                        <span class="text-sm text-gray-600">90%</span>
                                    </div>
                                </div>
                                <button class="ml-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                    Continue
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Learning Progress Chart -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Learning Progress</h2>
                        </div>
                        <div class="p-6">
                            <canvas id="progressChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Recent Activity</h2>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <!-- Activity Item 1 -->
                            <div class="activity-item p-6 flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-play text-blue-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">
                                        <span class="font-semibold">Completed</span> "Introduction to APIs" in Full Stack Web Development
                                    </p>
                                    <p class="text-xs text-gray-500">2 hours ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity Item 2 -->
                            <div class="activity-item p-6 flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-trophy text-green-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">
                                        <span class="font-semibold">Earned certificate</span> for JavaScript Fundamentals
                                    </p>
                                    <p class="text-xs text-gray-500">1 day ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity Item 3 -->
                            <div class="activity-item p-6 flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-users text-purple-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">
                                        <span class="font-semibold">Joined discussion</span> in Data Science Community
                                    </p>
                                    <p class="text-xs text-gray-500">2 days ago</p>
                                </div>
                            </div>
                            
                            <!-- Activity Item 4 -->
                            <div class="activity-item p-6 flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                                    <i class="fas fa-star text-yellow-600"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-900">
                                        <span class="font-semibold">Rated</span> UI/UX Design Masterclass (5 stars)
                                    </p>
                                    <p class="text-xs text-gray-500">3 days ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-8">
                    <!-- Upcoming Schedule -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Today's Schedule</h2>
                        </div>
                        <div class="p-6 space-y-4">
                            <!-- Schedule Item 1 -->
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900">Live Session: React Hooks</h4>
                                    <p class="text-xs text-gray-600">10:00 AM - 11:30 AM</p>
                                </div>
                            </div>
                            
                            <!-- Schedule Item 2 -->
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900">Assignment Due</h4>
                                    <p class="text-xs text-gray-600">Python Data Analysis Project</p>
                                </div>
                            </div>
                            
                            <!-- Schedule Item 3 -->
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                <div class="flex-1">
                                    <h4 class="text-sm font-semibold text-gray-900">Study Group</h4>
                                    <p class="text-xs text-gray-600">3:00 PM - 4:00 PM</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mini Calendar -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h2 class="text-xl font-bold text-gray-900">July 2025</h2>
                                <div class="flex space-x-2">
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="grid grid-cols-7 gap-1 text-center text-xs text-gray-500 mb-2">
                                <div class="p-2">S</div>
                                <div class="p-2">M</div>
                                <div class="p-2">T</div>
                                <div class="p-2">W</div>
                                <div class="p-2">T</div>
                                <div class="p-2">F</div>
                                <div class="p-2">S</div>
                            </div>
                            <div class="grid grid-cols-7 gap-1 text-sm">
                                <!-- Calendar days -->
                                <div class="calendar-day p-2 text-center text-gray-400">29</div>
                                <div class="calendar-day p-2 text-center text-gray-400">30</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">1</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">2</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">3</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">4</div>
                                <div class="calendar-day p-2 text-center has-event rounded cursor-pointer">5</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">6</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">7</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">8</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">9</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">10</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">11</div>
                                <div class="calendar-day p-2 text-center has-event rounded cursor-pointer">12</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">13</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">14</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">15</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">16</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">17</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">18</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">19</div>
                                <div class="calendar-day today p-2 text-center rounded cursor-pointer">20</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">21</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">22</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">23</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">24</div>
                                <div class="calendar-day p-2 text-center has-event rounded cursor-pointer">25</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">26</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">27</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">28</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">29</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">30</div>
                                <div class="calendar-day p-2 text-center hover:bg-gray-100 rounded cursor-pointer">31</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100">
                        <div class="p-6 border-b border-gray-200">
                            <h2 class="text-xl font-bold text-gray-900">Quick Actions</h2>
                        </div> 
                        <div class="p-6 space-y-4">
                            <button class="w-full p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                <i class="fas fa-plus mr-2"></i>
                                Add New Course
                            </button>
                            <button class="w-full p-3 bg-green-500 text-white rounded-lg hover:bg-green-600"> 
                                <i class="fas fa-check mr-2"></i>
                                Mark Attendance
                            </button>
                            <button class="w-full p-3 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600">
                                <i class="fas fa-edit mr-2"></i>
                                Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
