<?php
$page_title = 'Rankings';
require_once __DIR__ . '/../components/user_header.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Fetch rankings from the database
$ranking_period = $_GET['period'] ?? 'all_time'; // e.g., 'weekly', 'monthly', 'all_time'

try {
    $conn = getConnection();
    
    // Set ranking variable
    $conn->query("SET @curRank := 0");
    
    // Base query
    $query = "
        SELECT u.id, u.full_name as username, u.profile_picture as profile_pic, 
               COALESCE(us.total_score, 0) as total_score, COALESCE(us.streak_count, 0) as streak_count,
               @curRank := @curRank + 1 AS rank
        FROM users u
        LEFT JOIN user_stats us ON u.id = us.user_id
        WHERE u.is_active = 1
        ORDER BY COALESCE(us.total_score, 0) DESC, u.created_at ASC
        LIMIT 100
    ";

    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->get_result();
    $rankings = $result->fetch_all(MYSQLI_ASSOC);
    
    $stmt->close();
    $conn->close();
} catch (Exception $e) {
    $rankings = [];
    error_log("Rankings error: " . $e->getMessage());
}

?>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-trophy me-2"></i>Global Rankings
                </h3>
            </div>
            <div class="card-body">
                <p class="text-secondary mb-4">See how you stack up against other reciters on the platform. Keep practicing to climb the ranks!</p>

                <div class="table-responsive">
                    <table class="table modern-ranking-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>User</th>
                                <th class="text-center">Streak</th>
                                <th class="text-end">Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($rankings)): ?>
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <i class="fas fa-search fa-3x mb-3 text-secondary"></i>
                                        <p class="text-secondary">No rankings available yet.</p>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($rankings as $row): ?>
                                <tr class="<?php echo ($row['id'] == $user_id) ? 'current-user-rank' : ''; ?>">
                                    <td>
                                        <span class="rank-badge rank-<?php echo $row['rank']; ?>"><?php echo $row['rank']; ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if (!empty($row['profile_pic'])): ?>
                                                <img src="uploads/profiles/<?php echo htmlspecialchars($row['profile_pic']); ?>" 
                                                     alt="<?php echo htmlspecialchars($row['username'] ?? 'User'); ?>" class="rounded-circle me-3" width="40" height="40">
                                            <?php else: ?>
                                                <img src="assets/images/default-avatar.png" 
                                                     alt="<?php echo htmlspecialchars($row['username'] ?? 'User'); ?>" class="rounded-circle me-3" width="40" height="40">
                                            <?php endif; ?>
                                            <div>
                                                <div class="fw-semibold"><?php echo htmlspecialchars($row['username'] ?? 'User'); ?></div>
                                                <?php if ($row['id'] == $user_id): ?>
                                                    <small class="badge bg-primary">You</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span><?php echo intval($row['streak_count']); ?> days</span>
                                        <i class="fas fa-fire text-warning ms-1"></i>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold"><?php echo number_format(floatval($row['total_score']), 2); ?></span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>

<style>
:root {
    --primary-red: #B10020;
    --primary-red-light: #D32F2F;
    --primary-red-dark: #8B0000;
    --white: #FFFFFF;
    --dark-text: #1E1E1E;
    --subtle-gray: #F3F3F3;
    --light-gray: #E8E8E8;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(177, 0, 32, 0.1);
    background: var(--white);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
    color: white;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
}

.card-title {
    margin: 0;
    font-weight: 600;
}

.modern-ranking-table {
    border-collapse: separate;
    border-spacing: 0;
}

.modern-ranking-table thead th {
    background: var(--subtle-gray);
    color: var(--dark-text);
    font-weight: 600;
    padding: 1rem;
    border: none;
}

.modern-ranking-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--light-gray);
}

.modern-ranking-table tbody tr:hover {
    background-color: rgba(177, 0, 32, 0.05);
    transform: translateX(4px);
}

.modern-ranking-table tbody td {
    padding: 1rem;
    border: none;
}

.current-user-rank {
    background-color: rgba(177, 0, 32, 0.1) !important;
    border-left: 4px solid var(--primary-red);
}

.rank-badge {
    display: inline-block;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 50%;
    font-weight: bold;
    color: white;
    background: var(--primary-red-dark);
}

.rank-badge.rank-1 { 
    background: linear-gradient(135deg, #FFD700, #FFAA00); 
}

.rank-badge.rank-2 { 
    background: linear-gradient(135deg, #C0C0C0, #A0A0A0); 
}

.rank-badge.rank-3 { 
    background: linear-gradient(135deg, #CD7F32, #A0522D); 
}

.badge {
    border-radius: 12px;
    padding: 0.5rem 1rem;
    font-weight: 600;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light)) !important;
}

.text-warning {
    color: var(--primary-red-light) !important;
}

.text-secondary {
    color: var(--dark-text) !important;
}
</style> 