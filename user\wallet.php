<?php
$page_title = 'My Wallet & Points';
require_once __DIR__ . '/../components/user_header.php';
require_once __DIR__ . '/../includes/points_system.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$conn = getConnection();

// --- Fetch Data ---
// Wallet Balance
$wallet_stmt = $conn->prepare("SELECT balance FROM user_stats WHERE user_id = ?");
$wallet_stmt->bind_param("i", $user_id);
$wallet_stmt->execute();
$wallet_result = $wallet_stmt->get_result()->fetch_assoc();
$balance = $wallet_result['balance'] ?? 0;

// Points Balance
$points_summary = getUserPointsSummary($user_id);
$points_balance = $points_summary['total_points'] ?? 0;

// Transaction History
$trans_stmt = $conn->prepare("
    SELECT amount, type, description, created_at 
    FROM transactions 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$trans_stmt->bind_param("i", $user_id);
$trans_stmt->execute();
$transactions = $trans_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

$conn->close();

// --- Define Conversion Rates ---
define('NAIRA_PER_POINT_BUY', 10);
define('NAIRA_PER_POINT_SELL', 5);
?>

<div class="container-fluid recite-app-body">
    <div class="row">
        <!-- Wallet & Points Summary -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-wallet me-2"></i>My Wallet</h3>
                </div>
                <div class="card-body text-center">
                    <h1 class="display-4 fw-bold text-primary mb-3" id="wallet-balance">₦<?php echo number_format($balance, 2); ?></h1>
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#fundWalletModal"><i class="fas fa-plus me-2"></i>Fund Wallet</button>
                        <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#withdrawalModal"><i class="fas fa-paper-plane me-2"></i>Request Withdrawal</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-star me-2"></i>My Points</h3>
                </div>
                <div class="card-body text-center">
                    <h1 class="display-4 fw-bold text-warning mb-3" id="points-balance"><?php echo number_format($points_balance); ?></h1>
                    <div class="d-grid gap-2">
                        <button class="btn btn-warning text-dark" data-bs-toggle="modal" data-bs-target="#buyPointsModal"><i class="fas fa-shopping-cart me-2"></i>Buy Points</button>
                        <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#sellPointsModal"><i class="fas fa-tag me-2"></i>Sell Points</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h3 class="card-title"><i class="fas fa-exchange-alt me-2"></i>Points Conversion</h3>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Buy Rate</span>
                            <span class="badge bg-primary rounded-pill">1 Point = ₦<?php echo NAIRA_PER_POINT_BUY; ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Sell Rate</span>
                            <span class="badge bg-success rounded-pill">1 Point = ₦<?php echo NAIRA_PER_POINT_SELL; ?></span>
                        </li>
                    </ul>
                    <p class="text-muted small mt-3">Use your wallet balance to buy points, or sell points to top up your wallet.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title"><i class="fas fa-history me-2"></i>Recent Activity</h3>
        </div>
        <div class="card-body">
            <div id="transaction-feedback"></div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody id="transaction-history-body">
                        <?php if (empty($transactions)): ?>
                            <tr><td colspan="3" class="text-center text-secondary py-4">No transactions yet.</td></tr>
                        <?php else: ?>
                            <?php foreach ($transactions as $tx): ?>
                            <tr>
                                <td><?php echo date('M d, Y H:i', strtotime($tx['created_at'])); ?></td>
                                <td><?php echo htmlspecialchars($tx['description'] ?? 'Transaction'); ?></td>
                                <td class="text-end">
                                    <span class="fw-bold text-<?php echo floatval($tx['amount']) > 0 ? 'success' : 'danger'; ?>">
                                        <?php echo (floatval($tx['amount']) > 0 ? '+' : '') . '₦' . number_format(abs(floatval($tx['amount'])), 2); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!-- Fund Wallet Modal -->
<div class="modal fade" id="fundWalletModal" tabindex="-1" aria-labelledby="fundWalletModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fundWalletModalLabel">Fund Your Wallet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Enter the amount you want to add to your wallet.</p>
                <div class="form-group">
                    <label for="fund_amount" class="form-label">Amount (₦)</label>
                    <input type="number" class="form-control" id="fund_amount" name="amount" placeholder="e.g., 5000" min="100" required>
                </div>
                <div id="fund-error" class="text-danger mt-2" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="fundWalletBtn" class="btn btn-primary">Proceed to Pay</button>
            </div>
        </div>
    </div>
</div>

<!-- Withdrawal Modal -->
<div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="withdrawalForm" method="POST" action="../api/wallet_actions.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawalModalLabel">Request Withdrawal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="withdraw">
                    <p>Your current balance is <strong id="withdraw-balance-display">₦<?php echo number_format($balance, 2); ?></strong>.</p>
                    <div class="form-group">
                        <label for="withdraw_amount" class="form-label">Amount to Withdraw</label>
                        <input type="number" class="form-control" id="withdraw_amount" name="amount" placeholder="Amount" step="0.01" max="<?php echo $balance; ?>" required>
                    </div>
                    <p class="text-secondary small mt-2">Note: Withdrawals are processed within 3-5 business days.</p>
                    <div id="withdrawal-feedback" class="mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Request</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Buy Points Modal -->
<div class="modal fade" id="buyPointsModal" tabindex="-1" aria-labelledby="buyPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="buyPointsForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="buyPointsModalLabel">Buy Points</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Your wallet balance: <strong id="buy-points-balance-display">₦<?php echo number_format($balance, 2); ?></strong></p>
                    <div class="form-group">
                        <label for="points_to_buy" class="form-label">Points to Buy</label>
                        <input type="number" class="form-control" id="points_to_buy" name="points" min="1" required>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1">Cost: <strong id="buy-points-cost">₦0.00</strong></p>
                        <p class="text-muted small">Rate: ₦<?php echo NAIRA_PER_POINT_BUY; ?> per point</p>
                    </div>
                    <div id="buy-points-feedback" class="mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Confirm Purchase</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sell Points Modal -->
<div class="modal fade" id="sellPointsModal" tabindex="-1" aria-labelledby="sellPointsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="sellPointsForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="sellPointsModalLabel">Sell Points</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Your points balance: <strong id="sell-points-balance-display"><?php echo number_format($points_balance); ?></strong></p>
                    <div class="form-group">
                        <label for="points_to_sell" class="form-label">Points to Sell</label>
                        <input type="number" class="form-control" id="points_to_sell" name="points" min="1" max="<?php echo $points_balance; ?>" required>
                    </div>
                    <div class="mt-3">
                        <p class="mb-1">You will receive: <strong id="sell-points-value">₦0.00</strong></p>
                        <p class="text-muted small">Rate: ₦<?php echo NAIRA_PER_POINT_SELL; ?> per point</p>
                    </div>
                    <div id="sell-points-feedback" class="mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Confirm Sale</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../components/user_footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const NAIRA_PER_POINT_BUY = <?php echo NAIRA_PER_POINT_BUY; ?>;
    const NAIRA_PER_POINT_SELL = <?php echo NAIRA_PER_POINT_SELL; ?>;

    // --- Paystack Funding Logic ---
    const fundBtn = document.getElementById('fundWalletBtn');
    const fundAmountInput = document.getElementById('fund_amount');
    const fundError = document.getElementById('fund-error');

    if (fundBtn) {
        fundBtn.addEventListener('click', function() {
            const amount = fundAmountInput.value;
            const email = "<?php echo $_SESSION['user_email'] ?? ''; ?>";
            const user_id = "<?php echo $_SESSION['user_id'] ?? ''; ?>";

            if (!amount || parseFloat(amount) < 100) {
                fundError.textContent = 'Please enter an amount of at least ₦100.';
                fundError.style.display = 'block';
                return;
            }
            
            fundError.style.display = 'none';
            fundBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            fundBtn.disabled = true;

            const handler = PaystackPop.setup({
                key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                email: email,
                amount: amount * 100, // in kobo
                currency: 'NGN',
                ref: 'RECITE-' + Math.floor((Math.random() * 1000000000) + 1),
                metadata: { user_id: user_id },
                callback: function(response) {
                    fundBtn.innerHTML = '<i class="fas fa-check"></i> Verifying...';
                    window.location.href = '../verify_payment.php?reference=' + response.reference;
                },
                onClose: function() {
                    fundBtn.innerHTML = 'Proceed to Pay';
                    fundBtn.disabled = false;
                }
            });
            handler.openIframe();
        });
    }

    // --- Buy/Sell Points Calculation ---
    const pointsToBuyInput = document.getElementById('points_to_buy');
    const buyPointsCost = document.getElementById('buy-points-cost');
    pointsToBuyInput.addEventListener('input', () => {
        const points = parseInt(pointsToBuyInput.value) || 0;
        buyPointsCost.textContent = '₦' + (points * NAIRA_PER_POINT_BUY).toFixed(2);
    });

    const pointsToSellInput = document.getElementById('points_to_sell');
    const sellPointsValue = document.getElementById('sell-points-value');
    pointsToSellInput.addEventListener('input', () => {
        const points = parseInt(pointsToSellInput.value) || 0;
        sellPointsValue.textContent = '₦' + (points * NAIRA_PER_POINT_SELL).toFixed(2);
    });

    // --- AJAX Form Submission ---
    const buyPointsForm = document.getElementById('buyPointsForm');
    buyPointsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransaction(
            '../api/buy_points.php', 
            new FormData(buyPointsForm), 
            document.querySelector('#buyPointsModal .btn-primary'),
            'buy-points-feedback'
        );
    });

    const sellPointsForm = document.getElementById('sellPointsForm');
    sellPointsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransaction(
            '../api/sell_points.php', 
            new FormData(sellPointsForm),
            document.querySelector('#sellPointsModal .btn-primary'),
            'sell-points-feedback'
        );
    });
    
    const withdrawalForm = document.getElementById('withdrawalForm');
    withdrawalForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleTransaction(
            '../api/wallet_actions.php',
            new FormData(withdrawalForm),
            document.querySelector('#withdrawalModal .btn-primary'),
            'withdrawal-feedback'
        );
    });

    async function handleTransaction(url, formData, submitBtn, feedbackElId) {
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;

        const feedbackEl = document.getElementById(feedbackElId);
        feedbackEl.innerHTML = '';

        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                feedbackEl.innerHTML = `<div class="alert alert-success">${result.message}</div>`;
                updateBalances(result.data.new_balance, result.data.new_points_balance);
                updateTransactionHistory();
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(feedbackEl.closest('.modal'));
                    modal.hide();
                    feedbackEl.innerHTML = '';
                }, 2000);
            } else {
                feedbackEl.innerHTML = `<div class="alert alert-danger">${result.message}</div>`;
            }
        } catch (error) {
            feedbackEl.innerHTML = `<div class="alert alert-danger">An unexpected error occurred. Please try again.</div>`;
            console.error('Transaction Error:', error);
        } finally {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        }
    }

    function updateBalances(newBalance, newPoints) {
        if (newBalance !== undefined) {
            const formattedBalance = '₦' + parseFloat(newBalance).toFixed(2);
            document.getElementById('wallet-balance').textContent = formattedBalance;
            document.getElementById('withdraw-balance-display').textContent = formattedBalance;
            document.getElementById('buy-points-balance-display').textContent = formattedBalance;
            document.getElementById('withdraw_amount').max = newBalance;
        }
        if (newPoints !== undefined) {
            const formattedPoints = new Intl.NumberFormat().format(newPoints);
            document.getElementById('points-balance').textContent = formattedPoints;
            document.getElementById('sell-points-balance-display').textContent = formattedPoints;
            document.getElementById('points_to_sell').max = newPoints;
        }
    }
    
    async function updateTransactionHistory() {
        try {
            const response = await fetch('../api/wallet_transaction.php');
            const transactions = await response.json();
            const tbody = document.getElementById('transaction-history-body');
            tbody.innerHTML = ''; 

            if (transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-secondary py-4">No transactions yet.</td></tr>';
            } else {
                transactions.forEach(tx => {
                    const amountClass = parseFloat(tx.amount) > 0 ? 'text-success' : 'text-danger';
                    const amountPrefix = parseFloat(tx.amount) > 0 ? '+' : '';
                    const row = `
                        <tr>
                            <td>${new Date(tx.created_at).toLocaleString('en-US', { month: 'short', day: 'numeric', year: 'numeric', hour: '2-digit', minute: '2-digit' })}</td>
                            <td>${tx.description}</td>
                            <td class="text-end">
                                <span class="fw-bold ${amountClass}">
                                    ${amountPrefix}₦${Math.abs(parseFloat(tx.amount)).toFixed(2)}
                                </span>
                            </td>
                        </tr>`;
                    tbody.insertAdjacentHTML('beforeend', row);
                });
            }
        } catch (error) {
            console.error('Failed to update transaction history:', error);
        }
    }
});
</script>
