[25-Jul-2025 09:24:32 UTC] [Withdrawal #2] Processing action 'approve'.
[25-Jul-2025 09:24:32 UTC] [Withdrawal #2] Transaction started.
[25-Jul-2025 09:24:32 UTC] [Withdrawal #2] Current status in DB is: pending
[25-Jul-2025 09:24:32 UTC] [Withdrawal #2] UPDATE executed. Rows affected: 1.
[25-Jul-2025 09:24:32 UTC] [Withdrawal #2] Transaction committed.
[25-Jul-2025 09:25:12 UTC] [Withdrawal #1] Processing action 'approve'.
[25-Jul-2025 09:25:12 UTC] [Withdrawal #1] Transaction started.
[25-Jul-2025 09:25:12 UTC] [Withdrawal #1] Current status in DB is: pending
[25-Jul-2025 09:25:12 UTC] [Withdrawal #1] UPDATE executed. Rows affected: 1.
[25-Jul-2025 09:25:12 UTC] [Withdrawal #1] Transaction committed.
[25-Jul-2025 09:29:36 UTC] [Withdrawal #12] Processing action 'approve'.
[25-Jul-2025 09:29:36 UTC] [Withdra<PERSON> #12] Transaction started.
[25-Jul-2025 09:29:36 UTC] [Withdrawal #12] Current status in DB is: not_found
[25-Jul-2025 09:29:36 UTC] [Withdrawal #12] Aborting: Request is not pending.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] Processing action 'approve'.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] Transaction started.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] Found request in `transactions`. Current status: 'pending'.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] UPDATE on `transactions` executed. Rows affected: 1.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] Transaction committed.
[25-Jul-2025 09:32:06 UTC] [Withdrawal #12] VERIFIED status in DB is now: 'completed'
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] Processing action 'approve'.
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] Transaction started.
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] Found request in `transactions`. Current status: 'pending'.
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] UPDATE on `transactions` executed. Rows affected: 1.
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] Transaction committed.
[25-Jul-2025 09:51:04 UTC] [Withdrawal #11] VERIFIED status in DB is now: 'completed'
[25-Jul-2025 10:07:21 UTC] PHP Warning:  Undefined array key "is_legacy" in C:\laragon\www\RECITE_appbac\RECITE_app\admin\payouts.php on line 429
[25-Jul-2025 10:07:21 UTC] PHP Warning:  Undefined array key "is_legacy" in C:\laragon\www\RECITE_appbac\RECITE_app\admin\payouts.php on line 429
[25-Jul-2025 10:08:19 UTC] Invalid action: delete_request
[25-Jul-2025 10:08:25 UTC] Invalid action: delete_request
[25-Jul-2025 10:08:29 UTC] Invalid action: delete_request
[25-Jul-2025 10:09:28 UTC] Invalid action: delete_request
[25-Jul-2025 10:09:42 UTC] Invalid action: delete_request
[25-Jul-2025 10:10:56 UTC] --- DELETE ACTION TRIGGERED ---
[25-Jul-2025 10:10:56 UTC] Raw POST data: csrf_token=0d29fe3e65612b653894e09cbbed40fc5bbf3145a76fa9ff1ff766c169ae296f&action=delete_request&request_id=7&source_table=transactions
[25-Jul-2025 10:10:56 UTC] Received POST array: Array
(
    [csrf_token] => 0d29fe3e65612b653894e09cbbed40fc5bbf3145a76fa9ff1ff766c169ae296f
    [action] => delete_request
    [request_id] => 7
    [source_table] => transactions
)

[25-Jul-2025 10:10:56 UTC] Processing delete for ID: 7 from table: transactions
[25-Jul-2025 10:10:56 UTC] Attempting to execute DELETE query...
[25-Jul-2025 10:10:56 UTC] DELETE successful. Rows affected: 1
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] Processing action 'approve'.
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] Transaction started.
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] Found request in `withdrawal_requests`. Current status: 'pending'.
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] UPDATE on `withdrawal_requests` executed. Rows affected: 1.
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] Transaction committed.
[25-Jul-2025 10:57:42 UTC] [Withdrawal #3] VERIFIED status in DB is now: 'completed'
[25-Jul-2025 10:57:47 UTC] --- DELETE ACTION TRIGGERED ---
[25-Jul-2025 10:57:47 UTC] Raw POST data: csrf_token=75f0b557d862ffec8d5a8674321d9b80bc3e729747a1a84c930469ba8def709f&action=delete_request&request_id=17&source_table=transactions
[25-Jul-2025 10:57:47 UTC] Received POST array: Array
(
    [csrf_token] => 75f0b557d862ffec8d5a8674321d9b80bc3e729747a1a84c930469ba8def709f
    [action] => delete_request
    [request_id] => 17
    [source_table] => transactions
)

[25-Jul-2025 10:57:47 UTC] Processing delete for ID: 17 from table: transactions
[25-Jul-2025 10:57:47 UTC] Attempting to execute DELETE query...
[25-Jul-2025 10:57:47 UTC] DELETE successful. Rows affected: 1
