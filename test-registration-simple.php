<?php
/**
 * Simple registration test to identify the exact issue
 */
require_once 'config/db_config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$error = '';
$success = '';
$debug_info = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $debug_info[] = "Form submitted via POST";
    
    // Check CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid CSRF token';
        $debug_info[] = "CSRF token validation failed";
    } else {
        $debug_info[] = "CSRF token validated successfully";
        
        // Get form data
        $fullName = sanitize($_POST['full_name'] ?? '');
        $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
        $password = $_POST['password'] ?? '';
        $ward = sanitize($_POST['ward'] ?? '');
        $lga = sanitize($_POST['lga'] ?? '');
        $state = sanitize($_POST['state'] ?? '');
        
        $debug_info[] = "Form data collected: Name=$fullName, Email=$email, Ward=$ward, LGA=$lga, State=$state";
        
        // Validation
        if (empty($fullName) || empty($email) || empty($password) || empty($ward) || empty($lga) || empty($state)) {
            $error = 'All fields are required.';
            $debug_info[] = "Validation failed: Missing required fields";
        } elseif (strlen($password) < 6) {
            $error = 'Password must be at least 6 characters long.';
            $debug_info[] = "Validation failed: Password too short";
        } else {
            $debug_info[] = "Basic validation passed";
            
            try {
                // Check if email already exists
                $debug_info[] = "Checking if email exists...";
                $result = executeQuery(
                    "SELECT id FROM users WHERE email = ?",
                    's',
                    [$email]
                );
                
                if ($result && $result->num_rows > 0) {
                    $error = 'Email already exists. Please use a different email.';
                    $debug_info[] = "Email already exists in database";
                } else {
                    $debug_info[] = "Email is unique, proceeding with registration";
                    
                    // Hash password
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    $debug_info[] = "Password hashed successfully";
                    
                    // Insert user (inactive until payment)
                    $debug_info[] = "Inserting user into database...";
                    $insertResult = executeQuery(
                        "INSERT INTO users (full_name, email, password_hash, ward, lga, state, is_active, payment_verified) VALUES (?, ?, ?, ?, ?, ?, 0, 0)",
                        'ssssss',
                        [$fullName, $email, $passwordHash, $ward, $lga, $state]
                    );
                    
                    if ($insertResult && $insertResult->success) {
                        // executeQuery returns custom object for INSERT queries
                        $userId = $insertResult->insert_id;
                        $debug_info[] = "User inserted successfully with ID: $userId";
                        
                        // Store user ID in session for payment verification
                        $_SESSION['pending_user_id'] = $userId;
                        $_SESSION['user_email'] = $email;
                        $debug_info[] = "Session variables set: pending_user_id=$userId, user_email=$email";
                        
                        $success = 'Registration successful! Please complete the payment to activate your account.';
                        $debug_info[] = "Registration completed successfully";
                    } else {
                        $error = 'Failed to create user account.';
                        $debug_info[] = "Database insert failed";
                    }
                }
            } catch (Exception $e) {
                error_log("Registration error: " . $e->getMessage());
                $error = 'Registration failed: ' . $e->getMessage();
                $debug_info[] = "Exception caught: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Registration Test - Universal Reciters</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://js.paystack.co/v1/inline.js"></script>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="test-card p-4">
                    <h2 class="text-center mb-4">Simple Registration Test</h2>
                    
                    <!-- Debug Information -->
                    <?php if (!empty($debug_info)): ?>
                    <div class="alert alert-info">
                        <h6>Debug Information:</h6>
                        <ul class="mb-0">
                            <?php foreach ($debug_info as $info): ?>
                            <li><?php echo htmlspecialchars($info); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Error Message -->
                    <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $error; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Success Message and Payment Section -->
                    <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success; ?>
                    </div>
                    
                    <!-- Payment Section -->
                    <div id="paymentSection" class="text-center mt-4 p-4 bg-light rounded">
                        <h4 class="text-primary mb-3">₦1,000</h4>
                        <p class="text-muted mb-4">
                            Complete your payment to activate your account
                        </p>
                        <button onclick="payWithPaystack()" class="btn btn-primary btn-lg">
                            <i class="fas fa-credit-card"></i>
                            Pay Now with Paystack
                        </button>
                        <p class="text-sm text-muted mt-3">
                            <i class="fas fa-shield-alt"></i>
                            Secure payment powered by Paystack
                        </p>
                    </div>
                    
                    <!-- Session Debug -->
                    <div class="alert alert-info mt-4">
                        <h6>Session Debug:</h6>
                        <p>Pending User ID: <?php echo $_SESSION['pending_user_id'] ?? 'Not set'; ?></p>
                        <p>User Email: <?php echo $_SESSION['user_email'] ?? 'Not set'; ?></p>
                        <p>Session ID: <?php echo session_id(); ?></p>
                    </div>
                    
                    <?php else: ?>
                    
                    <!-- Registration Form -->
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name"
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                                       placeholder="Enter your full name" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                       placeholder="Enter your email" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password"
                                       placeholder="Enter your password" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="ward" class="form-label">Ward</label>
                                <input type="text" class="form-control" id="ward" name="ward"
                                       value="<?php echo htmlspecialchars($_POST['ward'] ?? ''); ?>"
                                       placeholder="Enter your ward" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="lga" class="form-label">LGA</label>
                                <input type="text" class="form-control" id="lga" name="lga"
                                       value="<?php echo htmlspecialchars($_POST['lga'] ?? ''); ?>"
                                       placeholder="Enter your LGA" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="state" class="form-label">State</label>
                                <input type="text" class="form-control" id="state" name="state"
                                       value="<?php echo htmlspecialchars($_POST['state'] ?? ''); ?>"
                                       placeholder="Enter your state" required>
                            </div>
                            
                            <div class="col-12">
                                <button type="submit" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-user-plus"></i>
                                    Create My Account
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <a href="register.php" class="btn btn-primary me-2">Go to Real Registration</a>
                        <a href="debug-registration.php" class="btn btn-info">Full Debug</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Paystack payment function
        function payWithPaystack() {
            <?php if (isset($_SESSION['pending_user_id'])): ?>
            console.log('Starting Paystack payment...');
            console.log('User ID:', <?php echo $_SESSION['pending_user_id']; ?>);
            console.log('Email:', '<?php echo $_SESSION['user_email']; ?>');
            
            var handler = PaystackPop.setup({
                key: '<?php echo PAYSTACK_PUBLIC_KEY; ?>',
                email: '<?php echo $_SESSION['user_email']; ?>',
                amount: 100000, // ₦1000 in kobo
                currency: 'NGN',
                ref: 'REG_<?php echo $_SESSION['pending_user_id']; ?>_' + Math.floor((Math.random() * 1000000000) + 1),
                metadata: {
                    user_id: <?php echo $_SESSION['pending_user_id']; ?>,
                    purpose: 'registration'
                },
                callback: function(response) {
                    console.log('Payment successful:', response);
                    // Payment successful, verify on server
                    window.location.href = 'verify_payment.php?reference=' + response.reference;
                },
                onClose: function() {
                    console.log('Payment popup closed');
                    alert('Payment was not completed. Please try again.');
                }
            });
            
            handler.openIframe();
            <?php else: ?>
            alert('No pending user session found. Please register first.');
            <?php endif; ?>
        }
        
        // Auto-trigger payment if registration was successful
        <?php if ($success): ?>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Registration successful, auto-triggering payment in 2 seconds...');
            setTimeout(function() {
                payWithPaystack();
            }, 2000);
        });
        <?php endif; ?>
    </script>
</body>
</html>
