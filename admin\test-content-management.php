<?php
/**
 * Test file for Content Management functionality
 * This file helps diagnose database connection and table issues
 */

// Start output buffering
ob_start();

require_once __DIR__ . '/../config/db_config.php';

echo "<h2>Content Management Database Test</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    $conn = getConnection();
    echo "✅ Database connection successful<br>";
    echo "Database: " . DB_NAME . "<br>";
    echo "Host: " . DB_HOST . "<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Check if required tables exist
echo "<h3>2. Table Existence Check</h3>";
$requiredTables = ['users', 'screen_records', 'mirror_recordings', 'comments', 'likes', 'admin_logs'];

foreach ($requiredTables as $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' does not exist<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
    }
}

// Test 3: Check table structures
echo "<h3>3. Table Structure Check</h3>";

// Check screen_records table
try {
    $result = $conn->query("DESCRIBE screen_records");
    if ($result) {
        echo "✅ screen_records table structure:<br>";
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            echo "<li>{$row['Field']} - {$row['Type']}</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "❌ Error checking screen_records structure: " . $e->getMessage() . "<br>";
}

// Check mirror_recordings table
try {
    $result = $conn->query("DESCRIBE mirror_recordings");
    if ($result) {
        echo "✅ mirror_recordings table structure:<br>";
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            echo "<li>{$row['Field']} - {$row['Type']}</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "❌ Error checking mirror_recordings structure: " . $e->getMessage() . "<br>";
}

// Test 4: Sample data check
echo "<h3>4. Sample Data Check</h3>";

try {
    $result = $conn->query("SELECT COUNT(*) as count FROM screen_records");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Screen records count: $count<br>";
    }
} catch (Exception $e) {
    echo "❌ Error counting screen records: " . $e->getMessage() . "<br>";
}

try {
    $result = $conn->query("SELECT COUNT(*) as count FROM mirror_recordings");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "✅ Mirror recordings count: $count<br>";
    }
} catch (Exception $e) {
    echo "❌ Error counting mirror recordings: " . $e->getMessage() . "<br>";
}

// Test 5: Admin functions
echo "<h3>5. Admin Functions Test</h3>";

if (function_exists('isAdmin')) {
    echo "✅ isAdmin() function exists<br>";
} else {
    echo "❌ isAdmin() function missing<br>";
}

if (function_exists('logAdminAction')) {
    echo "✅ logAdminAction() function exists<br>";
} else {
    echo "❌ logAdminAction() function missing<br>";
}

if (function_exists('validateCSRFToken')) {
    echo "✅ validateCSRFToken() function exists<br>";
} else {
    echo "❌ validateCSRFToken() function missing<br>";
}

// Test 6: Session check
echo "<h3>6. Session Check</h3>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ Session is not active<br>";
}

// Test 7: File permissions
echo "<h3>7. File Permissions Check</h3>";
$uploadDir = __DIR__ . '/../uploads/';
if (is_dir($uploadDir)) {
    echo "✅ Uploads directory exists<br>";
    if (is_writable($uploadDir)) {
        echo "✅ Uploads directory is writable<br>";
    } else {
        echo "❌ Uploads directory is not writable<br>";
    }
} else {
    echo "❌ Uploads directory does not exist<br>";
}

$conn->close();

echo "<h3>Test Complete</h3>";
echo "<p><a href='manage-content.php'>Go to Content Management</a></p>";

// End output buffering
ob_end_flush();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #15803d; }
h3 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
