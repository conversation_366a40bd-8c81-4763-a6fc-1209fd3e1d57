<?php
/**
 * Streams Page for Recite! App
 * Clean, Mobile-First Streams with Database Recordings
 */

require_once 'config/db_config.php';

// Require login FIRST - before any output
requireLogin();

$page_title = 'Streams';
$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Initialize variables
$message = '';
$error = '';

// Include video unlock system
require_once 'includes/points_system.php';

// Video unlock function
function unlockVideo($userId, $recordingId, $recordingType) {
    $conn = getConnection();
    
    if (!$conn) {
        return ['success' => false, 'message' => 'Database connection failed'];
    }

    try {
        // Start transaction
        $conn->begin_transaction();

        // Get user's wallet balance
        $userStmt = $conn->prepare("SELECT wallet_balance FROM users WHERE id = ?");
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $user = $userResult->fetch_assoc();
        $userStmt->close();

        if (!$user) {
            $conn->rollback();
            return ['success' => false, 'message' => 'User not found'];
        }

        if ($user['wallet_balance'] < 3.00) {
            $conn->rollback();
            return ['success' => false, 'message' => 'Insufficient wallet balance. Required: ₦3.00'];
        }

        // Check if already unlocked (if video_unlocks table exists)
        try {
            $unlockStmt = $conn->prepare("SELECT id FROM video_unlocks WHERE user_id = ? AND recording_id = ? AND recording_type = ?");
            $unlockStmt->bind_param("iis", $userId, $recordingId, $recordingType);
            $unlockStmt->execute();
            $unlockResult = $unlockStmt->get_result();
            $unlockStmt->close();

            if ($unlockResult->num_rows > 0) {
                $conn->rollback();
                return ['success' => false, 'message' => 'Video already unlocked'];
            }
        } catch (Exception $e) {
            // If video_unlocks table doesn't exist, continue without duplicate check
        }

        // Get recording details and creator
        $tableName = '';
        switch ($recordingType) {
            case 'video':
                $tableName = 'videos';
                break;
            case 'screen_record':
                $tableName = 'screen_records';
                break;
            case 'mirror_record':
                $tableName = 'mirror_recordings';
                break;
            default:
                $conn->rollback();
                return ['success' => false, 'message' => 'Invalid recording type'];
        }

        $recordingStmt = $conn->prepare("SELECT user_id, COALESCE(unlock_price, 3.00) as unlock_price FROM $tableName WHERE id = ?");
        $recordingStmt->bind_param("i", $recordingId);
        $recordingStmt->execute();
        $recordingResult = $recordingStmt->get_result();
        $recording = $recordingResult->fetch_assoc();
        $recordingStmt->close();

        if (!$recording) {
            $conn->rollback();
            return ['success' => false, 'message' => 'Recording not found'];
        }

        $creatorId = $recording['user_id'];
        $unlockPrice = $recording['unlock_price'];
        $creatorAmount = 1.00; // 1 Naira to creator
        $adminAmount = 2.00;   // 2 Naira to admin

        // Deduct from user's wallet
        $updateUserStmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?");
        $updateUserStmt->bind_param("di", $unlockPrice, $userId);
        $updateUserStmt->execute();
        $updateUserStmt->close();

        // Add to creator's wallet
        $updateCreatorStmt = $conn->prepare("UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?");
        $updateCreatorStmt->bind_param("di", $creatorAmount, $creatorId);
        $updateCreatorStmt->execute();
        $updateCreatorStmt->close();

        // Create wallet transaction for user (deduction)
        $userTransactionStmt = $conn->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description) VALUES (?, 'video_unlock', ?, ?)");
        $userDescription = "Unlocked $recordingType #$recordingId for ₦$unlockPrice";
        $userTransactionStmt->bind_param("ids", $userId, $unlockPrice, $userDescription);
        $userTransactionStmt->execute();
        $userTransactionId = $conn->insert_id;
        $userTransactionStmt->close();

        // Create wallet transaction for creator (earnings)
        $creatorTransactionStmt = $conn->prepare("INSERT INTO wallet_transactions (user_id, transaction_type, amount, description) VALUES (?, 'creator_earnings', ?, ?)");
        $creatorDescription = "Earned ₦$creatorAmount from video unlock by user #$userId";
        $creatorTransactionStmt->bind_param("ids", $creatorId, $creatorAmount, $creatorDescription);
        $creatorTransactionStmt->execute();
        $creatorTransactionStmt->close();

        // Create admin earnings record (if table exists)
        try {
            $adminEarningsStmt = $conn->prepare("INSERT INTO admin_earnings (transaction_id, user_id, recording_id, recording_type, amount) VALUES (?, ?, ?, ?, ?)");
            $adminEarningsStmt->bind_param("iiisd", $userTransactionId, $userId, $recordingId, $recordingType, $adminAmount);
            $adminEarningsStmt->execute();
            $adminEarningsStmt->close();
        } catch (Exception $e) {
            // If admin_earnings table doesn't exist, continue
        }

        // Create video unlock record (if table exists)
        try {
            $unlockRecordStmt = $conn->prepare("INSERT INTO video_unlocks (user_id, recording_id, recording_type, unlock_price, creator_amount, admin_amount, creator_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $unlockRecordStmt->bind_param("iisdddi", $userId, $recordingId, $recordingType, $unlockPrice, $creatorAmount, $adminAmount, $creatorId);
            $unlockRecordStmt->execute();
            $unlockRecordStmt->close();
        } catch (Exception $e) {
            // If video_unlocks table doesn't exist, continue
        }

        // Update view count
        $viewCountStmt = $conn->prepare("UPDATE $tableName SET view_count = COALESCE(view_count, 0) + 1 WHERE id = ?");
        $viewCountStmt->bind_param("i", $recordingId);
        $viewCountStmt->execute();
        $viewCountStmt->close();

        // Commit transaction
        $conn->commit();

        return [
                        'success' => true,
            'message' => 'Video unlocked successfully',
            'data' => [
                'unlock_price' => $unlockPrice,
                'creator_amount' => $creatorAmount,
                'admin_amount' => $adminAmount,
                'new_balance' => $user['wallet_balance'] - $unlockPrice
            ]
        ];

    } catch (Exception $e) {
        $conn->rollback();
        return ['success' => false, 'message' => 'Transaction failed: ' . $e->getMessage()];
    } finally {
        $conn->close();
    }
}



// Handle video unlock POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'unlock_video') {
        $recordingId = intval($_POST['recording_id']);
    $recordingType = $_POST['recording_type'];
    
    if ($recordingId > 0 && !empty($recordingType)) {
        // Call the unlock function
        $result = unlockVideo($userId, $recordingId, $recordingType);
        
        if ($result['success']) {
            $message = 'Video unlocked successfully!';
            
            // Store unlock status in session for this user
            if (!isset($_SESSION['unlocked_videos'])) {
                $_SESSION['unlocked_videos'] = [];
            }
            $_SESSION['unlocked_videos'][$recordingType . '_' . $recordingId] = true;
            
                } else {
            $error = $result['message'];
                }
            } else {
        $error = 'Invalid recording data';
    }
}

// Get all recordings from database with interaction data
$conn = getConnection();

// Get screen recordings with unlock status
$screenRecords = [];
$screenRecordsQuery = "SELECT sr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(sr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                       COALESCE(sr.unlock_price, 3.00) as unlock_price,
                       COALESCE(sr.is_locked, 1) as is_locked
                       FROM screen_records sr
                       JOIN users u ON sr.user_id = u.id
                       ORDER BY sr.created_at DESC";
$stmt = $conn->prepare($screenRecordsQuery);
$stmt->execute();
$screenRecordsResult = $stmt->get_result();
if ($screenRecordsResult) {
    while ($row = $screenRecordsResult->fetch_assoc()) {
        // Check if video is unlocked in session
        $unlockKey = 'screen_record_' . $row['id'];
        $row['is_unlocked'] = isset($_SESSION['unlocked_videos'][$unlockKey]) ? 1 : 0;
        $screenRecords[] = $row;
    }
}

// Get mirror recordings with unlock status
$mirrorRecords = [];
$mirrorRecordsQuery = "SELECT mr.*, u.full_name, u.profile_picture,
                       DATE_FORMAT(mr.created_at, '%M %d, %Y at %h:%i %p') as formatted_date,
                       COALESCE(mr.unlock_price, 3.00) as unlock_price,
                       COALESCE(mr.is_locked, 1) as is_locked
                       FROM mirror_recordings mr
                       JOIN users u ON mr.user_id = u.id
                       ORDER BY mr.created_at DESC";
$stmt = $conn->prepare($mirrorRecordsQuery);
$stmt->execute();
$mirrorRecordsResult = $stmt->get_result();
if ($mirrorRecordsResult) {
    while ($row = $mirrorRecordsResult->fetch_assoc()) {
        // Check if video is unlocked in session
        $unlockKey = 'mirror_record_' . $row['id'];
        $row['is_unlocked'] = isset($_SESSION['unlocked_videos'][$unlockKey]) ? 1 : 0;
        $mirrorRecords[] = $row;
    }
}

// Combine all recordings
$allRecordings = array_merge($screenRecords, $mirrorRecords);

// Sort by created_at descending
usort($allRecordings, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline'; object-src 'none';">
    <title><?php echo $page_title; ?> - Qur'an Recite App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Clean Mobile-First Design - Same as Dashboard */
        :root {
            --primary: #1a5f3f;
            --primary-light: #2d7a5a;
            --secondary: #f8f9fa;
            --text: #333;
            --border: #dee2e6;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: var(--text);
            line-height: 1.5;
            padding-bottom: 80px; /* Space for bottom nav */
        }

        .container {
            max-width: 100%;
            padding: 1rem;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }

        .header-center {
            flex: 1;
            max-width: 300px;
            margin: 0 1rem;
        }

        .search-input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 0.75rem;
            text-align: center;
        }

        .welcome-card h2 {
            font-size: 1.1rem;
            margin: 0;
            font-weight: 600;
        }

        .welcome-card p {
            font-size: 0.8rem;
            margin: 0;
            opacity: 0.9;
        }

        /* Cards */
        .card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .card-body {
            padding: 1rem;
        }

        /* Video Grid Layout */
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
            padding: 1rem;
        }

        /* Video Card */
        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        /* Video Thumbnail Container */
        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 180px;
            background: #000;
            overflow: hidden;
        }

        /* Locked Video Overlay */
        .video-lock-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .video-lock-overlay h5 {
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .video-lock-overlay p {
            margin-bottom: 1rem;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .unlock-video-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .unlock-video-btn:hover {
            background: var(--primary-light);
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            border: 1px solid transparent;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .video-thumbnail video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-thumbnail .thumbnail-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .video-card:hover .thumbnail-overlay {
            opacity: 1;
        }

        .play-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--primary);
            transition: transform 0.2s;
        }

        .play-button:hover {
            transform: scale(1.1);
        }

        /* Video Duration Badge */
        .duration-badge {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Video Info */
        .video-info {
            padding: 1rem;
        }

        .video-title {
            font-weight: 600;
            font-size: 0.95rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .video-meta {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .video-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .video-details {
            flex: 1;
            min-width: 0;
        }

        .video-author {
            font-weight: 500;
            font-size: 0.85rem;
            color: var(--text);
            margin-bottom: 2px;
        }

        .video-stats {
            font-size: 0.75rem;
            color: #666;
        }

        /* Video Actions */
        .video-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.75rem;
        }

        .btn-video {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .btn-play {
            background: var(--primary);
            color: white;
        }

        .btn-play:hover {
            background: var(--primary-light);
        }

        .btn-download {
            background: #f8f9fa;
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn-download:hover {
            background: #e9ecef;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            min-height: 36px;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border);
        }



        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid var(--border);
            padding: 0.5rem 0;
            z-index: 100;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--text);
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .bottom-nav-item:hover {
            background: var(--secondary);
            color: var(--primary);
        }

        .bottom-nav-item.active {
            color: var(--primary);
            background: rgba(26, 95, 63, 0.1);
        }

        .bottom-nav-item i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }

        .bottom-nav-item span {
            font-size: 0.7rem;
            font-weight: 500;
            text-align: center;
        }

        /* Video Modal */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            padding: 2rem;
        }

        .video-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            max-width: 90vw;
            max-height: 90vh;
            width: 800px;
            position: relative;
        }

        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0.25rem;
            border-radius: 4px;
        }

        .modal-close:hover {
            background: #f8f9fa;
        }

        .modal-video {
            width: 100%;
            height: 450px;
            background: #000;
        }

        .modal-video video {
            width: 100%;
            height: 100%;
        }

        .modal-info {
            padding: 1.5rem;
        }

        .modal-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .modal-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .modal-details h3 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
        }

        .modal-details p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border);
        }

        /* Responsive */
        @media (min-width: 768px) {
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }

            .video-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        @media (max-width: 767px) {
            .header-center {
                display: none;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.5rem;
            }

            .video-thumbnail {
                height: 200px;
            }

            .modal-content {
                width: 95vw;
                max-height: 95vh;
            }

            .modal-video {
                height: 250px;
            }

            .video-modal {
                padding: 1rem;
            }
        }
    </style>
</head>
<body data-user-id="<?php echo $userId; ?>">
    <!-- Header -->
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <div class="profile-avatar" onclick="window.location.href='profile.php'">
                    <?php if (!empty($user['profile_picture'])): ?>
                        <img src="uploads/profiles/<?php echo htmlspecialchars($user['profile_picture']); ?>"
                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <?php else: ?>
                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="header-center">
                <input type="text" class="search-input" placeholder="Search recordings...">
            </div>

            <div class="header-right">
                <div class="notification-icon" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                    <div class="notification-badge">1</div>
                </div>
            </div>
        </div>

        <div class="welcome-card">
            <h2><i class="fas fa-stream"></i> Streams</h2>
            <p>Watch and share Qur'an recitations</p>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; opacity: 0.9;">
                <i class="fas fa-wallet"></i> Balance: ₦<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>



        <?php if (!empty($allRecordings)): ?>
            <div class="video-grid">
                <?php foreach ($allRecordings as $index => $recording): ?>
                    <?php 
                    $recordingType = isset($recording['record_type']) ? $recording['record_type'] : 
                                   (strpos($recording['file_path'], 'mirror') !== false ? 'mirror_record' : 'screen_record');
                    
                    // Check if user is the owner of this video
                    $isOwner = ($recording['user_id'] == $userId);
                    
                    // Video is unlocked if: user is owner OR video was unlocked via payment
                    $isUnlocked = $isOwner || ($recording['is_unlocked'] ?? 0);
                    $isLocked = ($recording['is_locked'] ?? 1) && !$isUnlocked;
                    ?>
                    <div class="video-card" onclick="<?php echo ($isLocked && !$isOwner) ? 'return false;' : 'openVideoModal(' . $index . ');'; ?>">
                        <div class="video-thumbnail">
                            <?php if ($isLocked): ?>
                                <!-- Locked Video Overlay -->
                                <div class="video-lock-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 10; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-center text-white">
                                        <?php if ($isOwner): ?>
                                            <!-- Owner's own video -->
                                            <i class="fas fa-play-circle fa-3x mb-3"></i>
                                            <h5>Your Video</h5>
                                            <p>Click to play your recording</p>
                                            <button class="btn btn-primary" onclick="event.stopPropagation(); playVideo(<?php echo $index; ?>);">
                                                <i class="fas fa-play"></i> Play Your Video
                                            </button>
                                        <?php else: ?>
                                            <!-- Non-owner needs to pay -->
                                            <i class="fas fa-lock fa-3x mb-3"></i>
                                            <h5>Video Locked</h5>
                                            <p>Unlock this video to watch</p>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to unlock this video for ₦<?php echo $recording['unlock_price']; ?>?');">
                                                <input type="hidden" name="action" value="unlock_video">
                                                <input type="hidden" name="recording_id" value="<?php echo $recording['id']; ?>">
                                                <input type="hidden" name="recording_type" value="<?php echo $recordingType; ?>">
                                                <button type="submit" class="btn btn-primary" onclick="event.stopPropagation();">
                                                    <i class="fas fa-unlock"></i> Unlock for ₦<?php echo $recording['unlock_price']; ?>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <video preload="metadata" muted playsinline <?php echo ($isLocked && !$isOwner) ? 'style="display: none;"' : ''; ?>>
                                <source src="<?php echo htmlspecialchars($recording['file_path']); ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            
                            <?php if (!$isLocked || $isOwner): ?>
                            <div class="thumbnail-overlay">
                                <div class="play-button">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="duration-badge" id="duration-<?php echo $index; ?>">
                                --:--
                            </div>
                        </div>

                        <div class="video-info">
                            <div class="video-title">
                                <?php echo htmlspecialchars($recording['title'] ?? 'Recitation Recording'); ?>
                                <?php if ($isOwner): ?>
                                    <span class="badge bg-success" style="font-size: 0.7rem; margin-left: 0.5rem;">Your Video</span>
                                <?php endif; ?>
                            </div>

                            <div class="video-meta">
                                <a href="profile.php?user_id=<?php echo $recording['user_id']; ?>" class="video-avatar" style="text-decoration: none;">
                                    <?php if (!empty($recording['profile_picture'])): ?>
                                        <img src="uploads/profiles/<?php echo htmlspecialchars($recording['profile_picture']); ?>"
                                             alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <?php echo strtoupper(substr($recording['full_name'], 0, 1)); ?>
                                    <?php endif; ?>
                                </a>

                                <div class="video-details">
                                    <div class="video-author">
                                        <a href="profile.php?user_id=<?php echo $recording['user_id']; ?>" style="text-decoration: none; color: inherit;">
                                            <?php echo htmlspecialchars($recording['full_name']); ?>
                                        </a>
                                    </div>
                                    <div class="video-stats">
                                        <?php echo $recording['formatted_date']; ?>
                                        <?php if (isset($recording['file_size']) && $recording['file_size'] > 0): ?>
                                            • <?php echo number_format($recording['file_size'] / 1024 / 1024, 1); ?>MB
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="video-actions">
                                <?php if ($isLocked): ?>
                                    <?php if ($isOwner): ?>
                                        <!-- Owner can view their own video for free -->
                                <button class="btn-video btn-play" onclick="event.stopPropagation(); playVideo(<?php echo $index; ?>)">
                                            <i class="fas fa-play"></i> Play (Your Video)
                                </button>
                                    <?php else: ?>
                                        <!-- Non-owner needs to pay to unlock -->
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to unlock this video for ₦<?php echo $recording['unlock_price']; ?>?');">
                                            <input type="hidden" name="action" value="unlock_video">
                                            <input type="hidden" name="recording_id" value="<?php echo $recording['id']; ?>">
                                            <input type="hidden" name="recording_type" value="<?php echo $recordingType; ?>">
                                            <button type="submit" class="btn-video btn-play" onclick="event.stopPropagation();">
                                                <i class="fas fa-unlock"></i> Unlock for ₦<?php echo $recording['unlock_price']; ?>
                                </button>
                                        </form>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <button class="btn-video btn-play" onclick="event.stopPropagation(); playVideo(<?php echo $index; ?>)">
                                        <i class="fas fa-play"></i> Play
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-video"></i>
                        <h3>No Recordings Yet</h3>
                        <p>Start recording from the dashboard to see your streams here!</p>
                        <a href="dashboard.php" class="btn btn-primary" style="margin-top: 1rem;">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Video Modal -->
    <div class="video-modal" id="videoModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">Video Player</div>
                <button class="modal-close" onclick="closeVideoModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-video">
                <video id="modalVideo" controls preload="metadata" style="width: 100%; height: 100%;" volume="1.0">
                    <source id="modalVideoSource" src="" type="video/webm">
                    <source id="modalVideoSourceMp4" src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>

            <div class="modal-info">
                <div class="modal-meta">
                    <div class="modal-avatar" id="modalAvatar">
                        U
                    </div>
                    <div class="modal-details">
                        <h3 id="modalAuthor">User Name</h3>
                        <p id="modalDate">Upload Date</p>
                    </div>
                </div>


                </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <a href="dashboard.php" class="bottom-nav-item">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="streams.php" class="bottom-nav-item active">
            <i class="fas fa-video"></i>
            <span>Streams</span>
        </a>
        
        <a href="community.php" class="bottom-nav-item">
            <i class="fas fa-users"></i>
            <span>Community</span>
        </a>
        <a href="wallet.php" class="bottom-nav-item">
            <i class="fas fa-wallet"></i>
            <span>Wallet</span>
        </a>
        <a href="profile.php" class="bottom-nav-item">
            <i class="fas fa-user-circle"></i>
            <span>Profile</span>
        </a>
    </nav>

    <script>
        // Video data for JavaScript
        const videoData = <?php echo json_encode($allRecordings); ?>;

        // Load video durations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadVideoDurations();
        });

        // Load video durations from actual video elements
        function loadVideoDurations() {
            const videos = document.querySelectorAll('.video-thumbnail video');
            videos.forEach((video, index) => {
                video.addEventListener('loadedmetadata', function() {
                    const duration = formatDuration(video.duration);
                    const durationBadge = document.getElementById(`duration-${index}`);
                    if (durationBadge) {
                        durationBadge.textContent = duration;
                    }
                });

                // Trigger load
                video.load();
            });
        }

        // Format duration in MM:SS format
        function formatDuration(seconds) {
            if (isNaN(seconds) || seconds === 0) return '--:--';

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Play video (opens modal directly)
        function playVideo(index) {
            openVideoModal(index);
        }

        // Open video modal
        function openVideoModal(index) {
            const recording = videoData[index];
            if (!recording) return;

            // Set current modal recording for social interactions
            currentModalRecording = recording;

            // Update modal content
            document.getElementById('modalTitle').textContent = recording.title || 'Recitation Recording';
            document.getElementById('modalAuthor').textContent = recording.full_name;
            document.getElementById('modalDate').textContent = recording.formatted_date;
            document.getElementById('modalVideoSource').src = recording.file_path;
            document.getElementById('modalVideoSourceMp4').src = recording.file_path;

            // Update avatar
            const modalAvatar = document.getElementById('modalAvatar');
            if (recording.profile_picture) {
                modalAvatar.innerHTML = `<img src="uploads/profiles/${recording.profile_picture}" alt="Profile" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
            } else {
                modalAvatar.textContent = recording.full_name.charAt(0).toUpperCase();
            }



            // Load and show modal
            const modalVideo = document.getElementById('modalVideo');
            modalVideo.load();

            // Ensure audio is enabled
            modalVideo.muted = false;
            modalVideo.volume = 1.0;

            const modal = document.getElementById('videoModal');
            modal.classList.add('active');

            // Auto-play the video
            modalVideo.play().catch(e => {
                console.log('Auto-play prevented by browser policy');
            });
        }



        // Close video modal
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const modalVideo = document.getElementById('modalVideo');

            modalVideo.pause();
            modalVideo.currentTime = 0;
            modal.classList.remove('active');

            // Clear current modal recording
            currentModalRecording = null;


        }

        // Close modal when clicking outside
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideoModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('videoModal');
            if (modal.classList.contains('active')) {
                if (e.key === 'Escape') {
                    closeVideoModal();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    const video = document.getElementById('modalVideo');
                    if (video.paused) {
                        video.play();
                    } else {
                        video.pause();
                    }
                }
            }
        });

        // Show notifications
        function showNotifications() {
            alert('Streams Notifications:\n• New recording uploaded');
        }

        // Current modal recording data
        let currentModalRecording = null;

        // Optimize video loading
        function optimizeVideoLoading() {
            const videos = document.querySelectorAll('.video-thumbnail video');

            // Use Intersection Observer for lazy loading
            if ('IntersectionObserver' in window) {
                const videoObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const video = entry.target;
                            if (!video.src && video.querySelector('source')) {
                                video.load();
                            }
                            videoObserver.unobserve(video);
                        }
                    });
                });

                videos.forEach(video => {
                    videoObserver.observe(video);
                });
            }
        }

        // Show message function
        function showMessage(message, type) {
            // Create message element
            const messageDiv = document.createElement('div');
            let alertClass, iconClass, bgColor;

            switch(type) {
                case 'success':
                    alertClass = 'alert-success';
                    iconClass = 'check-circle';
                    bgColor = '#d4edda';
                    break;
                case 'info':
                    alertClass = 'alert-info';
                    iconClass = 'info-circle';
                    bgColor = '#d1ecf1';
                    break;
                case 'error':
                default:
                    alertClass = 'alert-danger';
                    iconClass = 'exclamation-circle';
                    bgColor = '#f8d7da';
                    break;
            }

            messageDiv.className = `alert ${alertClass}`;
            messageDiv.style.cssText = `position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 300px; padding: 1rem; border-radius: 8px; background-color: ${bgColor}; border: 1px solid #dee2e6;`;
            messageDiv.innerHTML = `
                <i class="fas fa-${iconClass}"></i> ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
            `;

            document.body.appendChild(messageDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }

        // Initialize optimizations
        document.addEventListener('DOMContentLoaded', function() {
            optimizeVideoLoading();
        });



    </script>
    

</body>
</html>

