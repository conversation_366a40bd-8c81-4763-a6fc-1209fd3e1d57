/**
 * Video Unlock System JavaScript
 * Handles video unlock functionality with 3 Naira payment split
 */

class VideoUnlockSystem {
    constructor() {
        this.apiUrl = '/api/unlock_video.php';
        this.currentUserId = null;
        this.init();
    }

    init() {
        // Get current user ID from session or data attribute
        this.currentUserId = document.querySelector('[data-user-id]')?.dataset.userId;
        if (!this.currentUserId) {
            console.error('User ID not found');
            return;
        }

        this.bindEvents();
    }

    bindEvents() {
        // Bind unlock buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.unlock-video-btn')) {
                e.preventDefault();
                this.handleUnlockClick(e.target);
            }
        });

        // Check unlock status for videos on page load
        this.checkUnlockStatuses();
    }

    async handleUnlockClick(button) {
        const recordingId = button.dataset.recordingId;
        const recordingType = button.dataset.recordingType;
        const unlockPrice = button.dataset.unlockPrice || '3.00';

        if (!recordingId || !recordingType) {
            this.showError('Invalid recording data');
            return;
        }

        // Show confirmation dialog
        const confirmed = await this.showConfirmationDialog(unlockPrice);
        if (!confirmed) return;

        // Disable button and show loading
        button.disabled = true;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        try {
            const result = await this.unlockVideo(recordingId, recordingType);
            
            if (result.success) {
                this.showSuccess('Video unlocked successfully!');
                this.updateVideoDisplay(recordingId, recordingType);
                this.updateWalletBalance(result.data.new_balance);
            } else {
                this.showError(result.message);
                button.disabled = false;
                button.innerHTML = originalText;
            }
        } catch (error) {
            console.error('Unlock error:', error);
            this.showError('Failed to unlock video. Please try again.');
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    async unlockVideo(recordingId, recordingType) {
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'unlock',
                user_id: this.currentUserId,
                recording_id: recordingId,
                recording_type: recordingType
            })
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        return await response.json();
    }

    async checkUnlockStatus(recordingId, recordingType) {
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'check_status',
                user_id: this.currentUserId,
                recording_id: recordingId,
                recording_type: recordingType
            })
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        return await response.json();
    }

    async checkUnlockStatuses() {
        const videoElements = document.querySelectorAll('[data-recording-id]');
        
        for (const element of videoElements) {
            const recordingId = element.dataset.recordingId;
            const recordingType = element.dataset.recordingType;

            if (recordingId && recordingType) {
                try {
                    const result = await this.checkUnlockStatus(recordingId, recordingType);
                    if (result.success && result.is_unlocked) {
                        this.updateVideoDisplay(recordingId, recordingType);
                    }
                } catch (error) {
                    console.error('Error checking unlock status:', error);
                }
            }
        }
    }

    updateVideoDisplay(recordingId, recordingType) {
        // Find all elements related to this recording
        const elements = document.querySelectorAll(`[data-recording-id="${recordingId}"][data-recording-type="${recordingType}"]`);
        
        elements.forEach(element => {
            // Remove lock overlay
            const lockOverlay = element.querySelector('.video-lock-overlay');
            if (lockOverlay) {
                lockOverlay.remove();
            }

            // Enable video player
            const videoPlayer = element.querySelector('video');
            if (videoPlayer) {
                videoPlayer.controls = true;
                videoPlayer.style.pointerEvents = 'auto';
            }

            // Update unlock button
            const unlockBtn = element.querySelector('.unlock-video-btn');
            if (unlockBtn) {
                unlockBtn.innerHTML = '<i class="fas fa-check"></i> Unlocked';
                unlockBtn.classList.remove('btn-primary');
                unlockBtn.classList.add('btn-success');
                unlockBtn.disabled = true;
            }

            // Show video content
            const videoContent = element.querySelector('.video-content');
            if (videoContent) {
                videoContent.style.display = 'block';
            }
        });
    }

    updateWalletBalance(newBalance) {
        // Update wallet balance display
        const balanceElements = document.querySelectorAll('.wallet-balance');
        balanceElements.forEach(element => {
            element.textContent = `₦${parseFloat(newBalance).toFixed(2)}`;
        });

        // Show balance update notification
        this.showSuccess(`Wallet balance updated: ₦${parseFloat(newBalance).toFixed(2)}`);
    }

    async showConfirmationDialog(unlockPrice) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Unlock Video</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure you want to unlock this video?</p>
                            <div class="alert alert-info">
                                <strong>Cost:</strong> ₦${unlockPrice}<br>
                                <small>₦1.00 goes to creator, ₦2.00 goes to admin</small>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary confirm-unlock">Confirm Unlock</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            modal.querySelector('.confirm-unlock').addEventListener('click', () => {
                bootstrapModal.hide();
                resolve(true);
            });

            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
                resolve(false);
            });
        });
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'danger');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Utility method to create locked video element
    static createLockedVideoElement(recordingId, recordingType, unlockPrice = '3.00') {
        return `
            <div class="video-container position-relative" data-recording-id="${recordingId}" data-recording-type="${recordingType}">
                <div class="video-lock-overlay position-absolute w-100 h-100 d-flex align-items-center justify-content-center" 
                     style="background: rgba(0,0,0,0.7); z-index: 10;">
                    <div class="text-center text-white">
                        <i class="fas fa-lock fa-3x mb-3"></i>
                        <h5>Video Locked</h5>
                        <p>Unlock this video to watch</p>
                        <button class="btn btn-primary unlock-video-btn" 
                                data-recording-id="${recordingId}" 
                                data-recording-type="${recordingType}" 
                                data-unlock-price="${unlockPrice}">
                            <i class="fas fa-unlock"></i> Unlock for ₦${unlockPrice}
                        </button>
                    </div>
                </div>
                <div class="video-content" style="display: none;">
                    <!-- Video content will be shown here when unlocked -->
                </div>
            </div>
        `;
    }
}

// Initialize video unlock system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.videoUnlockSystem = new VideoUnlockSystem();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VideoUnlockSystem;
} 