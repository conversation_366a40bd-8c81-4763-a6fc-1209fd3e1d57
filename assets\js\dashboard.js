// Dashboard JavaScript Functions
let currentStream = null;
let mediaRecorder = null;
let recordedChunks = [];

// Start Camera
function startCamera() {
    // Request both video and audio with specific constraints for better audio quality
    const constraints = {
        video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
        },
        audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100,
            channelCount: 2
        }
    };

    navigator.mediaDevices.getUserMedia(constraints)
        .then(function(stream) {
            currentStream = stream;
            const video = document.getElementById('selfie-video');
            const placeholder = document.getElementById('camera-placeholder');

            video.srcObject = stream;
            video.play();
            video.style.display = 'block';
            placeholder.style.display = 'none';

            document.getElementById('start-camera-btn').style.display = 'none';
            document.getElementById('stop-camera-btn').style.display = 'block';
            document.getElementById('start-record-btn').style.display = 'block';

            console.log('Camera and microphone started successfully');
            console.log('Audio tracks:', stream.getAudioTracks().length);
            console.log('Video tracks:', stream.getVideoTracks().length);
        })
        .catch(function(error) {
            alert('Camera or microphone access denied. Please allow both camera and microphone permissions for recording.');
            console.error('Camera/Microphone error:', error);
        });
}

// Stop Camera
function stopCamera() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
    
    const video = document.getElementById('selfie-video');
    const placeholder = document.getElementById('camera-placeholder');
    
    video.style.display = 'none';
    placeholder.style.display = 'block';
    
    document.getElementById('start-camera-btn').style.display = 'block';
    document.getElementById('stop-camera-btn').style.display = 'none';
    document.getElementById('start-record-btn').style.display = 'none';
    document.getElementById('stop-record-btn').style.display = 'none';
}

// Start Recording
function startRecording() {
    if (currentStream) {
        recordedChunks = [];

        // Check audio tracks before recording
        const audioTracks = currentStream.getAudioTracks();
        const videoTracks = currentStream.getVideoTracks();

        console.log('Starting recording with:', audioTracks.length, 'audio tracks and', videoTracks.length, 'video tracks');

        if (audioTracks.length === 0) {
            alert('No microphone detected. Please ensure microphone access is granted and try again.');
            return;
        }

        // Configure MediaRecorder with audio-optimized settings
        let options = {};

        // Try different codec combinations for best audio support
        if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
            options.mimeType = 'video/webm;codecs=vp9,opus';
        } else if (MediaRecorder.isTypeSupported('video/webm;codecs=vp8,opus')) {
            options.mimeType = 'video/webm;codecs=vp8,opus';
        } else if (MediaRecorder.isTypeSupported('video/webm;codecs=opus')) {
            options.mimeType = 'video/webm;codecs=opus';
        } else {
            options.mimeType = 'video/webm';
        }

        // Set bitrates for quality audio
        options.videoBitsPerSecond = 2500000; // 2.5 Mbps for video
        options.audioBitsPerSecond = 128000;  // 128 kbps for audio

        console.log('Using MediaRecorder options:', options);

        mediaRecorder = new MediaRecorder(currentStream, options);

        mediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                recordedChunks.push(event.data);
                console.log('Data chunk received:', event.data.size, 'bytes');
            }
        };

        mediaRecorder.onstop = function() {
            console.log('Recording stopped. Total chunks:', recordedChunks.length);

            const blob = new Blob(recordedChunks, { type: options.mimeType || 'video/webm' });
            const filename = 'recitation-' + new Date().getTime() + '.webm';

            console.log('Created blob:', blob.size, 'bytes, type:', blob.type);

            // Save to database
            saveRecordingToDatabase(blob, filename);
        };

        mediaRecorder.onerror = function(event) {
            console.error('MediaRecorder error:', event.error);
            alert('Recording error: ' + event.error.message);
        };

        // Start recording with data collection every second
        mediaRecorder.start(1000);

        document.getElementById('start-record-btn').style.display = 'none';
        document.getElementById('stop-record-btn').style.display = 'block';

        console.log('Recording started successfully');
    } else {
        alert('Please start the camera first before recording.');
    }
}

// Stop Recording
function stopRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
        
        document.getElementById('start-record-btn').style.display = 'block';
        document.getElementById('stop-record-btn').style.display = 'none';
    }
}

// Save recording to database
function saveRecordingToDatabase(blob, filename) {
    const formData = new FormData();
    formData.append('recording', blob, filename);
    formData.append('action', 'save_recording');
    
    fetch('save_recording.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.text();
    })
    .then(text => {
        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('Recording saved successfully!');
                window.location.reload();
            } else {
                alert('Failed to save recording: ' + data.message);
            }
        } catch (e) {
            console.error('JSON parse error:', e);
            console.error('Response text:', text);
            alert('Error saving recording: Invalid server response');
        }
    })
    .catch(error => {
        console.error('Error saving recording:', error);
        alert('Error saving recording: ' + error.message);
    });
}

// Toggle balance visibility
let balanceVisible = true;
function toggleBalance() {
    const balanceDisplay = document.getElementById('balance-display');
    const eyeIcon = document.getElementById('balance-eye');
    
    if (balanceVisible) {
        balanceDisplay.textContent = '****';
        eyeIcon.className = 'fas fa-eye-slash';
        balanceVisible = false;
    } else {
        const balance = balanceDisplay.getAttribute('data-balance');
        balanceDisplay.textContent = balance;
        eyeIcon.className = 'fas fa-eye';
        balanceVisible = true;
    }
}

// Show notifications
function showNotifications() {
    alert('Notifications:\n• New Surah unlocked!\n• Recording saved successfully\n• Welcome to RECITE!');
}
