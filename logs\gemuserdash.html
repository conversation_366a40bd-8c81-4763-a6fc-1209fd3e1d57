<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AuraSupport</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <style>
        /* Custom styles for the page */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9; /* slate-100 */
        }
        /* Custom scrollbar for webkit browsers */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="text-slate-800">

    <div class="flex h-screen bg-slate-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="w-64 bg-white border-r border-slate-200 flex-shrink-0 transition-all duration-300 ease-in-out lg:translate-x-0 -translate-x-full fixed lg:relative z-40 h-full flex flex-col">
            <!-- Logo -->
            <div class="px-6 h-16 flex items-center border-b border-slate-200">
                <a href="#" class="flex items-center space-x-2">
                    <svg width="28" height="28" viewBox="0 0 24 24" class="text-indigo-600" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                    <span class="text-xl font-bold text-slate-900">AuraSupport</span>
                </a>
            </div>
            
            <!-- Navigation -->
            <nav class="flex-grow px-4 py-4">
                <ul class="space-y-1">
                    <li>
                        <a href="#" class="flex items-center bg-indigo-50 text-indigo-600 px-4 py-2.5 rounded-lg text-sm font-semibold">
                            <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center text-slate-600 hover:bg-slate-50 hover:text-slate-900 px-4 py-2.5 rounded-lg text-sm font-medium">
                            <i data-lucide="message-square" class="w-5 h-5 mr-3"></i>
                            Conversations
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center text-slate-600 hover:bg-slate-50 hover:text-slate-900 px-4 py-2.5 rounded-lg text-sm font-medium">
                            <i data-lucide="bot" class="w-5 h-5 mr-3"></i>
                            AI Agents
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center text-slate-600 hover:bg-slate-50 hover:text-slate-900 px-4 py-2.5 rounded-lg text-sm font-medium">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-3"></i>
                            Analytics
                        </a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center text-slate-600 hover:bg-slate-50 hover:text-slate-900 px-4 py-2.5 rounded-lg text-sm font-medium">
                            <i data-lucide="plug-zap" class="w-5 h-5 mr-3"></i>
                            Integrations
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Sidebar Footer -->
            <div class="px-4 py-4 border-t border-slate-200">
                 <a href="#" class="flex items-center text-slate-600 hover:bg-slate-50 hover:text-slate-900 px-4 py-2.5 rounded-lg text-sm font-medium">
                    <i data-lucide="settings" class="w-5 h-5 mr-3"></i>
                    Settings
                </a>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="h-16 bg-white border-b border-slate-200 flex items-center justify-between px-4 sm:px-6 lg:px-8">
                <!-- Mobile Menu Toggle & Page Title -->
                <div class="flex items-center">
                    <button id="menu-toggle" class="lg:hidden -ml-2 p-2 text-slate-600 hover:text-indigo-600">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h1 class="text-xl font-semibold text-slate-900 ml-2 lg:ml-0">Dashboard</h1>
                </div>

                <!-- Search & User Profile -->
                <div class="flex items-center space-x-4">
                    <div class="relative hidden sm:block">
                        <i data-lucide="search" class="w-5 h-5 text-slate-400 absolute top-1/2 left-3 -translate-y-1/2"></i>
                        <input type="text" placeholder="Search..." class="w-full max-w-xs pl-10 pr-4 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500">
                    </div>

                    <button class="p-2 text-slate-600 hover:text-indigo-600 rounded-full hover:bg-slate-100">
                        <i data-lucide="bell" class="w-5 h-5"></i>
                    </button>

                    <div class="relative">
                        <button id="user-menu-button" class="flex items-center space-x-2">
                            <img src="https://i.pravatar.cc/150?img=1" alt="User avatar" class="w-9 h-9 rounded-full border-2 border-white shadow-sm">
                            <span class="hidden md:inline text-sm font-medium text-slate-700">Sarah Johnson</span>
                            <i data-lucide="chevron-down" class="w-4 h-4 text-slate-500 hidden md:inline"></i>
                        </button>
                        <!-- User dropdown menu -->
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-1 z-20">
                            <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Your Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100">Settings</a>
                            <div class="border-t border-slate-100 my-1"></div>
                            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">Sign out</a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page content -->
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-slate-100">
                <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Stat cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-500">Total Conversations</h3>
                                <i data-lucide="message-square" class="w-5 h-5 text-indigo-500"></i>
                            </div>
                            <p class="mt-2 text-3xl font-bold text-slate-900">12,432</p>
                            <p class="mt-1 text-sm text-green-600 flex items-center">+12.5% this month</p>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-500">Resolution Rate</h3>
                                <i data-lucide="check-circle-2" class="w-5 h-5 text-green-500"></i>
                            </div>
                            <p class="mt-2 text-3xl font-bold text-slate-900">92.8%</p>
                            <p class="mt-1 text-sm text-green-600 flex items-center">+1.2%</p>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-500">Avg. Response Time</h3>
                                <i data-lucide="clock" class="w-5 h-5 text-amber-500"></i>
                            </div>
                            <p class="mt-2 text-3xl font-bold text-slate-900">8.2s</p>
                            <p class="mt-1 text-sm text-red-600 flex items-center">-0.5s</p>
                        </div>
                        <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-500">Customer Satisfaction</h3>
                                <i data-lucide="smile" class="w-5 h-5 text-pink-500"></i>
                            </div>
                            <p class="mt-2 text-3xl font-bold text-slate-900">98%</p>
                            <p class="mt-1 text-sm text-slate-500 flex items-center">Excellent</p>
                        </div>
                    </div>

                    <!-- Main chart and agent performance -->
                    <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Conversation Volume Chart -->
                        <div class="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-900">Conversation Volume</h3>
                            <p class="text-sm text-slate-500">Last 7 days</p>
                            <div class="mt-4">
                                <img src="https://placehold.co/800x300/E9D5FF/4C1D95?text=Conversation+Chart" 
                                     alt="Conversation Volume Chart" 
                                     class="w-full h-auto rounded-lg"
                                     onerror="this.onerror=null;this.src='https://placehold.co/800x300/E9D5FF/4C1D95?text=Conversation+Chart';">
                            </div>
                        </div>

                        <!-- Agent Performance -->
                        <div class="bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                            <h3 class="text-lg font-semibold text-slate-900">Agent Performance</h3>
                            <div class="mt-4 space-y-4">
                                <div>
                                    <div class="flex justify-between text-sm font-medium mb-1">
                                        <span>Sales Bot</span>
                                        <span>1,230 convos</span>
                                    </div>
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-indigo-600 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm font-medium mb-1">
                                        <span>Support Bot</span>
                                        <span>980 convos</span>
                                    </div>
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm font-medium mb-1">
                                        <span>Onboarding Bot</span>
                                        <span>450 convos</span>
                                    </div>
                                    <div class="w-full bg-slate-200 rounded-full h-2">
                                        <div class="bg-amber-500 h-2 rounded-full" style="width: 30%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Conversations Table -->
                    <div class="mt-8 bg-white p-6 rounded-xl shadow-sm border border-slate-200">
                        <h3 class="text-lg font-semibold text-slate-900">Recent Conversations</h3>
                        <div class="mt-4 overflow-x-auto">
                            <table class="w-full text-left">
                                <thead>
                                    <tr class="text-xs font-semibold text-slate-500 uppercase border-b border-slate-200">
                                        <th class="px-4 py-3">Customer</th>
                                        <th class="px-4 py-3">Status</th>
                                        <th class="px-4 py-3">Agent</th>
                                        <th class="px-4 py-3">Last Update</th>
                                        <th class="px-4 py-3"></th>
                                    </tr>
                                </thead>
                                <tbody class="text-sm">
                                    <tr class="border-b border-slate-100 hover:bg-slate-50">
                                        <td class="px-4 py-3 font-medium text-slate-900">Alex Thompson</td>
                                        <td class="px-4 py-3"><span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Resolved</span></td>
                                        <td class="px-4 py-3 text-slate-600">Support Bot</td>
                                        <td class="px-4 py-3 text-slate-600">2 min ago</td>
                                        <td class="px-4 py-3"><a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View</a></td>
                                    </tr>
                                    <tr class="border-b border-slate-100 hover:bg-slate-50">
                                        <td class="px-4 py-3 font-medium text-slate-900">Maria Garcia</td>
                                        <td class="px-4 py-3"><span class="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Pending</span></td>
                                        <td class="px-4 py-3 text-slate-600">Sales Bot</td>
                                        <td class="px-4 py-3 text-slate-600">15 min ago</td>
                                        <td class="px-4 py-3"><a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View</a></td>
                                    </tr>
                                    <tr class="border-b border-slate-100 hover:bg-slate-50">
                                        <td class="px-4 py-3 font-medium text-slate-900">David Chen</td>
                                        <td class="px-4 py-3"><span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">In Progress</span></td>
                                        <td class="px-4 py-3 text-slate-600">Onboarding Bot</td>
                                        <td class="px-4 py-3 text-slate-600">1 hour ago</td>
                                        <td class="px-4 py-3"><a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View</a></td>
                                    </tr>
                                    <tr class="hover:bg-slate-50">
                                        <td class="px-4 py-3 font-medium text-slate-900">Emily White</td>
                                        <td class="px-4 py-3"><span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Resolved</span></td>
                                        <td class="px-4 py-3 text-slate-600">Support Bot</td>
                                        <td class="px-4 py-3 text-slate-600">3 hours ago</td>
                                        <td class="px-4 py-3"><a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium">View</a></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Initialize Lucide Icons
        lucide.createIcons();

        // Sidebar and User Menu Toggle
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.getElementById('sidebar');
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');

        if (menuToggle && sidebar) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('-translate-x-full');
            });
        }
        
        if (userMenuButton && userMenu) {
            userMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (userMenu && !userMenu.classList.contains('hidden') && !userMenu.contains(e.target) && !userMenuButton.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
