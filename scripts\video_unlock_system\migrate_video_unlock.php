<?php
/**
 * Video Unlock System Database Migration
 * Creates necessary tables and alters existing ones for video unlock functionality
 */

require_once '../../config/db_config.php';

function migrateVideoUnlockSystem() {
    $conn = getConnection();
    
    if (!$conn) {
        die("Connection failed: " . mysqli_connect_error());
    }

    echo "Starting Video Unlock System Migration...\n";

    try {
        // 1. Create video_unlocks table if it doesn't exist
        $sql = "CREATE TABLE IF NOT EXISTS `video_unlocks` (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NOT NULL,
            `recording_id` int NOT NULL,
            `recording_type` enum('screen_record','mirror_record','video') NOT NULL DEFAULT 'video',
            `unlock_price` decimal(10,2) NOT NULL DEFAULT '3.00',
            `creator_amount` decimal(10,2) NOT NULL DEFAULT '1.00',
            `admin_amount` decimal(10,2) NOT NULL DEFAULT '2.00',
            `creator_id` int NOT NULL,
            `status` enum('pending','completed','failed') DEFAULT 'completed',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_user_recording` (`user_id`,`recording_id`,`recording_type`),
            KEY `user_id` (`user_id`),
            KEY `recording_id` (`recording_id`),
            KEY `creator_id` (`creator_id`),
            KEY `status` (`status`),
            CONSTRAINT `video_unlocks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `video_unlocks_ibfk_2` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        if ($conn->query($sql) === TRUE) {
            echo "✓ video_unlocks table created successfully\n";
        } else {
            echo "✗ Error creating video_unlocks table: " . $conn->error . "\n";
        }

        // 2. Alter wallet_transactions table to include video unlock type
        $sql = "ALTER TABLE `wallet_transactions` 
                MODIFY COLUMN `transaction_type` enum('funding','withdrawal','points_purchase','points_sale','video_unlock','creator_earnings','admin_earnings') COLLATE utf8mb4_unicode_ci NOT NULL;";

        if ($conn->query($sql) === TRUE) {
            echo "✓ wallet_transactions table updated successfully\n";
        } else {
            echo "⚠ Note: wallet_transactions may already be updated: " . $conn->error . "\n";
        }

        // 3. Add unlock_price column to videos table
        $sql = "SHOW COLUMNS FROM `videos` LIKE 'unlock_price'";
        $result = $conn->query($sql);
        if ($result->num_rows == 0) {
            $sql = "ALTER TABLE `videos` 
                    ADD COLUMN `unlock_price` decimal(10,2) NOT NULL DEFAULT '3.00',
                    ADD COLUMN `is_locked` tinyint(1) NOT NULL DEFAULT '1',
                    ADD COLUMN `view_count` int NOT NULL DEFAULT '0'";
            
            if ($conn->query($sql) === TRUE) {
                echo "✓ videos table updated successfully\n";
            } else {
                echo "✗ Error updating videos table: " . $conn->error . "\n";
            }
        } else {
            echo "✓ videos table already has unlock_price column\n";
        }

        // 4. Add unlock_price column to screen_records table
        $sql = "SHOW COLUMNS FROM `screen_records` LIKE 'unlock_price'";
        $result = $conn->query($sql);
        if ($result->num_rows == 0) {
            $sql = "ALTER TABLE `screen_records` 
                    ADD COLUMN `unlock_price` decimal(10,2) NOT NULL DEFAULT '3.00',
                    ADD COLUMN `is_locked` tinyint(1) NOT NULL DEFAULT '1',
                    ADD COLUMN `view_count` int NOT NULL DEFAULT '0'";
            
            if ($conn->query($sql) === TRUE) {
                echo "✓ screen_records table updated successfully\n";
            } else {
                echo "✗ Error updating screen_records table: " . $conn->error . "\n";
            }
        } else {
            echo "✓ screen_records table already has unlock_price column\n";
        }

        // 5. Add unlock_price column to mirror_recordings table
        $sql = "SHOW COLUMNS FROM `mirror_recordings` LIKE 'unlock_price'";
        $result = $conn->query($sql);
        if ($result->num_rows == 0) {
            $sql = "ALTER TABLE `mirror_recordings` 
                    ADD COLUMN `unlock_price` decimal(10,2) NOT NULL DEFAULT '3.00',
                    ADD COLUMN `is_locked` tinyint(1) NOT NULL DEFAULT '1',
                    ADD COLUMN `view_count` int NOT NULL DEFAULT '0'";
            
            if ($conn->query($sql) === TRUE) {
                echo "✓ mirror_recordings table updated successfully\n";
            } else {
                echo "✗ Error updating mirror_recordings table: " . $conn->error . "\n";
            }
        } else {
            echo "✓ mirror_recordings table already has unlock_price column\n";
        }

        // 6. Create admin earnings table
        $sql = "CREATE TABLE IF NOT EXISTS `admin_earnings` (
            `id` int NOT NULL AUTO_INCREMENT,
            `transaction_id` int NOT NULL,
            `user_id` int NOT NULL,
            `recording_id` int NOT NULL,
            `recording_type` varchar(50) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `transaction_id` (`transaction_id`),
            KEY `user_id` (`user_id`),
            CONSTRAINT `admin_earnings_ibfk_1` FOREIGN KEY (`transaction_id`) REFERENCES `wallet_transactions` (`id`) ON DELETE CASCADE,
            CONSTRAINT `admin_earnings_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        if ($conn->query($sql) === TRUE) {
            echo "✓ admin_earnings table created successfully\n";
        } else {
            echo "✗ Error creating admin_earnings table: " . $conn->error . "\n";
        }

        // 7. Update existing videos to have unlock_price
        $sql = "UPDATE `videos` SET `unlock_price` = 3.00, `is_locked` = 1 WHERE `is_locked` IS NULL OR `unlock_price` IS NULL;";
        if ($conn->query($sql) === TRUE) {
            $affected = $conn->affected_rows;
            echo "✓ Updated $affected existing videos with unlock settings\n";
        }

        // 8. Update existing recordings to have unlock_price
        $sql = "UPDATE `screen_records` SET `unlock_price` = 3.00, `is_locked` = 1 WHERE `is_locked` IS NULL OR `unlock_price` IS NULL;";
        if ($conn->query($sql) === TRUE) {
            $affected = $conn->affected_rows;
            echo "✓ Updated $affected existing screen records with unlock settings\n";
        }

        $sql = "UPDATE `mirror_recordings` SET `unlock_price` = 3.00, `is_locked` = 1 WHERE `is_locked` IS NULL OR `unlock_price` IS NULL;";
        if ($conn->query($sql) === TRUE) {
            $affected = $conn->affected_rows;
            echo "✓ Updated $affected existing mirror recordings with unlock settings\n";
        }

        echo "\n🎉 Video Unlock System Migration Completed Successfully!\n";
        echo "\nNext steps:\n";
        echo "1. Run the API endpoint setup\n";
        echo "2. Update streams.php to use unlock functionality\n";
        echo "3. Test the unlock system\n";

    } catch (Exception $e) {
        echo "✗ Migration failed: " . $e->getMessage() . "\n";
    } finally {
        $conn->close();
    }
}

// Run migration
migrateVideoUnlockSystem();
?> 