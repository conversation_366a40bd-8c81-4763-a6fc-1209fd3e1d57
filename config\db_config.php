<?php
/**
 * Database Configuration - Single Source of Truth
 * Universal Reciters - https://universalreciters.name.ng/
 * 
 * UPDATE THESE CREDENTIALS FOR YOUR PRODUCTION SERVER
 */

// =============================================================================
// DATABASE CREDENTIALS - UPDATE THESE VALUES
// =============================================================================

define('DB_HOST', 'localhost');
define('DB_NAME', 'recite_app');     // UPDATE: Your actual database name
define('DB_USER', 'root');     // UPDATE: Your actual database username
define('DB_PASS', '');  // UPDATE: Your actual database password
define('DB_CHARSET', 'utf8mb4');

// =============================================================================
// APPLICATION CONFIGURATION
// =============================================================================

// URLs - Will be set after localhost detection

// Application Settings
define('APP_NAME', 'RECITE');
define('REGISTRATION_FEE', 1000);

// Admin Credentials
define('ADMIN_PASSWORD', '1@3Usazladan');
define('DELETE_PASSWORD', '1!3usazladan');

// File Paths
define('UPLOAD_PATH', __DIR__ . '/../uploads/');

// =============================================================================
// PAYSTACK CONFIGURATION - TEST KEYS
// =============================================================================
define('PAYSTACK_PUBLIC_KEY', 'pk_test_a6398b03e7b36dda137bb07664c1301259595dee');
define('PAYSTACK_SECRET_KEY', 'sk_test_de5caee37f77fba3c2db5bc87461c55de7d36d8f');


// define('PAYSTACK_PUBLIC_KEY', 'pk_live_c20feccf2bcd5ce811dfef01c926b30c2ccd9ccd');
// define('PAYSTACK_SECRET_KEY', '************************************************');

// =============================================================================
// ENVIRONMENT DETECTION & ERROR HANDLING
// =============================================================================

// Detect if we're on localhost or live server
$isLocalhost = false;
if (isset($_SERVER['HTTP_HOST'])) {
    $isLocalhost = (
        $_SERVER['HTTP_HOST'] === 'localhost' || 
        $_SERVER['HTTP_HOST'] === '127.0.0.1' || 
        strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
        strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0
    );
} else {
    // CLI mode - assume localhost
    $isLocalhost = true;
}

if ($isLocalhost) {
    // LOCAL DEVELOPMENT SETTINGS
    define('DEVELOPMENT_MODE', true);
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    // PRODUCTION SETTINGS
    define('DEVELOPMENT_MODE', false);
    error_reporting(E_ALL);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    
    // Set custom error log location
    $errorLogPath = __DIR__ . '/../logs/';
    if (!is_dir($errorLogPath)) {
        mkdir($errorLogPath, 0755, true);
    }
    ini_set('error_log', $errorLogPath . 'php_errors.log');
}

// URLs - Auto-detect environment
if ($isLocalhost) {
    define('BASE_URL', 'http://localhost/RECITE_appbac/RECITE_app/');
    define('SITE_URL', 'http://localhost/RECITE_appbac/RECITE_app/');
} else {
    define('BASE_URL', 'https://universalreciters.name.ng/');
    define('SITE_URL', 'https://universalreciters.name.ng/');
}

// =============================================================================
// DATABASE CONNECTION FUNCTION
// =============================================================================

/**
 * Get Database Connection
 * @return mysqli Database connection
 * @throws Exception If connection fails
 */
function getConnection() {
    // Always create a new connection to avoid "connection closed" issues
    // Check if database credentials are configured
    if (DB_PASS === 'your_database_password') {
        $errorMsg = "Database not configured! Please update DB_PASS in config/db_config.php";
        error_log($errorMsg);
        throw new Exception($errorMsg);
    }

    try {
        $connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

        if ($connection->connect_error) {
            $errorMsg = "Database connection failed: " . $connection->connect_error;
            error_log($errorMsg);
            throw new Exception($errorMsg);
        }

        $connection->set_charset(DB_CHARSET);

        return $connection;

    } catch (Exception $e) {
        $errorMsg = "Database error: " . $e->getMessage();
        error_log($errorMsg);
        throw $e;
    }
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Safe Query Execution
 */
function executeQuery($query, $types = '', $params = []) {
    // Get a fresh connection for each query to avoid "connection closed" issues
    $conn = getConnection();

    if (!$conn) {
        throw new Exception("Failed to get database connection");
    }

    try {
        if (empty($params)) {
            $result = $conn->query($query);
            if (!$result) {
                throw new Exception("Query failed: " . $conn->error);
            }
        } else {
            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }

            if (!empty($types) && !empty($params)) {
                $stmt->bind_param($types, ...$params);
            }

            $executeResult = $stmt->execute();
            if (!$executeResult) {
                throw new Exception("Execute failed: " . $stmt->error);
            }

            // For SELECT queries, get the result set
            // For INSERT/UPDATE/DELETE queries, we need to handle differently
            if (stripos(trim($query), 'SELECT') === 0) {
                $result = $stmt->get_result();
                $stmt->close();
            } else {
                // For INSERT/UPDATE/DELETE, we need to get insert_id/affected_rows before closing
                $insertId = $conn->insert_id;
                $affectedRows = $conn->affected_rows;
                $stmt->close();

                // Return a custom object with the information we need
                $result = new stdClass();
                $result->insert_id = $insertId;
                $result->affected_rows = $affectedRows;
                $result->success = true;
            }
        }

        // Close the connection since we create a new one each time
        $conn->close();
        return $result;

    } catch (Exception $e) {
        // Close connection on error
        if (isset($conn) && $conn) {
            $conn->close();
        }

        if (DEVELOPMENT_MODE) {
            throw new Exception("Query error: " . $e->getMessage() . "\nQuery: " . $query);
        } else {
            error_log("Query error: " . $e->getMessage() . " | Query: " . $query);
            return false;
        }
    }
}

/**
 * Sanitize Input
 */
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Get User by ID with Error Handling
 */
function getUserById($userId) {
    try {
        $result = executeQuery(
            "SELECT * FROM users WHERE id = ?",
            'i',
            [$userId]
        );
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        }
        
        return null;
        
    } catch (Exception $e) {
        error_log("getUserById error: " . $e->getMessage());
        return null;
    }
}

// =============================================================================
// SESSION MANAGEMENT
// =============================================================================

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Authentication Helper Functions
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

function getCurrentUser() {
    if (isLoggedIn()) {
        return getUserById($_SESSION['user_id']);
    }
    return null;
}

/**
 * Admin Authentication Functions
 */
function isAdmin() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: admin/login.php');
        exit;
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Log Error Function
 */
function logError($message) {
    $logFile = __DIR__ . '/../logs/error.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, $logFile);
}

/**
 * Time Ago Function
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

/**
 * Validate Email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate Referral Code
 */
function generateReferralCode($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

/**
 * Format Money
 */
function formatMoney($amount) {
    return '₦' . number_format($amount, 2);
}

/**
 * Upload File Function
 */
function uploadFile($file, $directory, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'webm']) {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    $uploadDir = UPLOAD_PATH . $directory . '/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $fileName = time() . '_' . uniqid() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    $filePath = $uploadDir . $fileName;

    $fileType = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($fileType, $allowedTypes)) {
        return false;
    }

    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return $directory . '/' . $fileName;
    }

    return false;
}

/**
 * Log Admin Action
 */
function logAdminAction($adminUsername, $action, $targetUserId = null, $details = '') {
    try {
        $conn = getConnection();
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        $stmt = $conn->prepare("
            INSERT INTO admin_logs (admin_username, action, target_user_id, details, ip_address, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->bind_param("ssiss", $adminUsername, $action, $targetUserId, $details, $ipAddress);
        $stmt->execute();
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        logError("Failed to log admin action: " . $e->getMessage());
    }
}

// =============================================================================
// CREATE REQUIRED DIRECTORIES
// =============================================================================

$uploadDir = __DIR__ . '/../uploads/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
    mkdir($uploadDir . 'profiles/', 0755, true);
    mkdir($uploadDir . 'recordings/', 0755, true);
    mkdir($uploadDir . 'screen-recordings/', 0755, true);
    mkdir($uploadDir . 'mirrors/', 0755, true);
    mkdir($uploadDir . 'posts/', 0755, true);
    mkdir($uploadDir . 'thumbnails/', 0755, true);
}
