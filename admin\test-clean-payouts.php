<?php
/**
 * Test the clean payouts functionality
 */
require_once '../config/db_config.php';

// Check if admin is logged in
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Clean Payouts Test - Universal Reciters</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background: linear-gradient(135deg, #1a5f3f 0%, #2d8f5f 100%); min-height: 100vh; }
        .test-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <div class='container py-5'>
        <div class='row justify-content-center'>
            <div class='col-md-10'>
                <div class='test-card p-4'>
                    <h2 class='text-center mb-4'>Clean Payouts Test</h2>";

// Test 1: Check if clean payouts file exists
echo "<div class='alert alert-info'>";
echo "<h5>Test 1: File Existence</h5>";
if (file_exists('payouts-clean.php')) {
    echo "✅ payouts-clean.php exists<br>";
} else {
    echo "❌ payouts-clean.php not found<br>";
}

if (file_exists('payouts.php')) {
    echo "✅ payouts.php exists<br>";
} else {
    echo "❌ payouts.php not found<br>";
}
echo "</div>";

// Test 2: Database connection
echo "<div class='alert alert-info'>";
echo "<h5>Test 2: Database Connection</h5>";
try {
    $conn = getConnection();
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Check for pending withdrawals
        $result = $conn->query("SELECT COUNT(*) as count FROM withdrawal_requests WHERE status = 'pending'");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "✅ Found $count pending withdrawal requests<br>";
        }
        
        $conn->close();
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Include test
echo "<div class='alert alert-info'>";
echo "<h5>Test 3: Include Test</h5>";
try {
    // Test if we can include the clean payouts file without errors
    ob_start();
    $old_get = $_GET;
    $old_post = $_POST;
    $_GET = ['status' => 'pending'];
    $_POST = [];
    
    // Capture any output
    include 'payouts-clean.php';
    $output = ob_get_clean();
    
    // Restore original values
    $_GET = $old_get;
    $_POST = $old_post;
    
    if (strlen($output) > 0) {
        echo "✅ Clean payouts file included successfully<br>";
        echo "✅ Generated " . strlen($output) . " bytes of output<br>";
    } else {
        echo "⚠️ Clean payouts file included but no output generated<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 4: Function test
echo "<div class='alert alert-info'>";
echo "<h5>Test 4: Function Test</h5>";

// Define the function for testing
function processWithdrawalRequest($requestId, $action, $adminNotes) {
    try {
        $processingConn = getConnection();
        if (!$processingConn) {
            throw new Exception("Database connection failed");
        }

        // Get withdrawal request details
        $requestStmt = $processingConn->prepare("
            SELECT wr.*, u.full_name, u.email, u.wallet_balance
            FROM withdrawal_requests wr 
            JOIN users u ON wr.user_id = u.id 
            WHERE wr.id = ? AND wr.status = 'pending'
        ");
        $requestStmt->bind_param("i", $requestId);
        $requestStmt->execute();
        $result = $requestStmt->get_result();
        $request = $result->fetch_assoc();
        $requestStmt->close();

        if ($request) {
            $processingConn->close();
            return "TEST: Would process withdrawal request #{$requestId} for user {$request['full_name']} - Amount: ₦{$request['amount']}";
        } else {
            $processingConn->close();
            return 'TEST: Withdrawal request not found or already processed.';
        }
        
    } catch (Exception $e) {
        return 'TEST: Error - ' . $e->getMessage();
    }
}

// Test the function with a real pending withdrawal
try {
    $conn = getConnection();
    $result = $conn->query("SELECT id FROM withdrawal_requests WHERE status = 'pending' LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $testResult = processWithdrawalRequest($row['id'], 'approve', 'Test from clean payouts test');
        echo "✅ Function test result: " . $testResult . "<br>";
    } else {
        echo "⚠️ No pending withdrawals found for function testing<br>";
    }
    $conn->close();
} catch (Exception $e) {
    echo "❌ Function test error: " . $e->getMessage() . "<br>";
}
echo "</div>";

echo "                <div class='text-center mt-4'>
                        <a href='payouts-clean.php' class='btn btn-success btn-lg me-2'>
                            <i class='fas fa-money-bill-wave me-2'></i>Test Clean Payouts Page
                        </a>
                        <a href='payouts.php' class='btn btn-warning btn-lg me-2'>
                            <i class='fas fa-money-bill-wave me-2'></i>Test Original Payouts Page
                        </a>
                        <a href='dashboard.php' class='btn btn-secondary btn-lg'>
                            <i class='fas fa-tachometer-alt me-2'></i>Dashboard
                        </a>
                    </div>
                    
                    <div class='mt-4'>
                        <h6>Recommendation:</h6>
                        <p class='small'>If the clean payouts page works without errors, you can replace the original payouts.php with payouts-clean.php</p>
                        <div class='alert alert-warning'>
                            <strong>To replace:</strong><br>
                            1. Backup original: <code>mv payouts.php payouts-backup.php</code><br>
                            2. Use clean version: <code>mv payouts-clean.php payouts.php</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
