<?php
/**
 * Create Sample Forum Replies for Testing
 */

require_once 'config/db_config.php';

$conn = getConnection();

if (!$conn) {
    die("Database connection failed");
}

// Sample forum replies
$sampleReplies = [
    [
        'post_id' => 1,
        'user_id' => 1,
        'content' => '<PERSON><PERSON><PERSON>u alaiku<PERSON>! Thank you for creating this wonderful community. I\'m excited to learn and share with everyone here.'
    ],
    [
        'post_id' => 1,
        'user_id' => 1,
        'content' => 'This is exactly what I was looking for! A place to discuss Qur\'an recitation with fellow Muslims.'
    ],
    [
        'post_id' => 2,
        'user_id' => 1,
        'content' => 'Great tips! I would also add that practicing with a teacher or mentor can make a huge difference in improving your recitation.'
    ],
    [
        'post_id' => 2,
        'user_id' => 1,
        'content' => 'I agree with all these points. Recording yourself is especially helpful - you can hear things you don\'t notice while reciting.'
    ],
    [
        'post_id' => 3,
        'user_id' => 1,
        'content' => 'Excellent explanation of ghunnah! This is one of the most important tajweed rules that many people struggle with.'
    ],
    [
        'post_id' => 4,
        'user_id' => 1,
        'content' => 'I started with <PERSON><PERSON><PERSON><PERSON><PERSON> and Al-Ikhlas too! It\'s a great way to begin the memorization journey.'
    ],
    [
        'post_id' => 5,
        'user_id' => 1,
        'content' => 'I find early morning (before Fajr) to be the best time for practice. The mind is fresh and there are fewer distractions.'
    ]
];

echo "Creating sample forum replies...\n";

try {
    $stmt = $conn->prepare("INSERT INTO forum_replies (post_id, user_id, content, created_at) VALUES (?, ?, ?, NOW())");
    
    foreach ($sampleReplies as $reply) {
        $stmt->bind_param("iis", $reply['post_id'], $reply['user_id'], $reply['content']);
        
        if ($stmt->execute()) {
            echo "✅ Created reply for post " . $reply['post_id'] . "\n";
        } else {
            echo "❌ Failed to create reply for post " . $reply['post_id'] . "\n";
        }
    }
    
    $stmt->close();
    
    echo "\n🎉 Sample forum replies created successfully!\n";
    echo "You can now test the reply functionality in the community forum.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
} finally {
    $conn->close();
}
?> 